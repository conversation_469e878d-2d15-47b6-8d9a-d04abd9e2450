import Foundation
import UIKit
import Metal
import MetalKit

/// Vertical position options for text placement
enum VerticalPosition: Int, CaseIterable {
    case top = 0
    case middle = 1
    case bottom = 2

    var description: String {
        switch self {
        case .top: return "上"
        case .middle: return "中"
        case .bottom: return "下"
        }
    }

    /// Convert to HomeMatPositionInfo vertical value
    var homeMatValue: Int {
        return self.rawValue
    }
}

/// Horizontal alignment options for text placement
enum HorizontalAlignment: Int, CaseIterable {
    case left = 0
    case center = 1
    case right = 2

    var description: String {
        switch self {
        case .left: return "左"
        case .center: return "中"
        case .right: return "右"
        }
    }

    /// Convert to HomeMatPositionInfo horizontal value
    var homeMatValue: Int {
        switch self {
        case .left: return 0
        case .center: return 1
        case .right: return 2
        }
    }

    /// Convert to NSTextAlignment
    var textAlignment: NSTextAlignment {
        switch self {
        case .left: return .left
        case .center: return .center
        case .right: return .right
        }
    }
}

/// Manages text positioning on mats
class TextPositionManager {
    var verticalPosition: VerticalPosition = .middle
    var horizontalAlignment: HorizontalAlignment = .center

    /// Get the position identifier in the format used by HomeMatConfig (e.g., "A-2", "B-1", "C-3")
    var positionIdentifier: String {
        let verticalLetter: String
        switch verticalPosition {
        case .top: verticalLetter = "A"
        case .middle: verticalLetter = "B"
        case .bottom: verticalLetter = "C"
        }

        let horizontalNumber: String
        switch horizontalAlignment {
        case .left: horizontalNumber = "1"
        case .center: horizontalNumber = "2"
        case .right: horizontalNumber = "3"
        }

        return "\(verticalLetter)-\(horizontalNumber)"
    }

    /// Create a HomeMatPositionInfo object for the current position settings
    func createPositionInfo() -> HomeMatPositionInfo {
        return HomeMatPositionInfo(
            labelEnable: true,
            labelPosition: positionIdentifier,
            x: 0,
            y: 0,
            width: 100,
            height: 100,
            vertical: verticalPosition.homeMatValue,
            horizontal: horizontalAlignment.homeMatValue
        )
    }
    
    func reset() {
        verticalPosition = .middle
        horizontalAlignment = .center
    }
}

/// Extension to add position calculation methods to HomeMatPositionInfo
extension HomeMatPositionInfo {
    /// Calculate the world position for text based on mat bounds and text size
    func calculateWorldPosition(matWorldBounds: (minX: Float, minY: Float, maxX: Float, maxY: Float), textNormalizedSize: (width: Float, height: Float)) -> SIMD2<Float> {
        let matWidth = matWorldBounds.maxX - matWorldBounds.minX
        let matHeight = matWorldBounds.maxY - matWorldBounds.minY
        let matCenterX = (matWorldBounds.minX + matWorldBounds.maxX) / 2
        let matCenterY = (matWorldBounds.minY + matWorldBounds.maxY) / 2

        // Calculate position based on vertical alignment
        var posY: Float

        //テキストの高さの半分、テキストの中心から端までの距離を計算するために使用
        let halfTextHeight = textNormalizedSize.height / 2.0

        // テキストの内容とテキスト枠の境界線との距離を表す
        // ここで、テキスト枠内に非常に小さな余白を想定して、テキストが枠の境界線にほぼ貼り付いているようにする
        let textInnerPadding = textNormalizedSize.height * 0.01

        switch vertical {
        case 0: // top - 上端にぴったりと合わせる
            // テキストの上端を背景の上端にぴったりと合わせる
            // より積極的な方法で、テキストを完全に上端に合わせる
            posY = matWorldBounds.maxY - halfTextHeight * 0.8
        case 1: // middle
            posY = matCenterY
        case 2: // bottom - 正確に底辺に貼り付ける
            // テキストの下端を背景画像の下端に正確に合わせる
            // より積極的な方法で、テキストを完全に底に貼り付ける
            posY = matWorldBounds.minY + halfTextHeight * 0.8
        default:
            posY = matCenterY
        }

        // Calculate position based on horizontal alignment
        var posX: Float

        // テキスト幅の半分。テキストの中心から端までの距離を計算するために使用されます。
        let halfTextWidth = textNormalizedSize.width / 2.0

        // テキストの内側の余白。この値は、テキストコンテンツとテキストボックスの縁との距離を示します。
        // 文本枠内に非常に小さな余白を想定して、テキストが枠の境界線にほぼ貼り付いているようにする
        let textHorizontalInnerPadding = textNormalizedSize.width * 0.01

        switch horizontal {
        case 0: // left - 精密に左端に揃える
            // テキストの左端を背景画像の左端に精密に揃える
            // より積極的な方法で、テキストを完全に左側に揃える
            posX = matWorldBounds.minX + halfTextWidth * 0.8
        case 1: // center
            posX = matCenterX
        case 2: // right - 精密に右端に揃える
            // テキストの右端を背景画像の右端に精密に揃える
            // より積極的な方法で、テキストを完全に右側に揃える
            posX = matWorldBounds.maxX - halfTextWidth * 0.8
        default:
            posX = matCenterX
        }

        return SIMD2<Float>(posX, posY)
    }
}