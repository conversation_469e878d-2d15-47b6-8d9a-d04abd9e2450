//
//  Renderer.swift
//  MatSimulator
//
//  Created by Clamour on 2025/02/09.
//

import Metal
import MetalKit

class Renderer: NSObject, MTKViewDelegate {
    var device: MTLDevice!
    var commandQueue: MTLCommandQueue!
    var pipelineState: MTLRenderPipelineState!
    var vertexBuffer: MTLBuffer!
    var indexBuffer: MTLBuffer!
    var currentSquare: Square?
    var squares: [Square] = []
    var metalView: MTKView!
    var samplerState: MTLSamplerState!
    var textureCache = [String: MTLTexture]()
    var currentSquareIndex: Int?
    var currentImage: UIImage?
    var originalTexture: MTLTexture?
    var currentRenderCommandEncoder: MTLRenderCommandEncoder?
    var renderPipelineState: MTLRenderPipelineState!
    private let gridSize = 10
    var indices: [UInt16] = []

    enum EraserMode {
        case circle
        case rectangle
    }

    enum EraserSize {
        case small
        case large

        var radius: Float {
            switch self {
            case .small: return 10.0
            case .large: return 20.0
            }
        }
    }

    enum ScaleOption {
        case large
        case medium
        case small
        case tiny
//        case custom(Float)
//
//        var scaleFactor: Float {
//            switch self {
//            case .large: return 0.9
//            case .medium: return 0.6
//            case .small: return 0.4
//            case .custom(let value): return value
//            }
//        }
    }

    private var eraserMode: EraserMode = .circle
    public var eraserSize: Float = 20.0
    // private var erasedPixels: [(square: Square, region: MTLRegion, pixels: [UInt8], position: CGPoint)] = []
    private var erasedPixels: [ErasedData] = []

    private var lastErasePosition: CGPoint?
    private var lastRestorePosition: CGPoint?

    init(metalView: MTKView) {
        super.init()

        metalView.clearColor = MTLClearColorMake(0, 0, 0, 0) // 背景色を設定
        self.metalView = metalView

        device = metalView.device
        commandQueue = device.makeCommandQueue() // コマンドキューを作成

        setupPipelineState() // パイプラインステートを設定
        setupVertexBuffer()  // 頂点バッファを設定

        createSamplerState() // サンプラーステートを作成
    }

    // パイプラインステートの設定
    func setupPipelineState() {
        guard let library = device.makeDefaultLibrary() else {
            fatalError("Metal library not found.")
        }

        let vertexDescriptor = MTLVertexDescriptor()

        vertexDescriptor.attributes[0].format = .float4
        vertexDescriptor.attributes[0].offset = 0
        vertexDescriptor.attributes[0].bufferIndex = 0

        vertexDescriptor.attributes[1].format = .float2
        vertexDescriptor.attributes[1].offset = MemoryLayout<Float>.size * 4
        vertexDescriptor.attributes[1].bufferIndex = 0

        vertexDescriptor.layouts[0].stride = MemoryLayout<Float>.size * 6
        vertexDescriptor.layouts[0].stepRate = 1
        vertexDescriptor.layouts[0].stepFunction = .perVertex

        let pipelineDescriptor = MTLRenderPipelineDescriptor()
        pipelineDescriptor.vertexDescriptor = vertexDescriptor

        pipelineDescriptor.vertexFunction = library.makeFunction(name: "vertex_shader")
        pipelineDescriptor.fragmentFunction = library.makeFunction(name: "fragment_shader")
        pipelineDescriptor.colorAttachments[0].pixelFormat = .bgra8Unorm

        pipelineDescriptor.colorAttachments[0].isBlendingEnabled = true
        pipelineDescriptor.colorAttachments[0].rgbBlendOperation = .add
        pipelineDescriptor.colorAttachments[0].alphaBlendOperation = .add
        pipelineDescriptor.colorAttachments[0].sourceRGBBlendFactor = .sourceAlpha
        pipelineDescriptor.colorAttachments[0].sourceAlphaBlendFactor = .sourceAlpha
        pipelineDescriptor.colorAttachments[0].destinationRGBBlendFactor = .oneMinusSourceAlpha
        pipelineDescriptor.colorAttachments[0].destinationAlphaBlendFactor = .oneMinusSourceAlpha

        do {
            pipelineState = try device.makeRenderPipelineState(descriptor: pipelineDescriptor)
        } catch {
            print("Failed to create pipeline state: \(error)")
        }
    }

    // 頂点バッファの設定
    func setupVertexBuffer() {
        var vertices: [Float] = []
        indices = []

        for i in 0...gridSize {
            for j in 0...gridSize {
                let x = -0.5 + Float(j) / Float(gridSize)
                let y = -0.5 + Float(i) / Float(gridSize)
                let u = Float(j) / Float(gridSize)
                let v = 1.0 - Float(i) / Float(gridSize)
                vertices.append(contentsOf: [x, y, 0, 1, u, v])
            }
        }

        for i in 0..<gridSize {
            for j in 0..<gridSize {
                let topLeft = UInt16(i * (gridSize + 1) + j)
                let topRight = UInt16(topLeft + 1)
                let bottomLeft = UInt16((i + 1) * (gridSize + 1) + j)
                let bottomRight = UInt16(bottomLeft + 1)

                indices.append(contentsOf: [topLeft, topRight, bottomRight])
                indices.append(contentsOf: [topLeft, bottomRight, bottomLeft])
            }
        }

        vertexBuffer = device.makeBuffer(
            bytes: vertices,
            length: vertices.count * MemoryLayout<Float>.size,
            options: []
        )

        indexBuffer = device.makeBuffer(
            bytes: indices,
            length: indices.count * MemoryLayout<UInt16>.size,
            options: []
        )
    }

    func addSquare(imageName: String, squareType: Int = 0, matType: String = "", isEditMode: Bool = false, matInfo: MatInfo? = nil) -> Bool {
        print("aqaz R addSquare - imageName: \(imageName), squareType: \(squareType), matType: \(matType), isEditMode: \(isEditMode)")
        print("aqaz R addSquare - matInfo: \(matInfo)")

        if let homeConfig = matInfo?.homeMatConfig {
            print("aqaz R addSquare - homeMatConfig exists, positionList count: \(homeConfig.positionList.count)")
            print("aqaz R addSquare - defaultLabelPosition: \(homeConfig.defaultLabelPosition)")
            print("aqaz R addSquare - textConfig.hasText: \(homeConfig.textConfig.hasText)")

            // デフォルト位置の情報を取得して表示
            if let defaultInfo = homeConfig.defaultPositionInfo {
                print("aqaz R addSquare - defaultPositionInfo found: \(defaultInfo.labelPosition), enabled: \(defaultInfo.labelEnable)")
            } else {
                print("aqaz R addSquare - defaultPositionInfo not found for \(homeConfig.defaultLabelPosition)")
            }
        }

        if squareType == 0 && !isEditMode && currentSquare != nil && currentSquare!.squareType == 0 && !currentSquare!.isEditMode {
            let existingSquare = currentSquare!
            let existingPosition = existingSquare.position
            let existingScale = existingSquare.scale
            let existingRotation = existingSquare.rotation
            let existingCornerDisplacements = existingSquare.cornerDisplacements

            guard let image = UIImage(contentsOfFile: imageName) else {
                print("Unable to load image: \(imageName)")
                return false
            }

            guard let texture = imageToTexture(image: image, device: device) else {
                print("Unable to create texture")
                return false
            }

            existingSquare.texture = texture
            existingSquare.texturePath = imageName
            existingSquare.matType = matType
            existingSquare.matInfo = matInfo
            print("aqaz R addSquare - updated existing square with matInfo: \(matInfo)")
            existingSquare.originalImage = image
            
            existingSquare.position = existingPosition
            existingSquare.scale = existingScale
            existingSquare.rotation = existingRotation
            existingSquare.cornerDisplacements = existingCornerDisplacements

            currentImage = image
            originalTexture = createTextureCopy(from: texture)

            return true
        }

        guard let image = UIImage(contentsOfFile: imageName),
              let texture = imageToTexture(image: image, device: device) else {
            print(" Failed to load image or create texture from \(imageName)")
            return false
        }

        let square = Square()
        square.texture = texture
        square.texturePath = imageName
        square.squareType = squareType
        square.matType = matType
        square.isEditMode = isEditMode
        square.size = SIMD2<Float>(Float(image.size.width), Float(image.size.height))
        square.drawableSize = metalView.drawableSize
        square.originalAspectRatio = Float(image.size.width / image.size.height)
        square.matInfo = matInfo
        square.originalImage = image

        originalTexture = createTextureCopy(from: texture)

        print("aqaz addSquare isEditMode:\(isEditMode)")

        if squareType == 0 {
            if isEditMode {
                if let index = squares.firstIndex(where: { $0.squareType == 0 && $0.isEditMode }) {
                    squares[index] = square
                    currentSquareIndex = index
                } else {
                    squares.append(square)
                    currentSquareIndex = squares.count - 1
                }
            } else {
                if let index = squares.firstIndex(where: { $0.squareType == 0 && !$0.isEditMode }) {
                    squares[index] = square
                    currentSquareIndex = index
                } else {
                    squares.append(square)
                    currentSquareIndex = squares.count - 1
                }
            }
        } else {
            //updateSquareScaleToCustomSize(option: .tiny)
//            updateLabelPosition(for: squares.first!,
//                                label: square,
//                                h: HorizontalAlignment.left,
//                                v: VerticalPosition.bottom)
            squares.append(square)
            currentSquareIndex = squares.count - 1
            currentSquare = square
            // 再に、currentSquare(=文字) のみを tiny サイズに変更する
            //    updateSquareScaleToCustomSize(option: .tiny)

            // 最後に、文字の位置を mat の位置に合わせて計算する
            if let mat = squares.first {        // デフォルトで最初のものが mat
                updateLabelPosition(for: mat,
                                    label: square,
                                    h: .left,
                                    v: .bottom)
            }
        }

        currentSquare = square
        currentImage = image

        if squareType == 3 {
            // 文本の場合、tiny サイズを初期サイズとして使用する
            updateSquareScaleToCustomSize(option: .tiny)
            print("aqaz R addSquare - Applied tiny scale to new text square.")
        } else {
            // 他のタイプの場合、調整や異なる初期サイズは必要ない
            square.updateTransformMatrix(preserveScale: true)
        }

        return true
    }
    
    // Renderer.swift
    func updateLabelPosition_Relative(for mat: Square,
                                      label: Square,
                                      h: HorizontalAlignment,
                                      v: VerticalPosition,
                                      marginPx: Float = 0) {

        // ——— ① Mat と Label の中心と半寸 (scale) ———
        let matCenterX = mat.position.x
        let matCenterY = mat.position.y
        let matHalfW = mat.scale.x * 0.5
        let matHalfH = mat.scale.y * 0.5

        let labelHalfW = label.scale.x * 0.5
        let labelHalfH = label.scale.y * 0.5

        // ——— ② 辺距 px ⇢ NDC ———
        let px2ndcX = 2.0 / Float(metalView.bounds.width)
        let px2ndcY = 2.0 / Float(metalView.bounds.height)
        let marginX = marginPx * px2ndcX
        let marginY = marginPx * px2ndcY

        // ——— ③ Label の目標中心位置 (Mat の中心と相対寸) ———
        var targetX: Float
        var targetY: Float

        switch h {
        case .left:   targetX = matCenterX - matHalfW + labelHalfW + marginX 
        // Mat の中心 X - Mat の半幅 + Label の半幅 + 余白
        case .center: targetX = matCenterX                                  
        // Mat の中心 X と整列
        case .right:  targetX = matCenterX + matHalfW - labelHalfW - marginX // Mat の中心 X + Mat の半幅 - Label の半幅 - 余白
        }
        switch v {
        case .top:    targetY = matCenterY + matHalfH - labelHalfH - marginY // Mat の中心 Y + Mat の半高 - Label の半高 - 余白
        case .middle: targetY = matCenterY   // Mat の中心 X と整列
        case .bottom: targetY = matCenterY - matHalfH + labelHalfH + marginY // Mat の中心 Y - Mat の半高 + Label の半高 + 余白
        }

        // ラベルの位置を更新する
        label.position = SIMD2<Float>(targetX, targetY)
        label.updateTransformMatrix(preserveScale: true)

        // ——— ④ DEBUG ——————————————————————————————————————————
        let tag = "🔵 ULPos(Relative)"
        print(String(format:
            "\(tag) h=%@ v=%@ | matPos(%.3f,%.3f) matScale(%.3f,%.3f) | lblScale(%.3f,%.3f) | margin(%.4f,%.4f) | NEW LblPos(%.4f,%.4f)",
            "\(h)", "\(v)",
            mat.position.x, mat.position.y, mat.scale.x, mat.scale.y,
            label.scale.x, label.scale.y,
            marginX, marginY,
            label.position.x, label.position.y))
    }
    
    // mat の世界包围盒に合わせて配置する
    

    // MARK: - 位置合わせ
     func updateLabelPosition(for mat: Square,
                                     label: Square,
                                     h: HorizontalAlignment,
                                     v: VerticalPosition,
                                     marginPx: Float = 0) {

        // 実際の世界範囲を取得する
        guard let b = mat.calculateWorldNormalizedBounds(viewSize: metalView.bounds.size) else { return }
        let matHalfW = (b.maxX - b.minX) * 0.5
        let matHalfH = (b.maxY - b.minY) * 0.5

        // ラベルの半幅と半高
        let labelNormSize = label.calculateNormalizedSize(viewSize: metalView.bounds.size) // ラベルの半幅と半高
        let labelHalfW = label.scale.x * 0.5 // scale の半分を NDC 半幅として使用
        let labelHalfH = label.scale.y * 0.5 // scale の半分を NDC 半高として使用

        // ——— ③ 10 px ⇢ NDC ———
        let px2ndcX = 2.0 / Float(metalView.bounds.width)
        let px2ndcY = 2.0 / Float(metalView.bounds.height)
        let marginX = marginPx * px2ndcX
        let marginY = marginPx * px2ndcY
         
        var targetX: Float
        var targetY: Float

        // ——— ④ 計算位置 ———
        switch h {
        case .left:   targetX = b.minX + labelHalfW + marginX // パディング左 + ラベル半分の幅 + マージン
        case .center: targetX = (b.minX + b.maxX) * 0.5     // パッドの中心 X
        case .right:  targetX = b.maxX - labelHalfW - marginX // パディング右 + ラベル半分の幅 + マージン
        }
        switch v {
        case .top:    targetY = b.maxY - labelHalfH - marginY // パディング上 + ラベル半分の高さ + マージン
        case .middle: targetY = (b.minY + b.maxY) * 0.5     // パッドの中心 Y
        case .bottom: targetY = b.minY + labelHalfH + marginY // パディング下 + ラベル半分の高さ + マージン
        }
         
        // ラベルの位置を更新する
        label.position = SIMD2<Float>(targetX, targetY)

        label.updateTransformMatrix(preserveScale: true)
         
         // ——— ⑤ DEBUG ——————————————————————————————————————————
             // MetalView サイズ（Float、使い勝手の良い連続使用）
             let viewW = Float(metalView.bounds.width)
             let viewH = Float(metalView.bounds.height)

             // 貼图の本体のピクセル寸
             print(String(format:"📏 MatTex  W=%dpx H=%dpx  |  LblPNG W=%dpx H=%dpx",
                          mat.texture?.width  ?? 0,
                          mat.texture?.height ?? 0,
                          label.texture?.width  ?? 0,
                          label.texture?.height ?? 0))

             // 現在の scale で実際に描画される「半幅半高」（ピクセル）
             let matHalfWpx = matHalfW * viewW * 0.5          // matHalfW は NDC
             let matHalfHpx = matHalfH * viewH * 0.5
             let lblHalfWpx = labelHalfW * viewW * 0.5        // labelHalfW 同じ
             let lblHalfHpx = labelHalfH * viewH * 0.5

             // 計算された「中心」座標
             let matCX = (mat.position.x + 1) * viewW * 0.5
             let matCY = (1 - mat.position.y) * viewH * 0.5
             let lblCX = (label.position.x + 1) * viewW * 0.5
             let lblCY = (1 - label.position.y) * viewH * 0.5

             print(String(format:"🖼  MatDraw  half(%.1f,%.1f)px  → center=(%.1f,%.1f)",
                          matHalfWpx, matHalfHpx, matCX, matCY))

             print(String(format:"✏️  LblDraw  half(%.1f,%.1f)px  → center=(%.1f,%.1f)",
                          lblHalfWpx, lblHalfHpx, lblCX, lblCY))

        // ——— ⑤ DEBUG ———
         print(String(format:
                 "🔵 ULPos h=%@ v=%@ | matBounds(%.3f,%.3f)-(%.3f,%.3f) | lblNormSize(%.3f,%.3f) | lblScale(%.3f,%.3f) | margin(%.4f,%.4f) | NEW LblPos(%.4f,%.4f)",
                 "\(h)", "\(v)",
                 b.minX, b.minY, b.maxX, b.maxY,
                 labelNormSize.x, labelNormSize.y,
                 label.scale.x, label.scale.y,
                 marginX, marginY,
                 label.position.x, label.position.y))
         
        
    }




    func getMatSquare() -> Square{
        if let matSquare = squares.first(where: { !$0.isHidden && $0.squareType == 0 }) {
            return matSquare
        }
        return currentSquare!
    }

    func updateSquareScaleToCustomSize(option: ScaleOption = .medium, additionalScale: Float = 1.0) {
        guard let square = currentSquare, let view = metalView else { return }

        let viewSize = view.bounds.size
        let textureSize = CGSize(width: CGFloat(square.size.x), height: CGFloat(square.size.y))

        let textureAspect = textureSize.width / textureSize.height

        var scaleFactor: CGFloat
        switch option {
        case .large:
            scaleFactor = 1  // 100%
        case .medium:
            scaleFactor = 0.6  // 60%
        case .small:
            scaleFactor = 0.3  // 30%
        case .tiny:
            let scale = (viewSize.height * 0.1) / textureSize.height
            let finalWidth = textureSize.width * scale
            let finalHeight = textureSize.height * scale

            let metalScaleX = Float(scale) * Float(textureSize.width) / Float(viewSize.width) * 2.0
            let metalScaleY = Float(scale) * Float(textureSize.height) / Float(viewSize.height) * 2.0

            square.scale = SIMD2<Float>(metalScaleX * additionalScale, metalScaleY * additionalScale)
            square.position = .zero
            square.updateTransformMatrix(preserveScale: true)
            metalView.setNeedsDisplay()
            return
        }

        let maxViewWidth = viewSize.width * scaleFactor
        let maxViewHeight = viewSize.height * scaleFactor

        var scale: CGFloat
        if textureAspect > 1 {
            scale = maxViewWidth / textureSize.width
        } else {
            scale = maxViewHeight / textureSize.height
        }

        let finalWidth = textureSize.width * scale
        let finalHeight = textureSize.height * scale

        let metalScaleX = Float(scale) * Float(textureSize.width) / Float(viewSize.width) * 2.0
        let metalScaleY = Float(scale) * Float(textureSize.height) / Float(viewSize.height) * 2.0

        square.scale = SIMD2<Float>(metalScaleX * additionalScale, metalScaleY * additionalScale)
        square.position = .zero
        square.updateTransformMatrix(preserveScale: true)
        
        // 垫子を縮尺する場合は、それに掛かるラベルも同時に移動する
        if square.squareType == 0 {
            for label in squares where label.squareType == 3 {
                updateLabelPosition(for: square,
                                    label: label,
                                    h: .left,
                                    v: .bottom)
            }
        }
        
        metalView.setNeedsDisplay()
    }

    // 四角形の位置を更新（移動量指定）
    func updateSquarePosition(translation: CGPoint) {
        guard let square = currentSquare else { return }
        square.position.x += Float(translation.x)
        square.position.y += Float(translation.y)
        
        square.updateTransformMatrix(preserveScale: true)
    }

    // 四角形の位置を更新（位置指定）
    func updateSquarePosition(location: SIMD2<Float>) {
        guard let square = currentSquare else { return }
        square.position = location
        
        square.updateTransformMatrix(preserveScale: true)  
    }

    // 四角形の回転を更新
    func updateSquareRotation(rotation: CGFloat) {
        guard let square = currentSquare else { return }
        square.rotation = Float(rotation)
    }

    func mtkView(_ view: MTKView, drawableSizeWillChange size: CGSize) {
        squares.forEach { square in
            square.drawableSize = size
        }
    }

    func draw(in view: MTKView) {
        guard let drawable = view.currentDrawable,
              let renderPassDescriptor = view.currentRenderPassDescriptor else { return }

        guard let commandBuffer = commandQueue.makeCommandBuffer(),
              let renderEncoder = commandBuffer.makeRenderCommandEncoder(descriptor: renderPassDescriptor)
        else {
            return
        }

        currentRenderCommandEncoder = renderEncoder

        renderEncoder.setRenderPipelineState(pipelineState)
        renderEncoder.setFragmentSamplerState(samplerState, index: 0)

        renderEncoder.setVertexBuffer(vertexBuffer, offset: 0, index: 0)

        for square in squares {
            if square.isHidden {
                continue
            }
            if let texture = square.texture {
                var modelMatrix = square.transform.matrix
                renderEncoder.setVertexBytes(&modelMatrix,
                                           length: MemoryLayout<float4x4>.size,
                                           index: 1)

                var displacements = square.cornerDisplacements
                renderEncoder.setVertexBytes(&displacements,
                                           length: MemoryLayout<SIMD2<Float>>.size * 4,
                                           index: 2)

                renderEncoder.setFragmentTexture(texture, index: 0)

                var color = square.color
                renderEncoder.setFragmentBytes(&color,
                                             length: MemoryLayout<SIMD4<Float>>.size,
                                             index: 1)

                renderEncoder.drawIndexedPrimitives(type: .triangle,
                                           indexCount: 6 * gridSize * gridSize,
                                           indexType: .uint16,
                                           indexBuffer: indexBuffer,
                                           indexBufferOffset: 0)
            }
        }

        renderEncoder.endEncoding()
        commandBuffer.present(drawable)
        commandBuffer.commit()

        currentRenderCommandEncoder = nil
    }

    // サンプラーステートの作成
    func createSamplerState() {
        let descriptor = MTLSamplerDescriptor()
        descriptor.minFilter = .linear
        descriptor.magFilter = .linear
        descriptor.mipFilter = .linear
        samplerState = device.makeSamplerState(descriptor: descriptor)
    }

    // 現在の四角形をリセット
    func resetCurrentSquare() {
        guard let square = currentSquare else { return }
        square.position = SIMD2<Float>(0, 0)
        square.rotation = 0
        square.scale = SIMD2<Float>(repeating: 1.0)
        square.cornerDisplacements = [
            SIMD2<Float>(0, 0),
            SIMD2<Float>(0, 0),
            SIMD2<Float>(0, 0),
            SIMD2<Float>(0, 0)
        ]
    }

    func setEraserSize(size: EraserSize) {
        eraserSize = size.radius
    }

    func setEraserMode(mode: EraserMode) {
        eraserMode = mode
    }

    func restoreErased() {
        guard let lastErased = erasedPixels.popLast() else { return }

        if let texture = lastErased.square.texture {
            texture.replace(
                region: lastErased.region,
                mipmapLevel: 0,
                withBytes: lastErased.pixels,
                bytesPerRow: lastErased.region.size.width * 4
            )
            metalView.setNeedsDisplay()
        }
    }


    func resetErasePosition() {
        lastErasePosition = nil
    }

    func eraseAtLocation(_ location: CGPoint) {
        guard let square = currentSquare, let texture = square.texture else { return }

        let viewSize = metalView.bounds.size
        let clipX = Float(location.x / viewSize.width) * 2 - 1
        let clipY = 1 - Float(location.y / viewSize.height) * 2

        if let lastPosition = lastErasePosition {
            let distance = hypot(location.x - lastPosition.x, location.y - lastPosition.y)
            let steps = Int(distance / CGFloat(eraserSize / 3.0))
            if steps > 1 {
                for i in 1...steps {
                    let t = Float(i) / Float(steps)
                    let interpolatedX = lastPosition.x + CGFloat(t) * (location.x - lastPosition.x)
                    let interpolatedY = lastPosition.y + CGFloat(t) * (location.y - lastPosition.y)
                    let interpolatedClipX = Float(interpolatedX / viewSize.width) * 2 - 1
                    let interpolatedClipY = 1 - Float(interpolatedY / viewSize.height) * 2
                    eraseCircleAtLocation(
                        clipX: interpolatedClipX,
                        clipY: interpolatedClipY,
                        square: square,
                        texture: texture,
                        position: CGPoint(x: interpolatedX, y: interpolatedY)
                    )
                }
            }
        }

        eraseCircleAtLocation(clipX: clipX, clipY: clipY, square: square, texture: texture, position: location)
        lastErasePosition = location
    }

    private func eraseCircleAtLocation(clipX: Float, clipY: Float, square: Square, texture: MTLTexture, position: CGPoint) {
        let viewSize = metalView.bounds.size

        let ndcPoint = SIMD4<Float>(clipX, clipY, 0, 1)

        let inverseMatrix = square.transform.matrix.inverse

        let texturePoint = inverseMatrix * ndcPoint

        let u = (texturePoint.x + 0.5)
        let v = (0.5 - texturePoint.y)
//        let v = (texturePoint.y + 0.5)

        let textureX = Int(u * Float(texture.width))
        let textureY = Int(v * Float(texture.height))

        let originX = max(0, textureX - Int(eraserSize))
        let originY = max(0, textureY - Int(eraserSize))
        let endX = min(textureX + Int(eraserSize), texture.width)
        let endY = min(textureY + Int(eraserSize), texture.height)

        let width = endX - originX
        let height = endY - originY
        guard width > 0 && height > 0 else { return }

        let region = MTLRegionMake2D(originX, originY, width, height)

        var originalPixels = [UInt8](repeating: 0, count: width * height * 4)
        texture.getBytes(&originalPixels, bytesPerRow: width * 4, from: region, mipmapLevel: 0)

        erasedPixels.append(ErasedData(square: square, region: region, pixels: originalPixels, position: position))

        var transparent = [UInt8](repeating: 0, count: width * height * 4)
        let centerX = Float(width) / 2
        let centerY = Float(height) / 2
        let radiusSquared = eraserSize * eraserSize

        for y in 0..<height {
            for x in 0..<width {
                let dx = Float(x) - centerX
                let dy = Float(y) - centerY
                let distanceSquared = dx * dx + dy * dy

                if distanceSquared <= radiusSquared {
                    let pixelIndex = (y * width + x) * 4
                    transparent[pixelIndex] = 0     // R
                    transparent[pixelIndex + 1] = 0 // G
                    transparent[pixelIndex + 2] = 0 // B
                    transparent[pixelIndex + 3] = 0 // A
                } else {
                    let pixelIndex = (y * width + x) * 4
                    transparent[pixelIndex] = originalPixels[pixelIndex]         // R
                    transparent[pixelIndex + 1] = originalPixels[pixelIndex + 1] // G
                    transparent[pixelIndex + 2] = originalPixels[pixelIndex + 2] // B
                    transparent[pixelIndex + 3] = originalPixels[pixelIndex + 3] // A
                }
            }
        }

        texture.replace(region: region, mipmapLevel: 0, withBytes: transparent, bytesPerRow: width * 4)
    }

    private func regionsIntersect(_ region1: MTLRegion, _ region2: MTLRegion) -> Bool {
        let rect1 = CGRect(x: CGFloat(region1.origin.x), y: CGFloat(region1.origin.y),
                           width: CGFloat(region1.size.width), height: CGFloat(region1.size.height))
        let rect2 = CGRect(x: CGFloat(region2.origin.x), y: CGFloat(region2.origin.y),
                           width: CGFloat(region2.size.width), height: CGFloat(region2.size.height))
        return rect1.intersects(rect2)
    }

    private func mergeRegions(_ region1: MTLRegion, _ region2: MTLRegion) -> MTLRegion {
        let minX = min(region1.origin.x, region2.origin.x)
        let minY = min(region1.origin.y, region2.origin.y)
        let maxX = max(region1.origin.x + region1.size.width, region2.origin.x + region2.size.width)
        let maxY = max(region1.origin.y + region1.size.height, region2.origin.y + region2.size.height)
        let width = maxX - minX
        let height = maxY - minY
        return MTLRegionMake2D(minX, minY, width, height)
    }

    private func getPixelsFromTexture(_ region: MTLRegion, texture: MTLTexture) -> [UInt8] {
        let width = region.size.width
        let height = region.size.height
        var pixels = [UInt8](repeating: 0, count: width * height * 4)
        texture.getBytes(&pixels, bytesPerRow: width * 4, from: region, mipmapLevel: 0)
        return pixels
    }

    func selectSquareAt(location: CGPoint, hitRadius: CGFloat = 0, inEditMode: Bool = false) -> Bool {
        let previousIndex = currentSquareIndex

        for i in stride(from: squares.count - 1, through: 0, by: -1) {
            let square = squares[i]

            if square.isHidden {
                continue
            }

            if square.isEditMode != inEditMode {
                continue
            }

            let corners = square.computeCornerPoints(
                modelMatrix: square.transform.matrix,
                viewSize: metalView.bounds.size
            )

            if isPointInPolygon(point: location, vertices: corners) {
                currentSquare = square
                currentSquareIndex = i
                currentImage = square.originalImage
                return true
            }
        }

        currentSquare = nil
        currentSquareIndex = -1

        return previousIndex != currentSquareIndex
    }

    private func isPointInPolygon(point: CGPoint, vertices: [CGPoint]) -> Bool {
        var inside = false
        var j = vertices.count - 1

        for i in 0..<vertices.count {
            if ((vertices[i].y > point.y) != (vertices[j].y > point.y)) &&
                (point.x < (vertices[j].x - vertices[i].x) * (point.y - vertices[i].y) /
                 (vertices[j].y - vertices[i].y) + vertices[i].x) {
                inside = !inside
            }
            j = i
        }

        return inside
    }

    func updateSquareColor(color: UIColor) {
        guard let square = currentSquare else { return }

        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0

        color.getRed(&red, green: &green, blue: &blue, alpha: &alpha)

        square.color = SIMD4<Float>(Float(red), Float(green), Float(blue), Float(alpha))

        // シェーダーに色情報を反映するためのコードを確認
        if let encoder = currentRenderCommandEncoder {
            var colorVector = square.color
            encoder.setFragmentBytes(&colorVector, length: MemoryLayout<SIMD4<Float>>.size, index: 2)
        }

        metalView.setNeedsDisplay()
    }

    private func createTextureCopy(from texture: MTLTexture) -> MTLTexture? {
        let descriptor = MTLTextureDescriptor.texture2DDescriptor(
            pixelFormat: texture.pixelFormat,
            width: texture.width,
            height: texture.height,
            mipmapped: false
        )
        guard let copyTexture = device.makeTexture(descriptor: descriptor) else {
            print(" Failed to create texture copy")
            return nil
        }

        let region = MTLRegionMake2D(0, 0, texture.width, texture.height)
        var pixels = [UInt8](repeating: 0, count: texture.width * texture.height * 4)
        texture.getBytes(&pixels, bytesPerRow: texture.width * 4, from: region, mipmapLevel: 0)
        copyTexture.replace(region: region, mipmapLevel: 0, withBytes: pixels, bytesPerRow: texture.width * 4)
        return copyTexture
    }

    func replaceSquareColor(originalColor: UIColor, newColor: UIColor) {
        guard let square = currentSquare, let texture = square.texture else {
            return
        }
        
        guard let image = square.originalImage ?? currentImage else {
            return
        }

        guard let replacedImage = changeColorOnImage(image: image, srcColor: originalColor, dstColor: newColor) else {
            return
        }

        if let newTexture = imageToTexture(image: replacedImage, device: device) {
            square.texture = newTexture
            currentImage = replacedImage
            square.originalImage = replacedImage
        } else {
            return
        }

        metalView.setNeedsDisplay()
    }

    //
    private func changeColorOnImage(image: UIImage, srcColor: UIColor, dstColor: UIColor) -> UIImage? {
        guard let cgImage = image.cgImage else { return nil }

        let width = cgImage.width
        let height = cgImage.height
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let bitmapInfo = CGBitmapInfo(rawValue: CGImageAlphaInfo.premultipliedLast.rawValue)

        guard let context = CGContext(data: nil,
                                      width: width,
                                      height: height,
                                      bitsPerComponent: 8,
                                      bytesPerRow: width * 4,
                                      space: colorSpace,
                                      bitmapInfo: bitmapInfo.rawValue) else {
            return nil
        }

        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))
        guard let pixelData = context.data?.assumingMemoryBound(to: UInt8.self) else { return nil }

        var srcRGB: (r: CGFloat, g: CGFloat, b: CGFloat) = (0, 0, 0)
        srcColor.getRed(&srcRGB.r, green: &srcRGB.g, blue: &srcRGB.b, alpha: nil)
        var srcH: CGFloat = 0, srcS: CGFloat = 0, srcV: CGFloat = 0
        srcColor.getHue(&srcH, saturation: &srcS, brightness: &srcV, alpha: nil)

        var dstRGB: (r: CGFloat, g: CGFloat, b: CGFloat) = (0, 0, 0)
        dstColor.getRed(&dstRGB.r, green: &dstRGB.g, blue: &dstRGB.b, alpha: nil)

        let hueTolerance: CGFloat = 0.02
        let satTolerance: CGFloat = 0.1
        let valTolerance: CGFloat = 0.1
        let rgbTolerance: CGFloat = 0.1
        var pixelsChanged = 0
        var unmatchedColors: [String: Int] = [:]

        for i in stride(from: 0, to: width * height * 4, by: 4) {
            let r = CGFloat(pixelData[i]) / 255.0
            let g = CGFloat(pixelData[i + 1]) / 255.0
            let b = CGFloat(pixelData[i + 2]) / 255.0
            let a = CGFloat(pixelData[i + 3]) / 255.0

            if a < 0.1 { continue }

            let pixelColor = UIColor(red: r, green: g, blue: b, alpha: 1.0)
            var pixelH: CGFloat = 0, pixelS: CGFloat = 0, pixelV: CGFloat = 0
            pixelColor.getHue(&pixelH, saturation: &pixelS, brightness: &pixelV, alpha: nil)

            let hDiff = min(abs(pixelH - srcH), 1.0 - abs(pixelH - srcH))
            let sDiff = abs(pixelS - srcS)
            let vDiff = abs(pixelV - srcV)
            let rDiff = abs(r - srcRGB.r)
            let gDiff = abs(g - srcRGB.g)
            let bDiff = abs(b - srcRGB.b)

            if hDiff <= hueTolerance && sDiff <= satTolerance && vDiff <= valTolerance &&
               rDiff <= rgbTolerance && gDiff <= rgbTolerance && bDiff <= rgbTolerance {
                pixelData[i] = UInt8(dstRGB.r * 255.0)
                pixelData[i + 1] = UInt8(dstRGB.g * 255.0)
                pixelData[i + 2] = UInt8(dstRGB.b * 255.0)
                pixelsChanged += 1

                if pixelsChanged <= 10 {
                    print("before: RGB: \(String(format: "%.2f", r)), \(String(format: "%.2f", g)), \(String(format: "%.2f", b)) HSV: \(String(format: "%.2f", pixelH)), \(String(format: "%.2f", pixelS)), \(String(format: "%.2f", pixelV)) -> after: RGB: \(String(format: "%.2f", dstRGB.r)), \(String(format: "%.2f", dstRGB.g)), \(String(format: "%.2f", dstRGB.b))")
                }
            } else {
                let key = "\(String(format: "%.2f", r)), \(String(format: "%.2f", g)), \(String(format: "%.2f", b))"
                unmatchedColors[key, default: 0] += 1
            }
        }

        if pixelsChanged == 0 {
            print("  srcColor RGB: \(String(format: "%.2f", srcRGB.r)), \(String(format: "%.2f", srcRGB.g)), \(String(format: "%.2f", srcRGB.b)) HSV: \(String(format: "%.2f", srcH)), \(String(format: "%.2f", srcS)), \(String(format: "%.2f", srcV))")
            let sortedUnmatched = unmatchedColors.sorted { $0.value > $1.value }.prefix(20)
            for (color, count) in sortedUnmatched {
                let components = color.split(separator: ",").map { String($0) }
                let r = components[0], g = components[1], b = components[2]
                let pixelColor = UIColor(red: CGFloat(Float(r) ?? 0), green: CGFloat(Float(g) ?? 0), blue: CGFloat(Float(b) ?? 0), alpha: 1.0)
                var h: CGFloat = 0, s: CGFloat = 0, v: CGFloat = 0
                pixelColor.getHue(&h, saturation: &s, brightness: &v, alpha: nil)
            }
            return nil
        }

        guard let newCGImage = context.makeImage() else { return nil }
        return UIImage(cgImage: newCGImage)
    }

    func imageToTexture(image: UIImage, device: MTLDevice) -> MTLTexture? {
        let fixedImage = image.fixOrientation()
        guard let cgImage = fixedImage.cgImage else {
            return nil
        }

        let width = cgImage.width
        let height = cgImage.height
        print("aqaz imageToTexture fixedImage width:\(width), height:\(height)")
        let bytesPerRow = width * 4
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let bitmapInfo = CGBitmapInfo(rawValue: CGImageAlphaInfo.premultipliedLast.rawValue)

        guard let context = CGContext(data: nil,
                                      width: width,
                                      height: height,
                                      bitsPerComponent: 8,
                                      bytesPerRow: bytesPerRow,
                                      space: colorSpace,
                                      bitmapInfo: bitmapInfo.rawValue) else {
            return nil
        }

        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))
        guard let data = context.data else {
            return nil
        }

        let textureDescriptor = MTLTextureDescriptor.texture2DDescriptor(pixelFormat: .rgba8Unorm,
                                                                        width: width,
                                                                        height: height,
                                                                        mipmapped: false)
        guard let texture = device.makeTexture(descriptor: textureDescriptor) else {
            return nil
        }

        texture.replace(region: MTLRegionMake2D(0, 0, width, height),
                        mipmapLevel: 0,
                        withBytes: data,
                        bytesPerRow: bytesPerRow)
        print("🔧 Texture  - width: \(width), height: \(height)")
        return texture
    }

    func getCurrentMatType() -> String {
        return currentSquare?.matType ?? ""
    }

    func resetRestorePosition() {
        lastRestorePosition = nil
    }

    func restoreAtLocation(_ location: CGPoint, radius: Float = 40.0) {
        guard let square = currentSquare,
               let texture = square.texture,
               let originalTexture = originalTexture else {
            return
        }

        let viewSize = metalView.bounds.size

        if let lastPosition = lastRestorePosition {
            let distance = hypot(location.x - lastPosition.x, location.y - lastPosition.y)
            let stepSize = CGFloat(radius) / 3.0
            let steps = Int(distance / stepSize)
            if steps > 0 {
                for i in 1...steps {
                    let t = Float(i) / Float(steps)
                    let interpolatedPoint = interpolatePoint(lastPosition, location, t)
                    performRestoreAtLocation(interpolatedPoint, radius: radius, square: square, texture: texture, originalTexture: originalTexture, viewSize: viewSize)
                }
            }
        }
        performRestoreAtLocation(location, radius: radius, square: square, texture: texture, originalTexture: originalTexture, viewSize: viewSize)
        lastRestorePosition = location
    }
    private func performRestoreAtLocation(_ location: CGPoint, radius: Float, square: Square, texture: MTLTexture, originalTexture: MTLTexture, viewSize: CGSize) {
        let clipX = Float(location.x / viewSize.width) * 2 - 1
        let clipY = 1 - Float(location.y / viewSize.height) * 2

        let ndcPoint = SIMD4<Float>(clipX, clipY, 0, 1)
        let inverseMatrix = square.transform.matrix.inverse
        let texturePoint = inverseMatrix * ndcPoint

        let u = (texturePoint.x + 0.5)
        let v = (0.5 - texturePoint.y)
//        let v = (texturePoint.y + 0.5)

        let textureX = Int(u * Float(texture.width))
        let textureY = Int(v * Float(texture.height))

        let originX = max(0, textureX - Int(radius))
        let originY = max(0, textureY - Int(radius))
        let endX = min(textureX + Int(radius), texture.width)
        let endY = min(textureY + Int(radius), texture.height)

        let width = endX - originX
        let height = endY - originY
        guard width > 0 && height > 0 else { return }

        let region = MTLRegionMake2D(originX, originY, width, height)

        var originalPixels = [UInt8](repeating: 0, count: width * height * 4)
        originalTexture.getBytes(&originalPixels, bytesPerRow: width * 4, from: region, mipmapLevel: 0)

        texture.replace(region: region, mipmapLevel: 0, withBytes: originalPixels, bytesPerRow: width * 4)

        metalView.setNeedsDisplay()
    }

    private func interpolatePoint(_ point1: CGPoint, _ point2: CGPoint, _ t: Float) -> CGPoint {
        let x = point1.x + CGFloat(t) * (point2.x - point1.x)
        let y = point1.y + CGFloat(t) * (point2.y - point1.y)
        return CGPoint(x: x, y: y)
    }

    func restoreAllForCurrentSquare() {
        guard let square = currentSquare else { return }

        if let originalImage = currentImage, let texture = imageToTexture(image: originalImage, device: device) {
            square.texture = texture
            erasedPixels.removeAll { $0.square === square }
            metalView.setNeedsDisplay()
            return
        }

        if let texturePath = square.texturePath, !texturePath.isEmpty,
            let image = UIImage(contentsOfFile: texturePath),
            let texture = imageToTexture(image: image, device: device) {
            square.texture = texture

            erasedPixels.removeAll { $0.square === square }

            metalView.setNeedsDisplay()
            return
        }

        guard let texture = square.texture else {
            return
        }

        var restoredCount = 0
        var indicesToRemove: [Int] = []

        for (index, erasedData) in erasedPixels.enumerated().reversed() {
            if erasedData.square === square {
                texture.replace(
                    region: erasedData.region,
                    mipmapLevel: 0,
                    withBytes: erasedData.pixels,
                    bytesPerRow: erasedData.region.size.width * 4
                )

                indicesToRemove.append(index)
                restoredCount += 1
            }
        }

        for index in indicesToRemove.sorted(by: >) {
            if index < erasedPixels.count {
                erasedPixels.remove(at: index)
            }
        }

        if restoredCount > 0 {
            metalView.setNeedsDisplay()
        }
    }

    func saveErasedPixels(square: Square, region: MTLRegion, pixels: [UInt8], position: CGPoint) {
        erasedPixels.append(ErasedData(square: square, region: region, pixels: pixels, position: position))
    }

    func removeCurrentSquare() {
        if let currentSquare = currentSquare, let index = squares.firstIndex(where: { $0 === currentSquare }) {
            squares.remove(at: index)
            self.currentSquare = nil
        }
    }

    func hideNonEditModeObjects() {
        for square in squares {
            if !square.isEditMode {
                square.isHidden = true
            } else {
                square.isHidden = false
            }
        }
    }

    func hideEditModeObjects() {
        for square in squares {
            if square.isEditMode {
                square.isHidden = true
            } else {
                square.isHidden = false
            }
        }
    }
}

extension UIImage {
    func fixOrientation() -> UIImage {
        if imageOrientation == .up { return self }
        UIGraphicsBeginImageContextWithOptions(size, false, scale)
        draw(in: CGRect(origin: .zero, size: size))
        let normalizedImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return normalizedImage ?? self
    }
}

class ErasedData {
    let square: Square
    let region: MTLRegion
    let pixels: [UInt8]
    let position: CGPoint
    let timestamp: TimeInterval

    init(square: Square, region: MTLRegion, pixels: [UInt8], position: CGPoint) {
        self.square = square
        self.region = region
        self.pixels = pixels
        self.position = position
        self.timestamp = Date().timeIntervalSince1970
    }
}

