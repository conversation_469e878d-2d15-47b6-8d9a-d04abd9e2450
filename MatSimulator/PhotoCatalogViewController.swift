//
//  PhotoCatalogViewController.swift
//  MatSimulator
//
//  Created by Clamour on 2025/02/26.
//

import UIKit

// 写真カタログ画面からのデリゲートプロトコル
protocol PhotoCatalogViewControllerDelegate: AnyObject {
    func photoCatalogViewControllerDidSavePhoto(_ controller: PhotoCatalogViewController, image: UIImage)
    func photoCatalogViewControllerDidDeletePhoto(_ controller: PhotoCatalogViewController, at index: Int)
}

class PhotoCatalogViewController: UIViewController {
    weak var delegate: PhotoCatalogViewControllerDelegate?
    
    // 表示する写真の配列
    private var photos: [UIImage] = []
    // 選択された写真のインデックス
    private var selectedPhotoIndex: Int?
    // 現在表示中のプレビューコントローラー
    private var currentPreviewController: UIViewController?
    
    // ボタンを縦に並べるスタックビュー
    private lazy var buttonStackView: UIStackView = {
        let stack = UIStackView()
        stack.translatesAutoresizingMaskIntoConstraints = false
        stack.axis = .vertical
        stack.spacing = 10
        stack.distribution = .fillEqually
        return stack
    }()
    
    // 写真を表示するコレクションビュー
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.itemSize = CGSize(width: 150, height: 150)
        layout.minimumInteritemSpacing = 10
        layout.minimumLineSpacing = 10
        layout.sectionInset = UIEdgeInsets(top: 10, left: 10, bottom: 10, right: 10)
        
        let collection = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collection.translatesAutoresizingMaskIntoConstraints = false
        collection.backgroundColor = .white
        collection.register(PhotoCell.self, forCellWithReuseIdentifier: "PhotoCell")
        collection.delegate = self
        collection.dataSource = self
        return collection
    }()
    
    // 現在選択されているディレクトリ
    private var currentDirectory: String = "CompositeImages" // デフォルトディレクトリ
    
    // ディレクトリ選択ボタンの配列
    private lazy var directoryButtons: [UIButton] = {
        // ディレクトリ名とボタンタイトルを変更
        let directories = ["CompositeImages", "EditedMatDesigns", "BackgroundImages", "Logos"]
        let titles = ["合成画像", "編集したマットデザイン", "背景画像", "ロゴ"]
        
        return zip(directories, titles).enumerated().map { index, pair in
            let (directory, title) = pair
            let button = UIButton(type: .system)
            button.setTitle(title, for: .normal)
            button.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .medium)
            button.backgroundColor = UIColor(red: 245/255, green: 248/255, blue: 250/255, alpha: 1.0)
            button.setTitleColor(UIColor(red: 60/255, green: 60/255, blue: 67/255, alpha: 1.0), for: .normal)
            button.layer.cornerRadius = 8
            button.layer.borderWidth = 0.5
            button.layer.borderColor = UIColor(red: 229/255, green: 229/255, blue: 234/255, alpha: 1.0).cgColor
            button.contentEdgeInsets = UIEdgeInsets(top: 8, left: 12, bottom: 8, right: 12) 
            button.tag = index
            button.addTarget(self, action: #selector(directoryButtonTapped(_:)), for: .touchUpInside)
            return button
        }
    }()
    
    // ディレクトリボタンを縦に並べるスタックビュー
    private lazy var directoriesStackView: UIStackView = {
        let stack = UIStackView()
        stack.translatesAutoresizingMaskIntoConstraints = false
        stack.axis = .vertical
        stack.spacing = 12
        stack.distribution = .fillEqually
        return stack
    }()
    
    // 区切り線
    private lazy var separatorLine: UIView = {
        let line = UIView()
        line.translatesAutoresizingMaskIntoConstraints = false
        line.backgroundColor = .systemGray4
        return line
    }()
    
    // カメラロールに保存するボタン
    private lazy var saveButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("カメラロールに転送", for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        button.backgroundColor = UIColor(red: 240/255, green: 240/255, blue: 245/255, alpha: 1.0)
        button.setTitleColor(.label, for: .normal)
        button.layer.cornerRadius = 10
        button.layer.borderWidth = 0.5
        button.layer.borderColor = UIColor.systemGray4.cgColor
        let iconImage = UIImage(named: "Save")?.withRenderingMode(.alwaysOriginal)
        button.setImage(iconImage, for: .normal)
        button.contentHorizontalAlignment = .left
        button.semanticContentAttribute = .forceLeftToRight
        
        let buttonPadding: CGFloat = 16
        let iconTextSpacing: CGFloat = 8
        button.contentEdgeInsets = UIEdgeInsets(
            top: 6,  
            left: buttonPadding,
            bottom: 6,
            right: buttonPadding
        )
        
        button.imageEdgeInsets = UIEdgeInsets(
            top: 0,
            left: -buttonPadding,
            bottom: 0,
            right: buttonPadding + iconTextSpacing
        )
        
        button.titleEdgeInsets = UIEdgeInsets(
            top: 0,
            left: -buttonPadding + iconTextSpacing,
            bottom: 0,
            right: 0
        )
        
        button.addTarget(self, action: #selector(saveButtonTapped), for: .touchUpInside)
        button.addTouchAnimation()
        return button
    }()
    
    // 削除ボタン
    private lazy var deleteButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("削除", for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        button.backgroundColor = UIColor(red: 240/255, green: 240/255, blue: 245/255, alpha: 1.0)
        button.setTitleColor(.label, for: .normal)
        button.layer.cornerRadius = 10
        button.layer.borderWidth = 0.5
        button.layer.borderColor = UIColor.systemGray4.cgColor
        let iconImage = UIImage(named: "Remove")?.withRenderingMode(.alwaysOriginal)
        button.setImage(iconImage, for: .normal)
        button.contentHorizontalAlignment = .left
        button.semanticContentAttribute = .forceLeftToRight
        
        let buttonPadding: CGFloat = 16
        let iconTextSpacing: CGFloat = 8
        button.contentEdgeInsets = UIEdgeInsets(
            top: 6,
            left: buttonPadding,
            bottom: 6,
            right: buttonPadding
        )
        
        button.imageEdgeInsets = UIEdgeInsets(
            top: 0,
            left: -buttonPadding,
            bottom: 0,
            right: buttonPadding + iconTextSpacing
        )
        
        button.titleEdgeInsets = UIEdgeInsets(
            top: 0,
            left: -buttonPadding + iconTextSpacing,
            bottom: 0,
            right: 0
        )
        
        button.addTarget(self, action: #selector(deleteButtonTapped), for: .touchUpInside)
        button.addTouchAnimation()
        return button
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI() // UIを設定
        loadPhotos() // 写真を読み込む
    }
    
    // UIの初期設定
    private func setupUI() {
        view.backgroundColor = .white
        
        view.addSubview(directoriesStackView)
        view.addSubview(separatorLine)
        view.addSubview(buttonStackView)
        view.addSubview(collectionView)
        
        directoryButtons.forEach { directoriesStackView.addArrangedSubview($0) }
        buttonStackView.addArrangedSubview(saveButton)
        buttonStackView.addArrangedSubview(deleteButton)
        
        NSLayoutConstraint.activate([
            directoriesStackView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 20),
            directoriesStackView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            directoriesStackView.widthAnchor.constraint(equalToConstant: 200),
            
            separatorLine.topAnchor.constraint(equalTo: directoriesStackView.bottomAnchor, constant: 20),
            separatorLine.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            separatorLine.trailingAnchor.constraint(equalTo: directoriesStackView.trailingAnchor),
            separatorLine.heightAnchor.constraint(equalToConstant: 1),
            
            buttonStackView.topAnchor.constraint(equalTo: separatorLine.bottomAnchor, constant: 20),
            buttonStackView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            buttonStackView.widthAnchor.constraint(equalToConstant: 200),
            
            collectionView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            collectionView.leadingAnchor.constraint(equalTo: directoriesStackView.trailingAnchor, constant: 20),
            collectionView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            collectionView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
        
        updateDirectoryButtonAppearance()
    }
    
    // 写真を読み込むメソッド
    private func loadPhotos() {
        let fileManager = FileManager.default
        
        guard let documentsURL = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return
        }
        let directoryURL = documentsURL.appendingPathComponent(currentDirectory)
        
        if !fileManager.fileExists(atPath: directoryURL.path) {
            do {
                try fileManager.createDirectory(at: directoryURL, withIntermediateDirectories: true)
            } catch {
                print("Error creating directory: \(error.localizedDescription)")
            }
        }
        
        do {
            let fileURLs = try fileManager.contentsOfDirectory(at: directoryURL,
                                                              includingPropertiesForKeys: nil)
            
            let imageURLs = fileURLs.filter { url in
                let ext = url.pathExtension.lowercased()
                return ext == "jpg" || ext == "png"
            }
            
            photos = imageURLs.compactMap { UIImage(contentsOfFile: $0.path) }
            collectionView.reloadData()
        } catch {
            print("Error loading photos: \(error.localizedDescription)")
        }
    }
    
    // ディレクトリボタンがタップされた時の処理
    @objc private func directoryButtonTapped(_ sender: UIButton) {
        // ディレクトリ名の配列を更新
        let directories = ["CompositeImages", "EditedMatDesigns", "BackgroundImages", "Logos"]
        if sender.tag < directories.count {
            currentDirectory = directories[sender.tag]
            updateDirectoryButtonAppearance()
            loadPhotos()
        }
    }
    
    // ディレクトリボタンの外観を更新するメソッド
    private func updateDirectoryButtonAppearance() {
        // ディレクトリ名の配列を更新
        let directories = ["CompositeImages", "EditedMatDesigns", "BackgroundImages", "Logos"]
        for (index, button) in directoryButtons.enumerated() {
            if directories[index] == currentDirectory {
                button.backgroundColor = UIColor(red: 0/255, green: 122/255, blue: 255/255, alpha: 1.0)
                button.setTitleColor(.white, for: .normal)
                button.layer.borderWidth = 0
            } else {
                button.backgroundColor = UIColor(red: 245/255, green: 248/255, blue: 250/255, alpha: 1.0)
                button.setTitleColor(UIColor(red: 60/255, green: 60/255, blue: 67/255, alpha: 1.0), for: .normal)
                button.layer.borderWidth = 0.5
                button.layer.borderColor = UIColor(red: 229/255, green: 229/255, blue: 234/255, alpha: 1.0).cgColor
            }
        }
    }

    // 削除ボタンがタップされた時の処理
    @objc private func deleteButtonTapped() {
        guard let index = selectedPhotoIndex else { return }
        
        let alert = UIAlertController(
            title: "確認",
            message: "この画像を削除しますか？",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "キャンセル", style: .cancel))
        alert.addAction(UIAlertAction(title: "削除", style: .destructive) { [weak self] _ in
            guard let self = self else { return }
            
            if let documentsURL = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first {
                let directoryURL = documentsURL.appendingPathComponent(self.currentDirectory)
                let fileURLs = try? FileManager.default.contentsOfDirectory(
                    at: directoryURL,
                    includingPropertiesForKeys: nil
                )
                
                if let fileURLs = fileURLs, index < fileURLs.count {
                    let fileURL = fileURLs[index]
                    try? FileManager.default.removeItem(at: fileURL)
                }
            }
            
            self.delegate?.photoCatalogViewControllerDidDeletePhoto(self, at: index)
            self.photos.remove(at: index)
            self.selectedPhotoIndex = nil
            self.collectionView.reloadData()
        })
        
        present(alert, animated: true)
    }
    
    
}

extension PhotoCatalogViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return photos.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "PhotoCell", for: indexPath) as! PhotoCell
        cell.imageView.image = photos[indexPath.row]
        return cell
    }
    
    
    @objc private func saveButtonTapped() {
        guard let index = selectedPhotoIndex else { return }
        let image = photos[index]
        UIImageWriteToSavedPhotosAlbum(image, self, #selector(image(_:didFinishSavingWithError:contextInfo:)), nil)
    }
    
    @objc private func image(_ image: UIImage, didFinishSavingWithError error: Error?, contextInfo: UnsafeRawPointer) {
        let title = error == nil ? "保存成功" : "保存失敗"
        let message = error?.localizedDescription
        
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }
}

extension PhotoCatalogViewController: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        selectedPhotoIndex = indexPath.row
        
        if let cell = collectionView.cellForItem(at: indexPath) as? PhotoCell {
            if let doubleTap = cell.gestureRecognizers?.first(where: { $0 is UITapGestureRecognizer && ($0 as? UITapGestureRecognizer)?.numberOfTapsRequired == 2 }) {
            } else {
                let doubleTapGesture = UITapGestureRecognizer(target: self, action: #selector(handleDoubleTap(_:)))
                doubleTapGesture.numberOfTapsRequired = 2
                cell.addGestureRecognizer(doubleTapGesture)
                cell.isUserInteractionEnabled = true
            }
        }
        
        if let oldIndex = selectedPhotoIndex, oldIndex != indexPath.row {
            collectionView.deselectItem(at: IndexPath(row: oldIndex, section: 0), animated: true)
        }
    }
    
    @objc private func handleDoubleTap(_ gesture: UITapGestureRecognizer) {
        guard let cell = gesture.view as? PhotoCell,
              let indexPath = collectionView.indexPath(for: cell),
              indexPath.row < photos.count else {
            return
        }
        
        let image = photos[indexPath.row]
        showImagePreview(image)
    }
    
    private func showImagePreview(_ image: UIImage) {
        let previewVC = UIViewController()
        previewVC.view.backgroundColor = .lightGray
        
        let imageView = UIImageView(image: image)
        imageView.contentMode = .scaleAspectFit
        imageView.translatesAutoresizingMaskIntoConstraints = false
        previewVC.view.addSubview(imageView)
        
        NSLayoutConstraint.activate([
            imageView.topAnchor.constraint(equalTo: previewVC.view.topAnchor),
            imageView.leadingAnchor.constraint(equalTo: previewVC.view.leadingAnchor),
            imageView.trailingAnchor.constraint(equalTo: previewVC.view.trailingAnchor),
            imageView.bottomAnchor.constraint(equalTo: previewVC.view.bottomAnchor)
        ])
        
        let closeButton = UIButton(type: .system)
        closeButton.setTitle("閉じる", for: .normal)
        closeButton.setTitleColor(.white, for: .normal)
        closeButton.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        closeButton.layer.cornerRadius = 8
        closeButton.translatesAutoresizingMaskIntoConstraints = false
        closeButton.addTarget(self, action: #selector(dismissPreviewController(_:)), for: .touchUpInside)
        
        previewVC.view.addSubview(closeButton)
        
        NSLayoutConstraint.activate([
            closeButton.topAnchor.constraint(equalTo: previewVC.view.safeAreaLayoutGuide.topAnchor, constant: 16),
            closeButton.trailingAnchor.constraint(equalTo: previewVC.view.trailingAnchor, constant: -16),
            closeButton.widthAnchor.constraint(equalToConstant: 80),
            closeButton.heightAnchor.constraint(equalToConstant: 40)
        ])
        
        previewVC.modalPresentationStyle = .formSheet
        previewVC.preferredContentSize = CGSize(width: view.bounds.width * 0.8, height: view.bounds.height * 0.8)

        self.currentPreviewController = previewVC
        
        present(previewVC, animated: true)
    }
    
    @objc private func dismissPreviewController(_ sender: Any) {
        currentPreviewController?.dismiss(animated: true)
        currentPreviewController = nil
    }
}

