//
//  BackgroundImageViewController.swift
//  MatSimulator
//
//  Created by Clamour on 2025/02/17.
//

import UIKit
import Photos

// 背景画像を管理するビューコントローラー
class BackgroundImageViewController: UIViewController {
    weak var delegate: BackgroundImageViewControllerDelegate?
    
    // 背景画像を保存するフォルダのパス
    private let imageFolder = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0].appendingPathComponent("BackgroundImages")
    
    // 左側のメニューバー
    private lazy var menuBar: UIView = {
        let view = UIView()
        view.backgroundColor = .lightGray
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()
    
    // カメラで撮影するボタン
    private lazy var cameraButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("背景画像の撮影", for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 16)
        button.setTitleColor(.black, for: .normal)
        button.backgroundColor = .white
        button.translatesAutoresizingMaskIntoConstraints = false
        button.addTarget(self, action: #selector(cameraButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // アルバムから画像を選択するボタン
    private lazy var albumButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("カメラロールから取込", for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 16)
        button.setTitleColor(.black, for: .normal)
        button.backgroundColor = .white
        button.translatesAutoresizingMaskIntoConstraints = false
        button.addTarget(self, action: #selector(albumButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 画像を表示するコレクションビュー
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.itemSize = CGSize(width: 150, height: 150)
        layout.minimumLineSpacing = 10
        layout.minimumInteritemSpacing = 10
        layout.sectionInset = UIEdgeInsets(top: 10, left: 10, bottom: 10, right: 10)
        
        let cv = UICollectionView(frame: .zero, collectionViewLayout: layout)
        cv.backgroundColor = .white
        cv.translatesAutoresizingMaskIntoConstraints = false
        cv.register(ImageCell.self, forCellWithReuseIdentifier: "ImageCell")
        cv.delegate = self
        cv.dataSource = self
        return cv
    }()
    
    // 保存された画像のURLリスト
    private var images: [URL] = []
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        createImageFolderIfNeeded()
        loadImages()
    }
    
    // UIの初期設定
    private func setupUI() {
        view.backgroundColor = .white
        
        view.addSubview(menuBar)
        menuBar.addSubview(cameraButton)
        menuBar.addSubview(albumButton)
        view.addSubview(collectionView)
        
        NSLayoutConstraint.activate([
            menuBar.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            menuBar.topAnchor.constraint(equalTo: view.topAnchor),
            menuBar.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            menuBar.widthAnchor.constraint(equalToConstant: 200),
            
            cameraButton.leadingAnchor.constraint(equalTo: menuBar.leadingAnchor, constant: 10),
            cameraButton.trailingAnchor.constraint(equalTo: menuBar.trailingAnchor, constant: -10),
            cameraButton.topAnchor.constraint(equalTo: menuBar.topAnchor, constant: 20),
            cameraButton.heightAnchor.constraint(equalToConstant: 50),
            
            albumButton.leadingAnchor.constraint(equalTo: menuBar.leadingAnchor, constant: 10),
            albumButton.trailingAnchor.constraint(equalTo: menuBar.trailingAnchor, constant: -10),
            albumButton.topAnchor.constraint(equalTo: cameraButton.bottomAnchor, constant: 10),
            albumButton.heightAnchor.constraint(equalToConstant: 50),
            
            collectionView.leadingAnchor.constraint(equalTo: menuBar.trailingAnchor),
            collectionView.topAnchor.constraint(equalTo: view.topAnchor),
            collectionView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            collectionView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }
    
    // 画像保存フォルダが存在しない場合は作成する
    private func createImageFolderIfNeeded() {
        try? FileManager.default.createDirectory(at: imageFolder, withIntermediateDirectories: true)
    }
    
    // 保存された画像を読み込む
    private func loadImages() {
        let files = try? FileManager.default.contentsOfDirectory(at: imageFolder, includingPropertiesForKeys: nil)
        images = files?.filter { $0.pathExtension.lowercased() == "jpg" || $0.pathExtension.lowercased() == "png" } ?? []
        collectionView.reloadData()
    }
    
    // カメラボタンがタップされた時の処理
    @objc private func cameraButtonTapped() {
        let picker = UIImagePickerController()
        picker.sourceType = .camera
        picker.delegate = self
        present(picker, animated: true)
    }
    
    // アルバムボタンがタップされた時の処理
    @objc private func albumButtonTapped() {
        let picker = UIImagePickerController()
        picker.sourceType = .photoLibrary
        picker.delegate = self
        present(picker, animated: true)
    }
    
    // 画像を保存する
    private func saveImage(_ image: UIImage) {
        let filename = "\(Date().timeIntervalSince1970).jpg"
        let fileURL = imageFolder.appendingPathComponent(filename)
        if let data = image.jpegData(compressionQuality: 0.8) {
            try? data.write(to: fileURL)
            loadImages()
        }
    }
}

// UIImagePickerControllerの委譲処理
extension BackgroundImageViewController: UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    // 画像が選択されたときに呼び出される
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        if let image = info[.originalImage] as? UIImage {
            saveImage(image) // 画像を保存
        }
        picker.dismiss(animated: true) // ピッカーを閉じる
    }
}

// コレクションビューの委譲処理
extension BackgroundImageViewController: UICollectionViewDelegate, UICollectionViewDataSource {
    // セクション内のアイテム数を返す
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return images.count
    }
    
    // セルを設定する
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "ImageCell", for: indexPath) as! ImageCell
        let imageURL = images[indexPath.item]
        cell.imageView.image = UIImage(contentsOfFile: imageURL.path)
        return cell
    }
    
    // アイテムが選択されたときに呼び出される
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let imageURL = images[indexPath.item]
        if let image = UIImage(contentsOfFile: imageURL.path) {
            delegate?.backgroundImageViewController(self, didSelect: image)
        }
    }
}

// 画像セルのカスタムクラス
class ImageCell: UICollectionViewCell {
    // 画像を表示するイメージビュー
    let imageView: UIImageView = {
        let iv = UIImageView()
        iv.contentMode = .scaleAspectFill
        iv.clipsToBounds = true
        iv.translatesAutoresizingMaskIntoConstraints = false
        return iv
    }()
    
    // 初期化処理
    override init(frame: CGRect) {
        super.init(frame: frame)
        contentView.addSubview(imageView)
        NSLayoutConstraint.activate([
            imageView.topAnchor.constraint(equalTo: contentView.topAnchor),
            imageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            imageView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            imageView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor)
        ])
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}

// 背景画像選択の委譲プロトコル
protocol BackgroundImageViewControllerDelegate: AnyObject {
    func backgroundImageViewController(_ controller: BackgroundImageViewController, didSelect image: UIImage)
}
