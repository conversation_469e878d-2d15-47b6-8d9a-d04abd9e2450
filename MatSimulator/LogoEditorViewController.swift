//
//  LogoEditorViewController.swift
//  MatSimulator
//
//  Created by Clamour on 2025/03/08.
//

import UIKit

protocol LogoEditorViewControllerDelegate: AnyObject {
    func logoEditorViewController(_ controller: LogoEditorViewController, didFinishEditingImage image: UIImage)
    func logoEditorViewController(_ controller: LogoEditorViewController, didFinishEditingImage image: UIImage, filePath: String)
}

extension LogoEditorViewControllerDelegate {
    func logoEditorViewController(_ controller: LogoEditorViewController, didFinishEditingImage image: UIImage, filePath: String) {
        logoEditorViewController(controller, didFinishEditingImage: image)
    }
}

class LogoEditorViewController: UIViewController {
    weak var delegate: LogoEditorViewControllerDelegate?
        
    private var originalImage: UIImage
    private var currentImage: UIImage?

    private var isBackgroundRemoveMode = false
    private var isLargeEraserMode = false
    private var isSmallEraserMode = false
    private var isRectangleEraserMode = false
    private var isPolygonEraserMode = false
    private var isFreeformEraserMode = false
    private var isRestoreMode = false
    private let largeEraserSize: CGFloat = 30
    private let smallEraserSize: CGFloat = 15
    private var currentEraserSize: CGFloat = 0
    private var restoreEraserSize: CGFloat = 20
    
    private var firstRectPoint: CGPoint?
    private var rectangleSelectionView: UIView?
    private var rectangleSelectionLayer: CAShapeLayer?
    private var rectangleFirstPointMarker: UIImageView?
    
    private var polygonPoints: [CGPoint] = []
    private var polygonSelectionView: UIView?
    private var polygonSelectionLayer: CAShapeLayer?
    private var polygonPointMarkers: [UIImageView] = []

    private var freeformPoints: [CGPoint] = []
    private var freeformSelectionView: UIView?
    private var freeformSelectionLayer: CAShapeLayer?
    
    private lazy var splitView: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()
    
    private lazy var leftPanel: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        view.backgroundColor = .systemBackground
        return view
    }()
    
    private lazy var rightPanel: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        view.backgroundColor = .systemBackground
        return view
    }()
    
    private lazy var backgroundRemoveButton: UIButton = {
        let button = createOptionButton(title: "背景色の消去", iconName: "BackgroundEraser")
        button.addTarget(self, action: #selector(backgroundRemoveButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var largeEraserButton: UIButton = {
        let button = createOptionButton(title: "消しゴム(大)", iconName: "Erase")
        button.addTarget(self, action: #selector(largeEraserButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var smallEraserButton: UIButton = {
        let button = createOptionButton(title: "消しゴム(小)", iconName: "erase_s")
        button.addTarget(self, action: #selector(smallEraserButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var rectangleEraserButton: UIButton = {
        let button = createOptionButton(title: "消しゴム(四角形)", iconName: "square")
        button.addTarget(self, action: #selector(rectangleEraserButtonTapped), for: .touchUpInside)
        return button
    }()

    private lazy var polygonEraserButton: UIButton = {
        let button = createOptionButton(title: "消しゴム(多角形)", iconName: "polygon")
        button.addTarget(self, action: #selector(polygonEraserButtonTapped), for: .touchUpInside)
        return button
    }()

    private lazy var freeformEraserButton: UIButton = {
        let button = createOptionButton(title: "消しゴム(自由曲線)", iconName: "freehand")
        button.addTarget(self, action: #selector(freeformEraserButtonTapped), for: .touchUpInside)
        return button
    }()

    private lazy var restoreButton: UIButton = {
        let button = createOptionButton(title: "消しゴム(復元)", iconName: "Restore")
        button.addTarget(self, action: #selector(restoreButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var resetButton: UIButton = {
        let button = createOptionButton(title: "最初に戻す", iconName: "Undo")
        button.addTarget(self, action: #selector(resetButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var logoImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.translatesAutoresizingMaskIntoConstraints = false
        imageView.contentMode = .scaleAspectFit
        imageView.backgroundColor = .clear
        return imageView
    }()
    
    private lazy var backgroundImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.translatesAutoresizingMaskIntoConstraints = false
        imageView.contentMode = .scaleToFill
        imageView.image = UIImage(named: "CheckBackground")
        return imageView
    }()
    
    private lazy var backButton: UIButton = {
        let button = createOptionButton(title: "戻る", iconName: "Back")
        button.addTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)
        return button
    }()
    
    init(image: UIImage) {
        self.originalImage = image
        self.currentImage = image
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        logoImageView.image = currentImage
    }
    
    private func createOptionButton(title: String, iconName: String) -> UIButton {
        let button = UIButton(type: .system)
        button.translatesAutoresizingMaskIntoConstraints = false
        
        button.backgroundColor = .secondarySystemBackground
        button.layer.cornerRadius = 10
        
        let image = UIImage(named: iconName)?.withRenderingMode(.alwaysOriginal)
        button.setImage(image, for: .normal)
        
        button.setTitle(title, for: .normal)
        button.tintColor = UIColor(red: 0.20, green: 0.29, blue: 0.37, alpha: 1.0)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        
        button.contentHorizontalAlignment = .left
        button.imageEdgeInsets = UIEdgeInsets(top: 0, left: 10, bottom: 0, right: 0)
        button.titleEdgeInsets = UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 0)
        button.titleLabel?.lineBreakMode = .byTruncatingTail
        button.titleLabel?.adjustsFontSizeToFitWidth = true
        button.titleLabel?.minimumScaleFactor = 0.8
        
        button.heightAnchor.constraint(equalToConstant: 52).isActive = true
        
        return button
    }
    
    private lazy var saveButton: UIButton = {
        let button = createOptionButton(title: "保存", iconName: "Save")
        button.addTarget(self, action: #selector(saveButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private func setupUI() {
        view.backgroundColor = .white
        
        view.addSubview(splitView)
        splitView.addSubview(leftPanel)
        splitView.addSubview(rightPanel)
        
        leftPanel.addSubview(backgroundRemoveButton)
        leftPanel.addSubview(largeEraserButton)
        leftPanel.addSubview(smallEraserButton)
        leftPanel.addSubview(rectangleEraserButton)
        leftPanel.addSubview(polygonEraserButton)
        leftPanel.addSubview(freeformEraserButton)
        leftPanel.addSubview(restoreButton)
        leftPanel.addSubview(resetButton)
        leftPanel.addSubview(saveButton)
        leftPanel.addSubview(backButton)
        
        rightPanel.addSubview(backgroundImageView)
        rightPanel.addSubview(logoImageView)
        
        NSLayoutConstraint.activate([
            splitView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            splitView.bottomAnchor.constraint(equalTo: view.bottomAnchor, constant: -60),
            splitView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            splitView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            
            leftPanel.topAnchor.constraint(equalTo: splitView.topAnchor),
            leftPanel.bottomAnchor.constraint(equalTo: splitView.bottomAnchor),
            leftPanel.leadingAnchor.constraint(equalTo: splitView.leadingAnchor),
            leftPanel.widthAnchor.constraint(equalToConstant: 210),
            
            rightPanel.topAnchor.constraint(equalTo: splitView.topAnchor),
            rightPanel.bottomAnchor.constraint(equalTo: splitView.bottomAnchor),
            rightPanel.leadingAnchor.constraint(equalTo: leftPanel.trailingAnchor),
            rightPanel.trailingAnchor.constraint(equalTo: splitView.trailingAnchor),
            
            backgroundRemoveButton.topAnchor.constraint(equalTo: leftPanel.topAnchor, constant: 20),
            backgroundRemoveButton.leadingAnchor.constraint(equalTo: leftPanel.leadingAnchor, constant: 10),
            backgroundRemoveButton.trailingAnchor.constraint(equalTo: leftPanel.trailingAnchor, constant: -10),
            
            largeEraserButton.topAnchor.constraint(equalTo: backgroundRemoveButton.bottomAnchor, constant: 10),
            largeEraserButton.leadingAnchor.constraint(equalTo: leftPanel.leadingAnchor, constant: 10),
            largeEraserButton.trailingAnchor.constraint(equalTo: leftPanel.trailingAnchor, constant: -10),
            
            smallEraserButton.topAnchor.constraint(equalTo: largeEraserButton.bottomAnchor, constant: 10),
            smallEraserButton.leadingAnchor.constraint(equalTo: leftPanel.leadingAnchor, constant: 10),
            smallEraserButton.trailingAnchor.constraint(equalTo: leftPanel.trailingAnchor, constant: -10),
            
            rectangleEraserButton.topAnchor.constraint(equalTo: smallEraserButton.bottomAnchor, constant: 10),
            rectangleEraserButton.leadingAnchor.constraint(equalTo: leftPanel.leadingAnchor, constant: 10),
            rectangleEraserButton.trailingAnchor.constraint(equalTo: leftPanel.trailingAnchor, constant: -10),
            
            polygonEraserButton.topAnchor.constraint(equalTo: rectangleEraserButton.bottomAnchor, constant: 10),
            polygonEraserButton.leadingAnchor.constraint(equalTo: leftPanel.leadingAnchor, constant: 10),
            polygonEraserButton.trailingAnchor.constraint(equalTo: leftPanel.trailingAnchor, constant: -10),
            
            freeformEraserButton.topAnchor.constraint(equalTo: polygonEraserButton.bottomAnchor, constant: 10),
            freeformEraserButton.leadingAnchor.constraint(equalTo: leftPanel.leadingAnchor, constant: 10),
            freeformEraserButton.trailingAnchor.constraint(equalTo: leftPanel.trailingAnchor, constant: -10),

            restoreButton.topAnchor.constraint(equalTo: freeformEraserButton.bottomAnchor, constant: 10),
            restoreButton.leadingAnchor.constraint(equalTo: leftPanel.leadingAnchor, constant: 10),
            restoreButton.trailingAnchor.constraint(equalTo: leftPanel.trailingAnchor, constant: -10),
            
            resetButton.topAnchor.constraint(equalTo: restoreButton.bottomAnchor, constant: 10),
            resetButton.leadingAnchor.constraint(equalTo: leftPanel.leadingAnchor, constant: 10),
            resetButton.trailingAnchor.constraint(equalTo: leftPanel.trailingAnchor, constant: -10),
            
            saveButton.topAnchor.constraint(equalTo: resetButton.bottomAnchor, constant: 10),
            saveButton.leadingAnchor.constraint(equalTo: leftPanel.leadingAnchor, constant: 10),
            saveButton.trailingAnchor.constraint(equalTo: leftPanel.trailingAnchor, constant: -10),
            
            backButton.leadingAnchor.constraint(equalTo: leftPanel.leadingAnchor, constant: 10),
            backButton.trailingAnchor.constraint(equalTo: leftPanel.trailingAnchor, constant: -10),
            backButton.bottomAnchor.constraint(equalTo: leftPanel.bottomAnchor, constant: -20),
            
            backgroundImageView.topAnchor.constraint(equalTo: rightPanel.topAnchor),
            backgroundImageView.leadingAnchor.constraint(equalTo: rightPanel.leadingAnchor),
            backgroundImageView.trailingAnchor.constraint(equalTo: rightPanel.trailingAnchor),
            backgroundImageView.bottomAnchor.constraint(equalTo: rightPanel.bottomAnchor),
            
            logoImageView.centerXAnchor.constraint(equalTo: rightPanel.centerXAnchor),
            logoImageView.centerYAnchor.constraint(equalTo: rightPanel.centerYAnchor),
            logoImageView.widthAnchor.constraint(lessThanOrEqualTo: rightPanel.widthAnchor, multiplier: 0.9),
            logoImageView.heightAnchor.constraint(lessThanOrEqualTo: rightPanel.heightAnchor, multiplier: 0.9),
            
        ])
    }
    
    @objc private func backgroundRemoveButtonTapped() {
        resetButtons()
        
        isBackgroundRemoveMode = !isBackgroundRemoveMode
        
        if isBackgroundRemoveMode {
            backgroundRemoveButton.backgroundColor = .systemBlue
            backgroundRemoveButton.setTitleColor(.white, for: .normal)
            
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleImageTap(_:)))
            logoImageView.********************(tapGesture)
            logoImageView.isUserInteractionEnabled = true
        } else {
            backgroundRemoveButton.backgroundColor = .secondarySystemBackground
            backgroundRemoveButton.setTitleColor(UIColor(red: 0.20, green: 0.29, blue: 0.37, alpha: 1.0), for: .normal)
            
            if let recognizers = logoImageView.gestureRecognizers {
                for recognizer in recognizers {
                    logoImageView.removeGestureRecognizer(recognizer)
                }
            }
            logoImageView.isUserInteractionEnabled = false
        }
    }

    @objc private func largeEraserButtonTapped() {
        toggleEraserMode(isLarge: true)
    }
    
    @objc private func smallEraserButtonTapped() {
        toggleEraserMode(isLarge: false)
    }

    private func toggleEraserMode(isLarge: Bool) {
        resetButtons()
        
        if isLarge {
            isLargeEraserMode = !isLargeEraserMode
            isSmallEraserMode = false
            
            largeEraserButton.backgroundColor = isLargeEraserMode ? .systemBlue : .secondarySystemBackground
            largeEraserButton.setTitleColor(isLargeEraserMode ? .white : UIColor(red: 0.20, green: 0.29, blue: 0.37, alpha: 1.0), for: .normal)
            smallEraserButton.backgroundColor = .secondarySystemBackground
            smallEraserButton.setTitleColor(UIColor(red: 0.20, green: 0.29, blue: 0.37, alpha: 1.0), for: .normal)
            
            currentEraserSize = largeEraserSize
        } else {
            isSmallEraserMode = !isSmallEraserMode
            isLargeEraserMode = false
            
            smallEraserButton.backgroundColor = isSmallEraserMode ? .systemBlue : .secondarySystemBackground
            smallEraserButton.setTitleColor(isSmallEraserMode ? .white : UIColor(red: 0.20, green: 0.29, blue: 0.37, alpha: 1.0), for: .normal)
            largeEraserButton.backgroundColor = .secondarySystemBackground
            largeEraserButton.setTitleColor(UIColor(red: 0.20, green: 0.29, blue: 0.37, alpha: 1.0), for: .normal)
            
            currentEraserSize = smallEraserSize
        }
        
        if isLargeEraserMode || isSmallEraserMode {
            let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handleEraserPan(_:)))
            logoImageView.********************(panGesture)
            logoImageView.isUserInteractionEnabled = true
        } else {
            if let recognizers = logoImageView.gestureRecognizers {
                for recognizer in recognizers {
                    logoImageView.removeGestureRecognizer(recognizer)
                }
            }
            logoImageView.isUserInteractionEnabled = false
        }
    }

    @objc private func handleEraserPan(_ gesture: UIPanGestureRecognizer) {
        guard (isLargeEraserMode || isSmallEraserMode),
              let image = currentImage else { return }
        
        let location = gesture.location(in: logoImageView)
        let viewSize = logoImageView.bounds.size
        
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self,
                  let cgImage = image.cgImage else {
                DispatchQueue.main.async {
                    self?.view.viewWithTag(1001)?.removeFromSuperview()
                }
                return
            }
            
            let imageSize = CGSize(width: cgImage.width, height: cgImage.height)
            let orientation = image.imageOrientation
            
            let aspectRatio = min(viewSize.width / imageSize.width, viewSize.height / imageSize.height)
            let scaledImageSize = CGSize(width: imageSize.width * aspectRatio, height: imageSize.height * aspectRatio)
            let imageOrigin = CGPoint(
                x: (viewSize.width - scaledImageSize.width) / 2,
                y: (viewSize.height - scaledImageSize.height) / 2
            )
            
            guard location.x >= imageOrigin.x && location.x < imageOrigin.x + scaledImageSize.width &&
                  location.y >= imageOrigin.y && location.y < imageOrigin.y + scaledImageSize.height else {
                DispatchQueue.main.async {
                    if gesture.state == .ended {
                        self.view.viewWithTag(1001)?.removeFromSuperview()
                    }
                }
                return
            }
            
            let normalizedX = (location.x - imageOrigin.x) / scaledImageSize.width
            let normalizedY = (location.y - imageOrigin.y) / scaledImageSize.height
            
            var imageX: CGFloat
            var imageY: CGFloat
            
            switch orientation {
            case .up:
                imageX = normalizedX * imageSize.width
                imageY = normalizedY * imageSize.height
            case .down:
                imageX = (1.0 - normalizedX) * imageSize.width
                imageY = (1.0 - normalizedY) * imageSize.height
            case .left:
                imageX = normalizedY * imageSize.width
                imageY = (1.0 - normalizedX) * imageSize.height
            case .right:
                imageX = (1.0 - normalizedY) * imageSize.width
                imageY = normalizedX * imageSize.height
            case .upMirrored:
                imageX = (1.0 - normalizedX) * imageSize.width
                imageY = normalizedY * imageSize.height
            case .downMirrored:
                imageX = normalizedX * imageSize.width
                imageY = (1.0 - normalizedY) * imageSize.height
            case .leftMirrored:
                imageX = (1.0 - normalizedY) * imageSize.width
                imageY = (1.0 - normalizedX) * imageSize.height
            case .rightMirrored:
                imageX = normalizedY * imageSize.width
                imageY = normalizedX * imageSize.height
            @unknown default:
                imageX = normalizedX * imageSize.width
                imageY = normalizedY * imageSize.height
            }
            
            let finalX = Int(imageX)
            let finalY = Int(imageY)
            
            guard finalX >= 0 && finalX < Int(imageSize.width) && finalY >= 0 && finalY < Int(imageSize.height) else {
                DispatchQueue.main.async {
                    if gesture.state == .ended {
                        self.view.viewWithTag(1001)?.removeFromSuperview()
                    }
                }
                return
            }
            
            self.performEraserOperation(image: image, centerX: finalX, centerY: finalY, size: self.currentEraserSize) { processedImage in
                DispatchQueue.main.async {
                    if let processedImage = processedImage {
                        self.currentImage = processedImage
                        self.logoImageView.image = processedImage
                        self.logoImageView.setNeedsDisplay()
                    }
                    if gesture.state == .ended {
                        self.view.viewWithTag(1001)?.removeFromSuperview()
                    }
                }
            }
        }
    }

    private func performEraserOperation(image: UIImage, centerX: Int, centerY: Int, size: CGFloat, completion: @escaping (UIImage?) -> Void) {
        guard let cgImage = image.cgImage else {
            completion(nil)
            return
        }
        
        let width = cgImage.width
        let height = cgImage.height
        let bytesPerPixel = 4
        let bytesPerRow = bytesPerPixel * width
        let bitsPerComponent = 8
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let bitmapInfo = CGImageAlphaInfo.premultipliedLast.rawValue
        
        guard let context = CGContext(data: nil,
                                      width: width,
                                      height: height,
                                      bitsPerComponent: bitsPerComponent,
                                      bytesPerRow: bytesPerRow,
                                      space: colorSpace,
                                      bitmapInfo: bitmapInfo),
              let data = context.data else {
            completion(nil)
            return
        }
        
        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))
        let pixels = data.assumingMemoryBound(to: UInt8.self)
        
        let radius = Int(size / 2)
        
        for y in max(0, centerY - radius)..<min(height, centerY + radius) {
            for x in max(0, centerX - radius)..<min(width, centerX + radius) {
                let dx = x - centerX
                let dy = y - centerY
                let distance = sqrt(Double(dx * dx + dy * dy))
                
                if distance <= Double(radius) {
                    let offset = (y * bytesPerRow) + (x * bytesPerPixel)
                    pixels.advanced(by: offset)[0] = 0 // R
                    pixels.advanced(by: offset)[1] = 0 // G
                    pixels.advanced(by: offset)[2] = 0 // B
                    pixels.advanced(by: offset)[3] = 0 // A
                }
            }
        }
        
        if let newCGImage = context.makeImage() {
            let processedImage = UIImage(cgImage: newCGImage, scale: image.scale, orientation: image.imageOrientation)
            completion(processedImage)
        } else {
            completion(nil)
        }
    }
    
    @objc private func handleImageTap(_ gesture: UITapGestureRecognizer) {
        guard isBackgroundRemoveMode,
              let image = currentImage else { return }
        
        let location = gesture.location(in: logoImageView)
        let viewSize = logoImageView.bounds.size
        
        let activityIndicator = UIActivityIndicatorView(style: .large)
        activityIndicator.center = view.center
        activityIndicator.startAnimating()
        view.addSubview(activityIndicator)
        
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self,
                  let cgImage = image.cgImage else {
                DispatchQueue.main.async {
                    activityIndicator.removeFromSuperview()
                }
                return
            }
            
            let imageSize = CGSize(width: cgImage.width, height: cgImage.height)
            let orientation = image.imageOrientation
            
            let aspectRatio = min(viewSize.width / imageSize.width, viewSize.height / imageSize.height)
            let scaledImageSize = CGSize(width: imageSize.width * aspectRatio, height: imageSize.height * aspectRatio)
            let imageOrigin = CGPoint(
                x: (viewSize.width - scaledImageSize.width) / 2,
                y: (viewSize.height - scaledImageSize.height) / 2
            )
            
            guard location.x >= imageOrigin.x && location.x < imageOrigin.x + scaledImageSize.width &&
                  location.y >= imageOrigin.y && location.y < imageOrigin.y + scaledImageSize.height else {
                DispatchQueue.main.async {
                    activityIndicator.removeFromSuperview()
                }
                return
            }
            
            let normalizedX = (location.x - imageOrigin.x) / scaledImageSize.width
            let normalizedY = (location.y - imageOrigin.y) / scaledImageSize.height
            
            var imageX: CGFloat
            var imageY: CGFloat
            
            switch orientation {
            case .up:
                imageX = normalizedX * imageSize.width
                imageY = normalizedY * imageSize.height
            case .down:
                imageX = (1.0 - normalizedX) * imageSize.width
                imageY = (1.0 - normalizedY) * imageSize.height
            case .left:
                imageX = normalizedY * imageSize.width
                imageY = (1.0 - normalizedX) * imageSize.height
            case .right:
                imageX = (1.0 - normalizedY) * imageSize.width
                imageY = normalizedX * imageSize.height
            case .upMirrored:
                imageX = (1.0 - normalizedX) * imageSize.width
                imageY = normalizedY * imageSize.height
            case .downMirrored:
                imageX = normalizedX * imageSize.width
                imageY = (1.0 - normalizedY) * imageSize.height
            case .leftMirrored:
                imageX = (1.0 - normalizedY) * imageSize.width
                imageY = (1.0 - normalizedX) * imageSize.height
            case .rightMirrored:
                imageX = normalizedY * imageSize.width
                imageY = normalizedX * imageSize.height
            @unknown default:
                imageX = normalizedX * imageSize.width
                imageY = normalizedY * imageSize.height
            }
            
            let finalX = Int(imageX)
            let finalY = Int(imageY)
            
            guard finalX >= 0 && finalX < Int(imageSize.width) && finalY >= 0 && finalY < Int(imageSize.height) else {
                DispatchQueue.main.async {
                    activityIndicator.removeFromSuperview()
                }
                return
            }
            
            self.performFloodFill(image: image, startX: finalX, startY: finalY) { processedImage in
                DispatchQueue.main.async {
                    if let processedImage = processedImage {
                        self.currentImage = processedImage
                        self.logoImageView.image = processedImage
                        self.logoImageView.setNeedsDisplay()
                    }
                    activityIndicator.removeFromSuperview()
                }
            }
        }
    }
    
    private func performFloodFill(image: UIImage, startX: Int, startY: Int, completion: @escaping (UIImage?) -> Void) {
        guard let cgImage = image.cgImage else {
            completion(nil)
            return
        }
        
        let width = cgImage.width
        let height = cgImage.height
        let bytesPerPixel = 4
        let bytesPerRow = bytesPerPixel * width
        let bitsPerComponent = 8
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let bitmapInfo = CGImageAlphaInfo.premultipliedLast.rawValue
        
        guard let context = CGContext(data: nil,
                                      width: width,
                                      height: height,
                                      bitsPerComponent: bitsPerComponent,
                                      bytesPerRow: bytesPerRow,
                                      space: colorSpace,
                                      bitmapInfo: bitmapInfo),
              let data = context.data else {
            completion(nil)
            return
        }
        
        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))
        let pixels = data.assumingMemoryBound(to: UInt8.self)
        
        let pixelOffset = (startY * bytesPerRow) + (startX * bytesPerPixel)
        let targetColor = pixels.advanced(by: pixelOffset)
        let r = targetColor[0]
        let g = targetColor[1]
        let b = targetColor[2]
        let tolerance: UInt8 = 30
        
        let directions = [(0, 1), (1, 0), (0, -1), (-1, 0)]
        var hasAdjacentSimilarColor = false
        
        for (dx, dy) in directions {
            let nx = startX + dx
            let ny = startY + dy
            if nx >= 0 && nx < width && ny >= 0 && ny < height {
                let offset = (ny * bytesPerRow) + (nx * bytesPerPixel)
                let pixel = pixels.advanced(by: offset)
                if abs(Int(pixel[0]) - Int(r)) <= Int(tolerance) &&
                   abs(Int(pixel[1]) - Int(g)) <= Int(tolerance) &&
                   abs(Int(pixel[2]) - Int(b)) <= Int(tolerance) {
                    hasAdjacentSimilarColor = true
                    break
                }
            }
        }

        if hasAdjacentSimilarColor {
            var visited = Array(repeating: Array(repeating: false, count: height), count: width)
            var queue = [(startX, startY)]
            visited[startX][startY] = true
            
            while !queue.isEmpty {
                let (x, y) = queue.removeFirst()
                let offset = (y * bytesPerRow) + (x * bytesPerPixel)
                let pixel = pixels.advanced(by: offset)
                
                if abs(Int(pixel[0]) - Int(r)) <= Int(tolerance) &&
                   abs(Int(pixel[1]) - Int(g)) <= Int(tolerance) &&
                   abs(Int(pixel[2]) - Int(b)) <= Int(tolerance) {

                    pixel[0] = 0 // R
                    pixel[1] = 0 // G
                    pixel[2] = 0 // B
                    pixel[3] = 0 // A
                    
                    for (dx, dy) in directions {
                        let nx = x + dx
                        let ny = y + dy
                        if nx >= 0 && nx < width && ny >= 0 && ny < height && !visited[nx][ny] {
                            visited[nx][ny] = true
                            queue.append((nx, ny))
                        }
                    }
                }
            }
        } else {
            pixels.advanced(by: pixelOffset)[0] = 0 // R
            pixels.advanced(by: pixelOffset)[1] = 0 // G
            pixels.advanced(by: pixelOffset)[2] = 0 // B
            pixels.advanced(by: pixelOffset)[3] = 0 // A
        }

        if let newCGImage = context.makeImage() {
            let processedImage = UIImage(cgImage: newCGImage, scale: image.scale, orientation: image.imageOrientation)
            completion(processedImage)
        } else {
            completion(nil)
        }
    }
    
    private func getColorAtPoint(image: UIImage, point: CGPoint) -> UIColor {
        let pixelData = image.cgImage!.dataProvider!.data
        let data: UnsafePointer<UInt8> = CFDataGetBytePtr(pixelData)
        
        let bytesPerPixel = 4
        let bytesPerRow = bytesPerPixel * Int(image.size.width)
        let pixelInfo = Int(point.y) * bytesPerRow + Int(point.x) * bytesPerPixel
        
        let r = CGFloat(data[pixelInfo]) / 255.0
        let g = CGFloat(data[pixelInfo+1]) / 255.0
        let b = CGFloat(data[pixelInfo+2]) / 255.0
        let a = CGFloat(data[pixelInfo+3]) / 255.0
        
        return UIColor(red: r, green: g, blue: b, alpha: a)
    }
    
    private func createTransparencyCubeData(dimension: Int, colorToReplace: UIColor, tolerance: CGFloat) -> [Float] {
        var cubeData = [Float](repeating: 0, count: dimension * dimension * dimension * 4)
        
        var r: CGFloat = 0
        var g: CGFloat = 0
        var b: CGFloat = 0
        var a: CGFloat = 0
        colorToReplace.getRed(&r, green: &g, blue: &b, alpha: &a)
        
        for z in 0..<dimension {
            let blue = CGFloat(z) / CGFloat(dimension - 1)
            for y in 0..<dimension {
                let green = CGFloat(y) / CGFloat(dimension - 1)
                for x in 0..<dimension {
                    let red = CGFloat(x) / CGFloat(dimension - 1)
                    
                    let index = z * dimension * dimension + y * dimension + x
                    
                    let distance = sqrt(pow(red - r, 2) + pow(green - g, 2) + pow(blue - b, 2))
                    
                    if distance < tolerance {
                        cubeData[index * 4] = Float(red)
                        cubeData[index * 4 + 1] = Float(green)
                        cubeData[index * 4 + 2] = Float(blue)
                        cubeData[index * 4 + 3] = 0.0
                    } else {
                        cubeData[index * 4] = Float(red)
                        cubeData[index * 4 + 1] = Float(green)
                        cubeData[index * 4 + 2] = Float(blue)
                        cubeData[index * 4 + 3] = 1.0
                    }
                }
            }
        }
        
        return cubeData
    }

    @objc private func rectangleEraserButtonTapped() {
        resetButtons()
        
        isRectangleEraserMode = !isRectangleEraserMode
        
        if isRectangleEraserMode {
            rectangleEraserButton.backgroundColor = .systemBlue
            rectangleEraserButton.setTitleColor(.white, for: .normal)
            
            // 既存のジェスチャーを削除
            if let recognizers = logoImageView.gestureRecognizers {
                for recognizer in recognizers {
                    logoImageView.removeGestureRecognizer(recognizer)
                }
            }
            
            // タップジェスチャーを追加
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleRectangleSelectionTap(_:)))
            logoImageView.********************(tapGesture)
            logoImageView.isUserInteractionEnabled = true
            
            // 選択状態をリセット
            clearRectangleSelection()
        } else {
            rectangleEraserButton.backgroundColor = .secondarySystemBackground
            rectangleEraserButton.setTitleColor(UIColor(red: 0.20, green: 0.29, blue: 0.37, alpha: 1.0), for: .normal)
            
            if let recognizers = logoImageView.gestureRecognizers {
                for recognizer in recognizers {
                    logoImageView.removeGestureRecognizer(recognizer)
                }
            }
            logoImageView.isUserInteractionEnabled = false
            
            clearRectangleSelection()
        }
    }
    
    @objc private func handleRectangleSelectionTap(_ gesture: UITapGestureRecognizer) {
        guard isRectangleEraserMode else { return }
        
        let location = gesture.location(in: logoImageView)
        
        if let selectionView = rectangleSelectionView, let selectionLayer = rectangleSelectionLayer {
            let path = selectionLayer.path!
            let isInside = path.contains(location)
            
            UIImpactFeedbackGenerator(style: .medium).impactOccurred()
            let alertTitle = isInside ? "内部消去" : "外部消去"
            let alertMessage = isInside ? "選択した範囲の内部を消去します。\nよろしいですか？" : "選択した範囲の外部を消去します。\nよろしいですか？"
            let alert = UIAlertController(
                title: alertTitle,
                message: alertMessage,
                preferredStyle: .alert
            )
            
            alert.addAction(UIAlertAction(title: "キャンセル", style: .cancel))
            alert.addAction(UIAlertAction(title: "OK", style: .destructive) { [weak self] _ in
                self?.eraseRectangleArea(isInside: isInside, rectangle: selectionLayer.path!)
                self?.clearRectangleSelection()
            })
            self.present(alert, animated: true)
            return
        }
        
        if firstRectPoint == nil {
            firstRectPoint = location
            showFirstPointMarker(at: location)
        } else if let firstPoint = firstRectPoint {
            createRectangleSelection(from: firstPoint, to: location)
            firstRectPoint = nil
            rectangleFirstPointMarker?.removeFromSuperview()
            rectangleFirstPointMarker = nil
        }
    }

    private func showFirstPointMarker(at location: CGPoint) {
        rectangleFirstPointMarker?.removeFromSuperview()
        
        let marker = UIImageView(image: UIImage(named: "cerclePoint"))
        marker.frame = CGRect(x: 0, y: 0, width: 40, height: 40)
        marker.center = location
        logoImageView.addSubview(marker)
        
        rectangleFirstPointMarker = marker
    }
    
    private func createRectangleSelection(from: CGPoint, to: CGPoint) {
        clearRectangleSelection()
        
        let minX = min(from.x, to.x)
        let minY = min(from.y, to.y)
        let width = abs(to.x - from.x)
        let height = abs(to.y - from.y)
        
        let rect = CGRect(x: minX, y: minY, width: width, height: height)
        
        let selectionView = UIView(frame: logoImageView.bounds)
        selectionView.backgroundColor = UIColor.clear
        
        let selectionLayer = CAShapeLayer()
        selectionLayer.fillColor = UIColor.white.withAlphaComponent(0.3).cgColor
        selectionLayer.strokeColor = UIColor.red.cgColor
        selectionLayer.lineWidth = 2.0
        selectionLayer.lineDashPattern = [4, 4]
        
        let path = UIBezierPath(rect: rect)
        selectionLayer.path = path.cgPath
        
        selectionView.layer.addSublayer(selectionLayer)
        logoImageView.addSubview(selectionView)
        
        rectangleSelectionView = selectionView
        rectangleSelectionLayer = selectionLayer
    }
    
    private func clearRectangleSelection() {
        rectangleSelectionView?.removeFromSuperview()
        rectangleSelectionView = nil
        rectangleSelectionLayer = nil
        firstRectPoint = nil
        rectangleFirstPointMarker?.removeFromSuperview()
        rectangleFirstPointMarker = nil
    }
    
    private func eraseRectangleArea(isInside: Bool, rectangle: CGPath) {
        guard let image = currentImage else { return }
        
        let boundingBox = rectangle.boundingBox
        let imageSize = image.size
        let viewSize = logoImageView.bounds.size
        
        // 画像の表示スケールを計算
        let aspectRatio = min(viewSize.width / imageSize.width, viewSize.height / imageSize.height)
        let scaledImageSize = CGSize(width: imageSize.width * aspectRatio, height: imageSize.height * aspectRatio)
        let imageOrigin = CGPoint(
            x: (viewSize.width - scaledImageSize.width) / 2,
            y: (viewSize.height - scaledImageSize.height) / 2
        )
        
        // 選択領域を画像座標系に変換
        let imageRect = CGRect(
            x: (boundingBox.minX - imageOrigin.x) / scaledImageSize.width * imageSize.width,
            y: (boundingBox.minY - imageOrigin.y) / scaledImageSize.height * imageSize.height,
            width: boundingBox.width / scaledImageSize.width * imageSize.width,
            height: boundingBox.height / scaledImageSize.height * imageSize.height
        )
        
        UIGraphicsBeginImageContextWithOptions(imageSize, false, image.scale)
        defer { UIGraphicsEndImageContext() }
        
        let context = UIGraphicsGetCurrentContext()!
        
        // 元の画像を描画
        image.draw(in: CGRect(origin: .zero, size: imageSize))
        
        // 選択領域を設定
        let rectPath = UIBezierPath(rect: imageRect)
        
        if isInside {
            // 内部を消去
            context.saveGState()
            rectPath.addClip()
            context.clear(CGRect(origin: .zero, size: imageSize))
            context.restoreGState()
        } else {
            // 外部を消去
            context.saveGState()
            let outerPath = UIBezierPath(rect: CGRect(origin: .zero, size: imageSize))
            outerPath.append(rectPath)
            outerPath.usesEvenOddFillRule = true
            outerPath.addClip()
            context.clear(CGRect(origin: .zero, size: imageSize))
            context.restoreGState()
        }
        
        if let processedImage = UIGraphicsGetImageFromCurrentImageContext() {
            currentImage = processedImage
            logoImageView.image = processedImage
        }
        
        // フィードバックを表示
        showEraseFeedback(isInside: isInside)
    }

    private func showEraseFeedback(isInside: Bool) {
        let feedbackLabel = UILabel()
        feedbackLabel.text = isInside ? "領域の内容が削除された" : "領域外の内容が削除された"
        feedbackLabel.textColor = .white
        feedbackLabel.backgroundColor = UIColor.black.withAlphaComponent(0.7)
        feedbackLabel.textAlignment = .center
        feedbackLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        feedbackLabel.layer.cornerRadius = 8
        feedbackLabel.layer.masksToBounds = true
        feedbackLabel.sizeToFit()
        
        let padding: CGFloat = 16
        feedbackLabel.frame = CGRect(
            x: (view.bounds.width - feedbackLabel.frame.width - padding) / 2,
            y: (view.bounds.height - feedbackLabel.frame.height - padding) / 2,
            width: feedbackLabel.frame.width + padding,
            height: feedbackLabel.frame.height + padding
        )
        
        view.addSubview(feedbackLabel)
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            UIView.animate(withDuration: 0.3, animations: {
                feedbackLabel.alpha = 0
            }) { _ in
                feedbackLabel.removeFromSuperview()
            }
        }
    }

    @objc private func polygonEraserButtonTapped() {
        resetButtons()
        
        isPolygonEraserMode = !isPolygonEraserMode
        
        if isPolygonEraserMode {
            polygonEraserButton.backgroundColor = .systemBlue
            polygonEraserButton.setTitleColor(.white, for: .normal)
            
            // 既存のジェスチャーを削除
            if let recognizers = logoImageView.gestureRecognizers {
                for recognizer in recognizers {
                    logoImageView.removeGestureRecognizer(recognizer)
                }
            }
            
            // タップジェスチャーを追加
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handlePolygonSelectionTap(_:)))
            logoImageView.********************(tapGesture)
            logoImageView.isUserInteractionEnabled = true
            
            // 選択状態をリセット
            clearPolygonSelection()
        } else {
            polygonEraserButton.backgroundColor = .secondarySystemBackground
            polygonEraserButton.setTitleColor(UIColor(red: 0.20, green: 0.29, blue: 0.37, alpha: 1.0), for: .normal)
            
            if let recognizers = logoImageView.gestureRecognizers {
                for recognizer in recognizers {
                    logoImageView.removeGestureRecognizer(recognizer)
                }
            }
            logoImageView.isUserInteractionEnabled = false
            
            clearPolygonSelection()
        }
    }
    
    @objc private func handlePolygonSelectionTap(_ gesture: UITapGestureRecognizer) {
        guard isPolygonEraserMode else { return }
        
        let location = gesture.location(in: logoImageView)
        
        if let selectionView = polygonSelectionView, let selectionLayer = polygonSelectionLayer, 
           selectionLayer.fillColor != UIColor.clear.cgColor {
            let path = selectionLayer.path!
            let isInside = path.contains(location)
            
            UIImpactFeedbackGenerator(style: .medium).impactOccurred()
            let alertTitle = isInside ? "内部消去" : "外部消去"
            let alertMessage = isInside ? "選択した範囲の内部を消去します。\nよろしいですか？" : "選択した範囲の外部を消去します。\nよろしいですか？"
            let alert = UIAlertController(
                title: alertTitle,
                message: alertMessage,
                preferredStyle: .alert
            )
            
            alert.addAction(UIAlertAction(title: "キャンセル", style: .cancel))
            alert.addAction(UIAlertAction(title: "OK", style: .destructive) { [weak self] _ in
                self?.erasePolygonArea(isInside: isInside, polygon: selectionLayer.path!)
                self?.clearPolygonSelection()
            })
            self.present(alert, animated: true)
            return
        }
        
        if polygonPoints.count >= 2 {
            let startPoint = polygonPoints[0]
            let distance = hypot(location.x - startPoint.x, location.y - startPoint.y)
            
            if distance < 30 {
                completePolygon()
                return
            }
        }
        
        polygonPoints.append(location)
        addPolygonPointMarker(at: location)
        updatePolygonPath()
    }
    
    private func updatePolygonPath() {
        // 既存の選択表示を削除
        polygonSelectionView?.removeFromSuperview()
        polygonSelectionLayer = nil
        
        guard polygonPoints.count >= 2 else { return }
        
        let selectionView = UIView(frame: logoImageView.bounds)
        selectionView.backgroundColor = UIColor.clear
        
        let selectionLayer = CAShapeLayer()
        selectionLayer.fillColor = UIColor.clear.cgColor
        selectionLayer.strokeColor = UIColor.red.cgColor
        selectionLayer.lineWidth = 2.0
        selectionLayer.lineDashPattern = [4, 4]
        
        let path = UIBezierPath()
        path.move(to: polygonPoints[0])
        
        for i in 1..<polygonPoints.count {
            path.addLine(to: polygonPoints[i])
        }
        
        selectionLayer.path = path.cgPath
        
        selectionView.layer.addSublayer(selectionLayer)
        logoImageView.addSubview(selectionView)
        
        polygonSelectionView = selectionView
        polygonSelectionLayer = selectionLayer
    }
    
    private func completePolygon() {
        guard polygonPoints.count >= 3 else { return }
        
        // 多角形を完成させる
        if let selectionLayer = polygonSelectionLayer {
            let path = UIBezierPath()
            path.move(to: polygonPoints[0])
            
            for i in 1..<polygonPoints.count {
                path.addLine(to: polygonPoints[i])
            }
            
            path.close()
            
            selectionLayer.path = path.cgPath
            selectionLayer.fillColor = UIColor.white.withAlphaComponent(0.3).cgColor
        }
        
        // 選択完了のフィードバック
        UIImpactFeedbackGenerator(style: .medium).impactOccurred()
        
        // マーカーを削除（選択が完了したため）
        for marker in polygonPointMarkers {
            marker.removeFromSuperview()
        }
        polygonPointMarkers.removeAll()
    }
    
    private func addPolygonPointMarker(at location: CGPoint) {
        let marker = UIImageView(image: UIImage(named: "cerclePoint"))
        marker.frame = CGRect(x: 0, y: 0, width: 30, height: 30)
        marker.center = location
        logoImageView.addSubview(marker)
        
        polygonPointMarkers.append(marker)
    }
    
    private func clearPolygonSelection() {
        polygonSelectionView?.removeFromSuperview()
        polygonSelectionView = nil
        polygonSelectionLayer = nil
        
        for marker in polygonPointMarkers {
            marker.removeFromSuperview()
        }
        polygonPointMarkers.removeAll()
        polygonPoints.removeAll()
    }
    
    private func erasePolygonArea(isInside: Bool, polygon: CGPath) {
        guard let image = currentImage else { return }
        
        let imageSize = image.size
        let viewSize = logoImageView.bounds.size
        
        // 画像の表示スケールを計算
        let aspectRatio = min(viewSize.width / imageSize.width, viewSize.height / imageSize.height)
        let scaledImageSize = CGSize(width: imageSize.width * aspectRatio, height: imageSize.height * aspectRatio)
        let imageOrigin = CGPoint(
            x: (viewSize.width - scaledImageSize.width) / 2,
            y: (viewSize.height - scaledImageSize.height) / 2
        )
        
        // 多角形の点を画像座標系に変換
        let imagePolygonPoints = polygonPoints.map { point in
            CGPoint(
                x: (point.x - imageOrigin.x) / scaledImageSize.width * imageSize.width,
                y: (point.y - imageOrigin.y) / scaledImageSize.height * imageSize.height
            )
        }
        
        UIGraphicsBeginImageContextWithOptions(imageSize, false, image.scale)
        defer { UIGraphicsEndImageContext() }
        
        let context = UIGraphicsGetCurrentContext()!
        
        // 元の画像を描画
        image.draw(in: CGRect(origin: .zero, size: imageSize))
        
        // 多角形パスを作成
        let polygonPath = UIBezierPath()
        if let firstPoint = imagePolygonPoints.first {
            polygonPath.move(to: firstPoint)
            
            for i in 1..<imagePolygonPoints.count {
                polygonPath.addLine(to: imagePolygonPoints[i])
            }
            
            polygonPath.close()
        }
        
        if isInside {
            // 内部を消去
            context.saveGState()
            polygonPath.addClip()
            context.clear(CGRect(origin: .zero, size: imageSize))
            context.restoreGState()
        } else {
            // 外部を消去
            context.saveGState()
            let outerPath = UIBezierPath(rect: CGRect(origin: .zero, size: imageSize))
            outerPath.append(polygonPath)
            outerPath.usesEvenOddFillRule = true
            outerPath.addClip()
            context.clear(CGRect(origin: .zero, size: imageSize))
            context.restoreGState()
        }
        
        if let processedImage = UIGraphicsGetImageFromCurrentImageContext() {
            currentImage = processedImage
            logoImageView.image = processedImage
        }
        
        // フィードバックを表示
        showEraseFeedback(isInside: isInside)
    }

    @objc private func freeformEraserButtonTapped() {
        resetButtons()

        isFreeformEraserMode = !isFreeformEraserMode
        
        if isFreeformEraserMode {
            freeformEraserButton.backgroundColor = .systemBlue
            freeformEraserButton.setTitleColor(.white, for: .normal)
            
            // 既存のジェスチャーを削除
            if let recognizers = logoImageView.gestureRecognizers {
                for recognizer in recognizers {
                    logoImageView.removeGestureRecognizer(recognizer)
                }
            }
            
            // パンジェスチャーを追加
            let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handleFreeformSelectionPan(_:)))
            logoImageView.********************(panGesture)
            logoImageView.isUserInteractionEnabled = true
            
            // 選択状態をリセット
            clearFreeformSelection()
        } else {
            freeformEraserButton.backgroundColor = .secondarySystemBackground
            freeformEraserButton.setTitleColor(UIColor(red: 0.20, green: 0.29, blue: 0.37, alpha: 1.0), for: .normal)
            
            if let recognizers = logoImageView.gestureRecognizers {
                for recognizer in recognizers {
                    logoImageView.removeGestureRecognizer(recognizer)
                }
            }
            logoImageView.isUserInteractionEnabled = false
            
            clearFreeformSelection()
        }
    }
    
    @objc private func handleFreeformSelectionPan(_ gesture: UIPanGestureRecognizer) {
        guard isFreeformEraserMode else { return }
        
        let location = gesture.location(in: logoImageView)
        
        if gesture.state == .began {
            // 新しい選択を開始
            clearFreeformSelection()
            freeformPoints = [location]
        } else if gesture.state == .changed {
            // 点を追加
            freeformPoints.append(location)
            updateFreeformPath()
        } else if gesture.state == .ended {
            // 選択を完了
            if freeformPoints.count >= 3 {
                completeFreeformSelection()
            } else {
                clearFreeformSelection()
            }
        }
    }

    private func updateFreeformPath() {
        // 既存の選択表示を削除
        freeformSelectionView?.removeFromSuperview()
        freeformSelectionLayer = nil
        
        guard freeformPoints.count >= 2 else { return }
        
        let selectionView = UIView(frame: logoImageView.bounds)
        selectionView.backgroundColor = UIColor.clear
        
        let selectionLayer = CAShapeLayer()
        selectionLayer.fillColor = UIColor.clear.cgColor
        selectionLayer.strokeColor = UIColor.red.cgColor
        selectionLayer.lineWidth = 2.0
        selectionLayer.lineDashPattern = [4, 4]
        
        let path = UIBezierPath()
        path.move(to: freeformPoints[0])
        
        for i in 1..<freeformPoints.count {
            path.addLine(to: freeformPoints[i])
        }
        
        selectionLayer.path = path.cgPath
        
        selectionView.layer.addSublayer(selectionLayer)
        logoImageView.addSubview(selectionView)
        
        freeformSelectionView = selectionView
        freeformSelectionLayer = selectionLayer
    }

    private func completeFreeformSelection() {
        guard freeformPoints.count >= 3 else { return }
        
        // 自由曲線を閉じる
        if let selectionLayer = freeformSelectionLayer {
            let path = UIBezierPath()
            path.move(to: freeformPoints[0])
            
            for i in 1..<freeformPoints.count {
                path.addLine(to: freeformPoints[i])
            }
            
            // 最初の点に戻って閉じる
            path.close()
            
            selectionLayer.path = path.cgPath
            selectionLayer.fillColor = UIColor.white.withAlphaComponent(0.3).cgColor
            
            // タップジェスチャーを追加して内部/外部の選択を可能にする
            if let recognizers = logoImageView.gestureRecognizers {
                for recognizer in recognizers {
                    logoImageView.removeGestureRecognizer(recognizer)
                }
            }
            
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleFreeformSelectionTap(_:)))
            logoImageView.********************(tapGesture)
        }
        
        // 選択完了のフィードバック
        UIImpactFeedbackGenerator(style: .medium).impactOccurred()
    }

    @objc private func handleFreeformSelectionTap(_ gesture: UITapGestureRecognizer) {
        guard isFreeformEraserMode, let selectionLayer = freeformSelectionLayer, 
              selectionLayer.fillColor != UIColor.clear.cgColor else { return }
        
        let location = gesture.location(in: logoImageView)
        let path = selectionLayer.path!
        let isInside = path.contains(location)
        
        UIImpactFeedbackGenerator(style: .medium).impactOccurred()
        let alertTitle = isInside ? "内部消去" : "外部消去"
        let alertMessage = isInside ? "選択した範囲の内部を消去します。\nよろしいですか？" : "選択した範囲の外部を消去します。\nよろしいですか？"
        let alert = UIAlertController(
            title: alertTitle,
            message: alertMessage,
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "キャンセル", style: .cancel))
        alert.addAction(UIAlertAction(title: "OK", style: .destructive) { [weak self] _ in
            self?.eraseFreeformArea(isInside: isInside, freeform: path)
            self?.clearFreeformSelection()
        })
        self.present(alert, animated: true)
    }
    
    private func eraseFreeformArea(isInside: Bool, freeform: CGPath) {
        guard let image = currentImage else { return }
        
        let imageSize = image.size
        let viewSize = logoImageView.bounds.size
        
        // 画像の表示スケールを計算
        let aspectRatio = min(viewSize.width / imageSize.width, viewSize.height / imageSize.height)
        let scaledImageSize = CGSize(width: imageSize.width * aspectRatio, height: imageSize.height * aspectRatio)
        let imageOrigin = CGPoint(
            x: (viewSize.width - scaledImageSize.width) / 2,
            y: (viewSize.height - scaledImageSize.height) / 2
        )
        
        // 自由曲線の点を画像座標系に変換
        let imageFreeformPoints = freeformPoints.map { point in
            CGPoint(
                x: (point.x - imageOrigin.x) / scaledImageSize.width * imageSize.width,
                y: (point.y - imageOrigin.y) / scaledImageSize.height * imageSize.height
            )
        }
        
        UIGraphicsBeginImageContextWithOptions(imageSize, false, image.scale)
        defer { UIGraphicsEndImageContext() }
        
        let context = UIGraphicsGetCurrentContext()!
        
        // 元の画像を描画
        image.draw(in: CGRect(origin: .zero, size: imageSize))
        
        // 自由曲線パスを作成
        let freeformPath = UIBezierPath()
        if let firstPoint = imageFreeformPoints.first {
            freeformPath.move(to: firstPoint)
            
            for i in 1..<imageFreeformPoints.count {
                freeformPath.addLine(to: imageFreeformPoints[i])
            }
            
            freeformPath.close()
        }
        
        if isInside {
            // 内部を消去
            context.saveGState()
            freeformPath.addClip()
            context.clear(CGRect(origin: .zero, size: imageSize))
            context.restoreGState()
        } else {
            // 外部を消去
            context.saveGState()
            let outerPath = UIBezierPath(rect: CGRect(origin: .zero, size: imageSize))
            outerPath.append(freeformPath)
            outerPath.usesEvenOddFillRule = true
            outerPath.addClip()
            context.clear(CGRect(origin: .zero, size: imageSize))
            context.restoreGState()
        }
        
        if let processedImage = UIGraphicsGetImageFromCurrentImageContext() {
            currentImage = processedImage
            logoImageView.image = processedImage
        }
        
        // フィードバックを表示
        showEraseFeedback(isInside: isInside)
    }

    private func clearFreeformSelection() {
        freeformSelectionView?.removeFromSuperview()
        freeformSelectionView = nil
        freeformSelectionLayer = nil
        freeformPoints.removeAll()
    }

    @objc private func restoreButtonTapped() {
        resetButtons()

        isRestoreMode = !isRestoreMode
        
        if isRestoreMode {
            restoreButton.backgroundColor = .systemBlue
            restoreButton.setTitleColor(.white, for: .normal)
            
            // 既存のジェスチャーを削除
            if let recognizers = logoImageView.gestureRecognizers {
                for recognizer in recognizers {
                    logoImageView.removeGestureRecognizer(recognizer)
                }
            }
            
            // パンジェスチャーを追加
            let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handleRestorePan(_:)))
            logoImageView.********************(panGesture)
            logoImageView.isUserInteractionEnabled = true
            
            currentEraserSize = restoreEraserSize
        } else {
            restoreButton.backgroundColor = .secondarySystemBackground
            restoreButton.setTitleColor(UIColor(red: 0.20, green: 0.29, blue: 0.37, alpha: 1.0), for: .normal)
            
            if let recognizers = logoImageView.gestureRecognizers {
                for recognizer in recognizers {
                    logoImageView.removeGestureRecognizer(recognizer)
                }
            }
            logoImageView.isUserInteractionEnabled = false
        }
        
        // 選択状態をリセット
        clearRectangleSelection()
        clearPolygonSelection()
        clearFreeformSelection()
    }
    
    @objc private func handleRestorePan(_ gesture: UIPanGestureRecognizer) {
        guard isRestoreMode, currentImage != nil, originalImage != nil else { return }
        
        let location = gesture.location(in: logoImageView)
        
        if gesture.state == .began || gesture.state == .changed {
            // 復元処理を実行
            restoreArea(at: location, currentImage: currentImage!, originalImage: originalImage)
        }
    }
    
    private func restoreArea(at point: CGPoint, currentImage: UIImage, originalImage: UIImage) {
        let imageSize = currentImage.size
        let viewSize = logoImageView.bounds.size
        
        // 画像の表示スケールを計算
        let aspectRatio = min(viewSize.width / imageSize.width, viewSize.height / imageSize.height)
        let scaledImageSize = CGSize(width: imageSize.width * aspectRatio, height: imageSize.height * aspectRatio)
        let imageOrigin = CGPoint(
            x: (viewSize.width - scaledImageSize.width) / 2,
            y: (viewSize.height - scaledImageSize.height) / 2
        )
        
        // タップ位置を画像座標系に変換
        let imageX = (point.x - imageOrigin.x) / scaledImageSize.width * imageSize.width
        let imageY = (point.y - imageOrigin.y) / scaledImageSize.height * imageSize.height
        
        // 復元する円の半径を計算（画像座標系）
        let radius = currentEraserSize * imageSize.width / scaledImageSize.width
        
        UIGraphicsBeginImageContextWithOptions(imageSize, false, currentImage.scale)
        defer { UIGraphicsEndImageContext() }
        
        let context = UIGraphicsGetCurrentContext()!
        
        // 現在の画像を描画
        currentImage.draw(in: CGRect(origin: .zero, size: imageSize))
        
        // 復元する円の領域を設定
        let restoreRect = CGRect(
            x: imageX - radius,
            y: imageY - radius,
            width: radius * 2,
            height: radius * 2
        )
        
        // 元の画像から復元する部分を切り取る
        context.saveGState()
        context.addEllipse(in: restoreRect)
        context.clip()
        
        // 元の画像を描画（クリップされた領域のみ表示される）
        originalImage.draw(in: CGRect(origin: .zero, size: imageSize))
        context.restoreGState()
        
        if let processedImage = UIGraphicsGetImageFromCurrentImageContext() {
            self.currentImage = processedImage
            logoImageView.image = processedImage
        }
    }
    
    @objc private func resetButtonTapped() {
        currentImage = originalImage
        logoImageView.image = currentImage
        
        resetButtons()

        clearRectangleSelection()  // 清除四角形选择
        clearPolygonSelection() 
        clearFreeformSelection() 
        
        logoImageView.gestureRecognizers?.forEach { logoImageView.removeGestureRecognizer($0) }
        logoImageView.isUserInteractionEnabled = false
    }
    
    private func resetButtons() {
        isBackgroundRemoveMode = false
        isLargeEraserMode = false
        isSmallEraserMode = false
        isRectangleEraserMode = false
        isPolygonEraserMode = false
        isFreeformEraserMode = false
        isRestoreMode = false
        
        backgroundRemoveButton.backgroundColor = .secondarySystemBackground
        backgroundRemoveButton.setTitleColor(UIColor(red: 0.20, green: 0.29, blue: 0.37, alpha: 1.0), for: .normal)
        largeEraserButton.backgroundColor = .secondarySystemBackground
        largeEraserButton.setTitleColor(UIColor(red: 0.20, green: 0.29, blue: 0.37, alpha: 1.0), for: .normal)
        smallEraserButton.backgroundColor = .secondarySystemBackground
        smallEraserButton.setTitleColor(UIColor(red: 0.20, green: 0.29, blue: 0.37, alpha: 1.0), for: .normal)
        rectangleEraserButton.backgroundColor = .secondarySystemBackground
        rectangleEraserButton.setTitleColor(UIColor(red: 0.20, green: 0.29, blue: 0.37, alpha: 1.0), for: .normal)
        polygonEraserButton.backgroundColor = .secondarySystemBackground
        polygonEraserButton.setTitleColor(UIColor(red: 0.20, green: 0.29, blue: 0.37, alpha: 1.0), for: .normal)
        freeformEraserButton.backgroundColor = .secondarySystemBackground  // 自由曲線ボタンもリセット
        freeformEraserButton.setTitleColor(UIColor(red: 0.20, green: 0.29, blue: 0.37, alpha: 1.0), for: .normal)
        restoreButton.backgroundColor = .secondarySystemBackground  // 復元ボタンもリセット
        restoreButton.setTitleColor(UIColor(red: 0.20, green: 0.29, blue: 0.37, alpha: 1.0), for: .normal)
    }
    
    @objc private func saveButtonTapped() {
        guard let image = currentImage else { return }
        
        let activityIndicator = UIActivityIndicatorView(style: .large)
        activityIndicator.center = view.center
        activityIndicator.startAnimating()
        view.addSubview(activityIndicator)
        
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            
            let croppedImage = self.cropImageToContent(image)
            
            if let imageData = croppedImage.pngData(),
               let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first {
                
                // Logosディレクトリのパスを作成
                let logosDirectory = documentsDirectory.appendingPathComponent("Logos")
                
                // ディレクトリが存在しない場合は作成
                if !FileManager.default.fileExists(atPath: logosDirectory.path) {
                    do {
                        try FileManager.default.createDirectory(at: logosDirectory, withIntermediateDirectories: true)
                    } catch {
                        DispatchQueue.main.async {
                            activityIndicator.removeFromSuperview()
                            self.showSaveError(message: "ディレクトリの作成に失敗しました: \(error.localizedDescription)")
                        }
                        return
                    }
                }
                
                let fileName = "logo_\(Date().timeIntervalSince1970).png"
                let fileURL = logosDirectory.appendingPathComponent(fileName)
                
                do {
                    try imageData.write(to: fileURL)
                    
                    DispatchQueue.main.async {
                        activityIndicator.removeFromSuperview()
                        
                        let alertController = UIAlertController(
                            title: "保存成功",
                            message: "ロゴは保存されました",
                            preferredStyle: .alert
                        )
                        alertController.addAction(UIAlertAction(title: "OK", style: .default) { [weak self] _ in
                            if let self = self {
                                self.delegate?.logoEditorViewController(self, didFinishEditingImage: image, filePath: fileURL.path)
                                self.dismiss(animated: true)
                            }
                        })
                        self.present(alertController, animated: true)
                    }
                } catch {
                    DispatchQueue.main.async {
                        activityIndicator.removeFromSuperview()
                        self.showSaveError(message: error.localizedDescription)
                    }
                }
            }
        }
    }
    
    // エラーメッセージを表示するヘルパーメソッド
    private func showSaveError(message: String) {
        let alertController = UIAlertController(
            title: "保存失敗",
            message: message,
            preferredStyle: .alert
        )
        alertController.addAction(UIAlertAction(title: "OK", style: .default))
        self.present(alertController, animated: true)
    }
    
    private func cropImageToContent(_ image: UIImage) -> UIImage {
        guard let cgImage = image.cgImage else { return image }
        
        let width = cgImage.width
        let height = cgImage.height
        let bytesPerPixel = 4
        let bytesPerRow = bytesPerPixel * width
        let bitsPerComponent = 8
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let bitmapInfo = CGImageAlphaInfo.premultipliedLast.rawValue
        
        guard let context = CGContext(data: nil,
                                    width: width,
                                    height: height,
                                    bitsPerComponent: bitsPerComponent,
                                    bytesPerRow: bytesPerRow,
                                    space: colorSpace,
                                    bitmapInfo: bitmapInfo),
              let data = context.data else {
            return image
        }
        
        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))
        let pixels = data.assumingMemoryBound(to: UInt8.self)
        
        var minX = width
        var maxX = 0
        var minY = height
        var maxY = 0
        
        for y in 0..<height {
            for x in 0..<width {
                let offset = (y * bytesPerRow) + (x * bytesPerPixel)
                let alpha = pixels.advanced(by: offset)[3]
                
                if alpha > 0 {
                    minX = min(minX, x)
                    maxX = max(maxX, x)
                    minY = min(minY, y)
                    maxY = max(maxY, y)
                }
            }
        }
        
        if minX > maxX || minY > maxY {
            return image
        }
        
        let padding: CGFloat = 10
        minX = max(0, minX - Int(padding))
        maxX = min(width - 1, maxX + Int(padding))
        minY = max(0, minY - Int(padding))
        maxY = min(height - 1, maxY + Int(padding))
        
        let cropRect = CGRect(x: minX, y: minY, width: maxX - minX + 1, height: maxY - minY + 1)
        if let croppedCGImage = cgImage.cropping(to: cropRect) {
            return UIImage(cgImage: croppedCGImage, scale: image.scale, orientation: image.imageOrientation)
        }
        
        return image
    }
    
    @objc private func backButtonTapped() {
        dismiss(animated: true)
    }
}
