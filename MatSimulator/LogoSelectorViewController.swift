//
//  LogoSelectorViewController.swift
//  MatSimulator
//
//  Created by Clamour on 2025/03/07.
//

import UIKit

protocol LogoSelectorViewControllerDelegate: AnyObject {
    func logoSelectorViewController(_ controller: LogoSelectorViewController, didSelectImageNamed imageName: String)
}

class LogoSelectorViewController: UIViewController, UIImagePickerControllerDelegate, UINavigationControllerDelegate, UICollectionViewDelegate, UICollectionViewDataSource {
    
    weak var delegate: LogoSelectorViewControllerDelegate?
    
    private var currentMode: ButtonMode = .select
    
    private enum ButtonMode {
        case select
        case edit
    }
    
    private lazy var modeButtonsStack: UIStackView = {
        let stack = UIStackView()
        stack.translatesAutoresizingMaskIntoConstraints = false
        stack.axis = .horizontal
        stack.distribution = .fillEqually
        stack.spacing = 5
        return stack
    }()
    
    private lazy var selectButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("選択", for: .normal)
        button.backgroundColor = .systemBlue
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 5
        button.tag = 0
        return button
    }()
    
    private lazy var editButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("編集", for: .normal)
        button.backgroundColor = .systemGray5
        button.setTitleColor(.systemBlue, for: .normal)
        button.layer.cornerRadius = 5
        button.tag = 1
        return button
    }()
    
    private lazy var splitView: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()
    
    private lazy var leftPanel: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        view.backgroundColor = .systemBackground
        return view
    }()
    
    private lazy var rightPanel: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        view.backgroundColor = .systemBackground
        return view
    }()
    
    private lazy var cameraButton: UIButton = {
        let button = createOptionButton(title: "ロゴの撮影", iconName: "Camera")
        button.addTarget(self, action: #selector(cameraButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var photoLibraryButton: UIButton = {
        let button = createOptionButton(title: "カメラロール取込", iconName: "Folder")
        button.addTarget(self, action: #selector(photoLibraryButtonTapped), for: .touchUpInside)
        return button
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        
        selectButton.addTarget(self, action: #selector(modeButtonTapped(_:)), for: .touchUpInside)
        editButton.addTarget(self, action: #selector(modeButtonTapped(_:)), for: .touchUpInside)
        
        let closeButton = UIButton(type: .system)
        closeButton.setTitle("閉じる", for: .normal)
        closeButton.addTarget(self, action: #selector(dismissView), for: .touchUpInside)
        closeButton.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(closeButton)
        
        NSLayoutConstraint.activate([
            closeButton.topAnchor.constraint(equalTo: view.topAnchor, constant: 10),
            closeButton.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -10)
        ])
    }
    
    private func createOptionButton(title: String, iconName: String) -> UIButton {
        let button = UIButton(type: .system)
        button.translatesAutoresizingMaskIntoConstraints = false

        button.backgroundColor = .secondarySystemBackground
        button.layer.cornerRadius = 10
        
        let image = UIImage(named: iconName)?.withRenderingMode(.alwaysOriginal)
        button.setImage(image, for: .normal)
        
        button.setTitle(title, for: .normal)
        button.tintColor = UIColor(red: 0.20, green: 0.29, blue: 0.37, alpha: 1.0)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        
        button.contentHorizontalAlignment = .left
        button.imageEdgeInsets = UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 0)
        button.titleEdgeInsets = UIEdgeInsets(top: 0, left: 24, bottom: 0, right: 0)
        
        button.heightAnchor.constraint(equalToConstant: 52).isActive = true
        
        return button
    }

    private lazy var thumbnailCollectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = 10
        layout.minimumInteritemSpacing = 10
        layout.itemSize = CGSize(width: 100, height: 100)
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.translatesAutoresizingMaskIntoConstraints = false
        collectionView.backgroundColor = .clear
        collectionView.register(LogoThumbnailCell.self, forCellWithReuseIdentifier: "LogoThumbnailCell")
        collectionView.delegate = self
        collectionView.dataSource = self
        return collectionView
    }()

    private var thumbnails: [UIImage] = []
    private var thumbnailPaths: [String] = []
    
    private func setupUI() {
        view.backgroundColor = .white
        
        view.addSubview(splitView)
        splitView.addSubview(leftPanel)
        splitView.addSubview(rightPanel)
        
        leftPanel.addSubview(cameraButton)
        leftPanel.addSubview(photoLibraryButton)
        view.addSubview(modeButtonsStack)
        
        modeButtonsStack.addArrangedSubview(selectButton)
        modeButtonsStack.addArrangedSubview(editButton)

        rightPanel.addSubview(thumbnailCollectionView)
        
        let placeholderLabel = UILabel()
        placeholderLabel.text = "ロゴ画像がここに表示されます"
        placeholderLabel.textAlignment = .center
        placeholderLabel.textColor = .secondaryLabel
        placeholderLabel.translatesAutoresizingMaskIntoConstraints = false
        placeholderLabel.tag = 100
        
        rightPanel.addSubview(placeholderLabel)
        
        NSLayoutConstraint.activate([
            splitView.topAnchor.constraint(equalTo: view.topAnchor),
            splitView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            splitView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            splitView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            
            leftPanel.topAnchor.constraint(equalTo: splitView.topAnchor),
            leftPanel.bottomAnchor.constraint(equalTo: splitView.bottomAnchor, constant: -50),
            leftPanel.leadingAnchor.constraint(equalTo: splitView.leadingAnchor),
            leftPanel.widthAnchor.constraint(equalToConstant: 210),
            
            rightPanel.topAnchor.constraint(equalTo: splitView.topAnchor),
            rightPanel.bottomAnchor.constraint(equalTo: splitView.bottomAnchor),
            rightPanel.leadingAnchor.constraint(equalTo: leftPanel.trailingAnchor),
            rightPanel.trailingAnchor.constraint(equalTo: splitView.trailingAnchor),
            
            cameraButton.topAnchor.constraint(equalTo: leftPanel.topAnchor, constant: 20),
            cameraButton.leadingAnchor.constraint(equalTo: leftPanel.leadingAnchor, constant: 10),
            cameraButton.trailingAnchor.constraint(equalTo: leftPanel.trailingAnchor, constant: -10),
            
            photoLibraryButton.topAnchor.constraint(equalTo: cameraButton.bottomAnchor, constant: 10),
            photoLibraryButton.leadingAnchor.constraint(equalTo: leftPanel.leadingAnchor, constant: 10),
            photoLibraryButton.trailingAnchor.constraint(equalTo: leftPanel.trailingAnchor, constant: -10),
            
            modeButtonsStack.topAnchor.constraint(equalTo: leftPanel.bottomAnchor, constant: 10),
            modeButtonsStack.leadingAnchor.constraint(equalTo: leftPanel.leadingAnchor, constant: 10),
            modeButtonsStack.trailingAnchor.constraint(equalTo: leftPanel.trailingAnchor, constant: -10),
            modeButtonsStack.heightAnchor.constraint(equalToConstant: 30),
            
            thumbnailCollectionView.topAnchor.constraint(equalTo: rightPanel.topAnchor, constant: 20),
            thumbnailCollectionView.leadingAnchor.constraint(equalTo: rightPanel.leadingAnchor, constant: 20),
            thumbnailCollectionView.trailingAnchor.constraint(equalTo: rightPanel.trailingAnchor, constant: -20),
            thumbnailCollectionView.bottomAnchor.constraint(equalTo: rightPanel.bottomAnchor, constant: -20),
            
            placeholderLabel.centerXAnchor.constraint(equalTo: rightPanel.centerXAnchor),
            placeholderLabel.centerYAnchor.constraint(equalTo: rightPanel.centerYAnchor)
        ])
        
        loadSavedImages()
    }
    
    private func loadSavedImages() {
        guard let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return
        }
        
        let logosDirectory = documentsDirectory.appendingPathComponent("Logos")
        
        if !FileManager.default.fileExists(atPath: logosDirectory.path) {
            do {
                try FileManager.default.createDirectory(at: logosDirectory, withIntermediateDirectories: true, attributes: nil)
            } catch {
                print("Error creating Logos directory: \(error)")
                return
            }
        }
        
        do {
            let fileURLs = try FileManager.default.contentsOfDirectory(at: logosDirectory, includingPropertiesForKeys: nil)
            let imageURLs = fileURLs.filter { $0.pathExtension.lowercased() == "png" }
            
            for imageURL in imageURLs {
                if let image = UIImage(contentsOfFile: imageURL.path) {
                    thumbnails.append(image)
                    thumbnailPaths.append(imageURL.path)
                }
            }
            
            if !thumbnails.isEmpty {
                if let placeholderLabel = rightPanel.viewWithTag(100) as? UILabel {
                    placeholderLabel.isHidden = true
                }
                thumbnailCollectionView.reloadData()
            }
        } catch {
            print("Error loading saved images: \(error)")
        }
    }
    
    @objc private func dismissView() {
        dismiss(animated: true)
    }
    
    
    @objc private func cameraButtonTapped() {
        if UIImagePickerController.isSourceTypeAvailable(.camera) {
            let imagePicker = UIImagePickerController()
            imagePicker.delegate = self
            imagePicker.sourceType = .camera
            imagePicker.allowsEditing = false
            present(imagePicker, animated: true)
        } else {
            let alert = UIAlertController(
                title: "カメラが利用できません",
                message: "このデバイスではカメラが使用できないか、アクセス権限がありません。",
                preferredStyle: .alert
            )
            alert.addAction(UIAlertAction(title: "OK", style: .default))
            present(alert, animated: true)
        }
    }
    
    @objc private func photoLibraryButtonTapped() {
        let imagePicker = UIImagePickerController()
        imagePicker.delegate = self
        imagePicker.sourceType = .photoLibrary
        imagePicker.allowsEditing = false
        present(imagePicker, animated: true)
    }
    
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        picker.dismiss(animated: true) { [weak self] in
            guard let self = self else { return }
            
            if let selectedImage = info[.originalImage] as? UIImage {
                let resizedImage = self.resizeImage(selectedImage, maxDimension: 800)
                
                let logoEditorVC = LogoEditorViewController(image: resizedImage)
                logoEditorVC.delegate = self
                logoEditorVC.modalPresentationStyle = .fullScreen
                self.present(logoEditorVC, animated: true)
            }
        }
    }
    
    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true)
    }

    private func resizeImage(_ image: UIImage, maxDimension: CGFloat) -> UIImage {
        let originalSize = image.size
    
        if originalSize.width <= maxDimension && originalSize.height <= maxDimension {
            return image
        }

        let widthRatio = maxDimension / originalSize.width
        let heightRatio = maxDimension / originalSize.height
        let scaleFactor = min(widthRatio, heightRatio)

        let newWidth = originalSize.width * scaleFactor
        let newHeight = originalSize.height * scaleFactor
        let newSize = CGSize(width: newWidth, height: newHeight)

        UIGraphicsBeginImageContextWithOptions(newSize, false, 1.0)
        image.draw(in: CGRect(origin: .zero, size: newSize))
        let newImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        return newImage ?? image
    }
    
    
    @objc private func modeButtonTapped(_ sender: UIButton) {
        currentMode = sender.tag == 0 ? .select : .edit
        selectButton.backgroundColor = currentMode == .select ? .systemBlue : .systemGray5
        selectButton.setTitleColor(currentMode == .select ? .white : .systemBlue, for: .normal)
        editButton.backgroundColor = currentMode == .edit ? .systemBlue : .systemGray5
        editButton.setTitleColor(currentMode == .edit ? .white : .systemBlue, for: .normal)
    }

    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return thumbnails.count
    }
    
    // コレクションビューのセルを設定するメソッド
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "LogoThumbnailCell", for: indexPath) as! LogoThumbnailCell
        cell.configure(with: thumbnails[indexPath.item])
        return cell
    }

    // コレクションビューのアイテムが選択された時の処理
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if currentMode == .select {
            // 選択モードの場合：選択したロゴを親ビューに通知して画面を閉じる
            delegate?.logoSelectorViewController(self, didSelectImageNamed: thumbnailPaths[indexPath.item])
            dismiss(animated: true)
        } else {
            // 編集モードの場合：選択したロゴをエディタで開く
            let logoEditorVC = LogoEditorViewController(image: thumbnails[indexPath.item])
            logoEditorVC.delegate = self
            logoEditorVC.modalPresentationStyle = .fullScreen
            present(logoEditorVC, animated: true)
        }
    }
}

// ロゴエディタからの結果を処理するデリゲート実装
extension LogoSelectorViewController: LogoEditorViewControllerDelegate {
    // 編集完了時の処理（古いメソッド - 使用されていない）
    func logoEditorViewController(_ controller: LogoEditorViewController, didFinishEditingImage image: UIImage) {
        // 実装なし（非推奨メソッド）
    }
    
    // 編集完了時の処理（ファイルパス付き）
    func logoEditorViewController(_ controller: LogoEditorViewController, didFinishEditingImage image: UIImage, filePath: String) {
        // 編集済みの画像をサムネイルリストに追加
        thumbnails.append(image)
        thumbnailPaths.append(filePath)
        
        // プレースホルダーラベルを非表示にする
        if let placeholderLabel = rightPanel.viewWithTag(100) as? UILabel {
            placeholderLabel.isHidden = true
        }
        
        // コレクションビューを更新
        thumbnailCollectionView.reloadData()
    }
}


