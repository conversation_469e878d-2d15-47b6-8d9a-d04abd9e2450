import Foundation
import UIKit

// 家庭用マットに設定できるラベルの位置情報保持構造体
struct HomeMatPositionInfo {
    /// 文字を設定できるかどうか
    let labelEnable: Bool

    /// 文字位置識別子（例："A-1", "B-2", "C-3"）
    let labelPosition: String

    /// 文字位置：x 座標
    let x: Int

    /// 文字位置：y 座標
    let y: Int

    /// 文字配置領域：幅
    let width: Int

    /// 文字配置領域：高さ
    let height: Int

    /// 配置方法：垂直方向（0:上，1:中央，2:下）
    let vertical: Int

    /// 配置方法：水平方法（0:左，1:中央，2:右）
    let horizontal: Int

    /// 指定された位置情報から実際の表示位置を計算する
    /// - Parameters:
    ///   - matSize: マットのサイズ
    ///   - textSize: テキストのサイズ
    /// - Returns: 表示位置（正規化座標系）
    func calculatePosition(matSize: SIMD2<Float>, textSize: SIMD2<Float>) -> SIMD2<Float> {
        print("aqaz calculatePosition - position: \(labelPosition), vertical: \(vertical), horizontal: \(horizontal)")

        // 垂直方向の位置調整
        var yOffset: Float = 0

        // マットの高さに基づいて上中下の位置を計算
        // 固定のオフセット値を使用
        let topPosition: Float = 0.7     // 上部はマットの上部に固定位置（より高く）
        let middlePosition: Float = 0.0   // 中部はマットの中央
        let bottomPosition: Float = -0.7  // 下部はマットの下部に固定位置（より低く）

        print("aqaz calculatePosition - matSize: \(matSize), textSize: \(textSize)")

        switch vertical {
        case 0: // A 行 - 上部
            yOffset = topPosition
            print("aqaz calculatePosition - using top position: \(topPosition)")
        case 1: // B 行 - 中部
            yOffset = middlePosition
            print("aqaz calculatePosition - using middle position: \(middlePosition)")
        case 2: // C 行 - 下部
            yOffset = bottomPosition
            print("aqaz calculatePosition - using bottom position: \(bottomPosition)")
        default:
            yOffset = 0.0
        }

        // 水平方向の位置調整
        var xOffset: Float = 0

        // マットの幅に基づいて左中右の位置を計算
        let matWidth = matSize.x
        let leftPosition: Float = -matWidth * 0.3    // 左はマットの左から 30% の位置
        let centerPosition: Float = 0.0             // 中央はマットの中央
        let rightPosition: Float = matWidth * 0.3   // 右はマットの右から 30% の位置

        switch horizontal {
        case 0: // 1 - 左
            xOffset = leftPosition
            print("aqaz calculatePosition - using left position: \(leftPosition)")
        case 2: // 3 - 右
            xOffset = rightPosition
            print("aqaz calculatePosition - using right position: \(rightPosition)")
        default: // 2 - 中央
            xOffset = centerPosition
            print("aqaz calculatePosition - using center position: \(centerPosition)")
        }

        print("aqaz calculatePosition - final xOffset: \(xOffset), yOffset: \(yOffset)")
        return SIMD2<Float>(xOffset, yOffset)
    }
    
    // MatInfo.swift  ── HomeMatPositionInfo
    func calculateWorldPosition(bounds: (minX: Float, minY: Float, maxX: Float, maxY: Float),
                                textNormalizedSize textSize: SIMD2<Float>,
                                marginPx: Float = 0,
                                viewSize: CGSize) -> SIMD2<Float> {
        


        // 10 px → NDC
           let px2ndcY = 2.0 / Float(viewSize.height)
           let px2ndcX = 2.0 / Float(viewSize.width)
           let marginY = marginPx * px2ndcY
           let marginX = marginPx * px2ndcX

           let halfH = textSize.y * 0.5
           let halfW = textSize.x * 0.5        // ← 半幅
        
        print(String(format:"📐 bounds=(%.3f,%.3f)-(%.3f,%.3f)  textHalf=(%.3f,%.3f)  marginY=%.4f",
              bounds.minX,bounds.minY,bounds.maxX,bounds.maxY,halfW,halfH,marginY))

        // ➋ 縦方向の位置
        let y: Float
        switch vertical {               // ← あなたの元の enum
        case 0:
            y = bounds.maxY - halfH - marginY   // ✅ halfH + margin を減算
        case 2:
            y = bounds.minY + halfH + marginY   // ✅ 加算 halfH + margin
        default:
            y = (bounds.maxY + bounds.minY) * 0.5
        }

        // ➌ 横方向の位置
        let x: Float
        switch horizontal {
        case 0:
            x = bounds.minX + halfW + marginX
        case 2:
            x = bounds.maxX - halfW - marginX
        default:
            x = (bounds.minX + bounds.maxX) * 0.5
        }

        return SIMD2<Float>(x, y)
    }

    /// テキスト配置のアライメントを取得
    var textAlignment: NSTextAlignment {
        switch horizontal {
        case 0: return .left
        case 2: return .right
        default: return .center
        }
    }
}
