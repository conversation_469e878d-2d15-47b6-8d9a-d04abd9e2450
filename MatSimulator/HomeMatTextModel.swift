import Foundation



// String extension for character type checking
private extension String {
    var containsKanji: <PERSON>ol {
        return self.range(of: "^[\\p{Han}]+$", options: .regularExpression) != nil
    }
    
    var containsHiragana: Bool {
        return self.range(of: "^[\\p{Hiragana}]+$", options: .regularExpression) != nil
    }
    
    var containsKatakana: Bool {
        return self.range(of: "^[\\p{Katakana}]+$", options: .regularExpression) != nil
    }
}
