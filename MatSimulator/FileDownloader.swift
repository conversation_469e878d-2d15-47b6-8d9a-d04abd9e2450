//
//  FileDownloader.swift
//  MatSimulator
//
//  Created by Clamour on 2025/02/10.
//

import Foundation
import CommonCrypto
import UIKit

class FileDownloader {
    let filehostURL = "https://www5.duskin-net.com/omm-tablet/synchronizedTabletMasterAction.do"
    var tmpListFile: String
    var imageListFile: String
    private var matRootURL: URL
    var matRootPath: String
    var logoRootPath: String
    let fileMng = FileManager.default
    
    private var lastSyncChecksum: String?
    
    init() {
        let srcDirURL = URL(fileURLWithPath: NSHomeDirectory(), isDirectory: true)
        
        let tmpDirURL = srcDirURL
            .appendingPathComponent(DIRPATH_TMP, isDirectory: true)
            .appendingPathComponent("fileList.csv")
        tmpListFile = tmpDirURL.path
        
        let stdMatDirURL = srcDirURL
            .appendingPathComponent(DIRPATH_STDMAT, isDirectory: true)
        imageListFile = stdMatDirURL
            .appendingPathComponent("imagefileList.csv").path
        
        self.matRootURL = srcDirURL
            .appendingPathComponent(DIRPATH_DOC, isDirectory: true)
            .standardized
        self.matRootPath = matRootURL.path + "/"
        
        logoRootPath = matRootPath
        
        createNecessaryDirectories()
    }
    
    private func createNecessaryDirectories() {
            do {
                try fileMng.createDirectory(
                    at: URL(fileURLWithPath: tmpListFile).deletingLastPathComponent(),
                    withIntermediateDirectories: true,
                    attributes: nil
                )
                
                try fileMng.createDirectory(
                    at: URL(fileURLWithPath: imageListFile).deletingLastPathComponent(),
                    withIntermediateDirectories: true,
                    attributes: nil
                )
                
                try fileMng.createDirectory(
                    at: matRootURL,
                    withIntermediateDirectories: true,
                    attributes: nil
                )
            } catch {
                print("create folder error: \(error)")
            }
        }
    
    func downloadImgFile() async throws -> Bool {
        print(" start download...")
        
        return try await withCheckedThrowingContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                do {
                    self.downloadFileListCSV()
                    
                    if let checksum = self.getFileChecksum(path: self.tmpListFile),
                       let lastChecksum = self.lastSyncChecksum,
                       checksum == lastChecksum {
                        continuation.resume(returning: true)
                        return
                    }
                    
                    guard self.fileMng.fileExists(atPath: self.tmpListFile) else {
                        throw NSError(domain: "FileCheck", code: -1, userInfo: [NSLocalizedDescriptionKey: "リストファイルが欠落しています"])
                    }

                    Task {
                        do {
                            let success = try await self.compareVersionFile()
                            self.lastSyncChecksum = self.getFileChecksum(path: self.imageListFile)
                            continuation.resume(returning: success)
                        } catch {
                            continuation.resume(throwing: error)
                        }
                    }
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    private func compareVersionFile() async throws -> Bool {
        return try await withCheckedThrowingContinuation { continuation in
            Task.detached(priority: .userInitiated) { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: NSError(domain: "SelfDeallocated", code: -1))
                    return
                }
                
                do {
                    let newContent = try String(contentsOfFile: self.tmpListFile, encoding: .utf8)
                    let newFiles = self.parseCSVToArrays(content: newContent)
                    
                    if let localContent = try? String(contentsOfFile: self.imageListFile, encoding: .utf8),
                       self.sha256(data: Data(localContent.utf8)) == self.sha256(data: Data(newContent.utf8)) {
                        continuation.resume(returning: true)
                        return
                    }
                    
                    let (localFiles, localError) = self.loadLocalVersionFile()
                    
                    if localError != nil {
                        try await self.fullSync(newFiles: newFiles, newContent: newContent)
                    } else {
                        try await self.incrementalSync(
                            localFiles: localFiles,
                            newFiles: newFiles,
                            newContent: newContent
                        )
                    }
                    
                    continuation.resume(returning: true)
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }

    private func loadLocalVersionFile() -> (files: (fileNames: [String], versions: [String]), error: Error?) {
        do {
            let content = try String(contentsOfFile: imageListFile, encoding: .utf8)
            return (parseCSVToArrays(content: content), nil)
        } catch {
            return (([], []), error)
        }
    }

    private func fullSync(
        newFiles: (fileNames: [String], versions: [String]),
        newContent: String
    ) async throws {
        return try await withCheckedThrowingContinuation { continuation in
            let group = DispatchGroup()
            var errors = [String: Error]()
            var retryExhaustedError: Error? = nil
            
            group.enter()
            self.downloadAllFiles(fileNames: newFiles.fileNames) { results in
                defer { group.leave() }
                
                for (fileName, result) in results {
                    if case .failure(let error) = result {
                        errors[fileName] = error
                        
                        if let nsError = error as? NSError, nsError.domain == "RetryExhausted" {
                            retryExhaustedError = error
                            break
                        }
                    }
                }
                
                if let error = retryExhaustedError {
                    continuation.resume(throwing: error)
                    return
                }
                
                if !errors.isEmpty {
                    print("download error: \(errors)")
                }
                
                do {
                    try self.replaceFileListCSV(content: newContent)
                    continuation.resume()
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }

    private var lastDownloadState: (files: [(String, String)], downloadedCount: Int)? {
        get {
            return loadDownloadState()
        }
        set {
            saveDownloadState(newValue)
        }
    }
    
    private var downloadStateFilePath: String {
        return URL(fileURLWithPath: NSHomeDirectory())
            .appendingPathComponent(DIRPATH_TMP)
            .appendingPathComponent("downloadState.json")
            .path
    }
    
    private func saveDownloadState(_ state: (files: [(String, String)], downloadedCount: Int)?) {
        guard let state = state else {
            try? FileManager.default.removeItem(atPath: downloadStateFilePath)
            return
        }
        
        let stateDict: [String: Any] = [
            "files": state.files.map { ["fileName": $0.0, "version": $0.1] },
            "downloadedCount": state.downloadedCount
        ]
        
        do {
            let data = try JSONSerialization.data(withJSONObject: stateDict, options: [])
            try data.write(to: URL(fileURLWithPath: downloadStateFilePath), options: .atomic)
            print("ダウンロード状態を保存しました: \(state.downloadedCount)/\(state.files.count)")
        } catch {
            print("ダウンロード状態の保存に失敗しました: \(error)")
        }
    }
    
    private func loadDownloadState() -> (files: [(String, String)], downloadedCount: Int)? {
        guard FileManager.default.fileExists(atPath: downloadStateFilePath),
              let data = try? Data(contentsOf: URL(fileURLWithPath: downloadStateFilePath)),
              let dict = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let filesArray = dict["files"] as? [[String: String]],
              let downloadedCount = dict["downloadedCount"] as? Int else {
            return nil
        }
        
        let files = filesArray.compactMap { dict -> (String, String)? in
            guard let fileName = dict["fileName"], let version = dict["version"] else {
                return nil
            }
            return (fileName, version)
        }
        
        print("保存されたダウンロード状態を読み込みました: \(downloadedCount)/\(files.count)")
        return (files, downloadedCount)
    }
    
    private func incrementalSync(
        localFiles: (fileNames: [String], versions: [String]),
        newFiles: (fileNames: [String], versions: [String]),
        newContent: String
    ) async throws {
        return try await withCheckedThrowingContinuation { continuation in
            let dispatchGroup = DispatchGroup()
            let operationQueue = OperationQueue()
            operationQueue.maxConcurrentOperationCount = 5
            var needUpdateVersion = false
            var syncErrors = [Error]()
            var isSyncAborted = false
            
            let filesToDownload = newFiles.fileNames.enumerated().compactMap { index, fileName -> (String, String)? in
                guard let localIndex = localFiles.fileNames.firstIndex(of: fileName) else {
                    return (fileName, newFiles.versions[index])
                }
                
                let localVersion = localFiles.versions[localIndex]
                let remoteVersion = newFiles.versions[index]
                let fileURL = self.matRootURL.appendingPathComponent(fileName)
                let fileExists = self.fileMng.fileExists(atPath: fileURL.path)
                
                return (remoteVersion != localVersion || !fileExists) ? (fileName, remoteVersion) : nil
            }
            
            var filesToProcess = filesToDownload
            var startIndex = 0
            
            if let lastState = self.lastDownloadState, 
               lastState.files.map({ $0.0 }) == filesToDownload.map({ $0.0 }) {
                startIndex = lastState.downloadedCount
            } else {
                self.lastDownloadState = (filesToDownload, 0)
            }
            
            filesToProcess = Array(filesToDownload.suffix(from: startIndex))
            var downloadedCount = startIndex
            
            filesToProcess.forEach { fileName, version in
                dispatchGroup.enter()
                operationQueue.addOperation {
                    if isSyncAborted {
                        dispatchGroup.leave()
                        return
                    }
                    
                    self.downloadFile(fileName) { result in
                        if isSyncAborted {
                            dispatchGroup.leave()
                            return
                        }
                        
                        switch result {
                        case .success:
                            needUpdateVersion = true
                            downloadedCount += 1
                            self.lastDownloadState = (filesToDownload, downloadedCount)
                        case .failure(let error):
                            syncErrors.append(error)
                            
                            if let nsError = error as? NSError, 
                               nsError.domain == "RetryExhausted" {
                                isSyncAborted = true
                                operationQueue.cancelAllOperations()
                                
                                self.lastDownloadState = (filesToDownload, downloadedCount)
                                
                                continuation.resume(throwing: error)
                                return
                            }
                        }
                        
                        dispatchGroup.leave()
                    }
                }
            }
            
            dispatchGroup.notify(queue: .global()) {
                if !isSyncAborted {
                    do {
                        if needUpdateVersion {
                            try self.replaceFileListCSV(content: newContent)
                        }
                        
                        if !syncErrors.isEmpty {
                            throw NSError(
                                domain: "IncrementalSyncError",
                                code: -1,
                                userInfo: ["errors": syncErrors]
                            )
                        }
                        
                        if let firstFile = newFiles.fileNames.first, firstFile.contains("ダスキン色情報") {
                            let filesToDelete = localFiles.fileNames.filter { !newFiles.fileNames.contains($0) }
                            self.batchDeleteFiles(filesToDelete)
                        }
                        
                        self.lastDownloadState = nil
                        
                        continuation.resume()
                    } catch {
                        continuation.resume(throwing: error)
                    }
                }
            }
        }
    }
    
    private func getFileChecksum(path: String) -> String? {
        guard let data = try? Data(contentsOf: URL(fileURLWithPath: path)) else { return nil }
        return sha256(data: data)
    }
    
    private func sha256(data: Data) -> String {
        var hash = [UInt8](repeating: 0, count: Int(CC_SHA256_DIGEST_LENGTH))
        data.withUnsafeBytes {
            _ = CC_SHA256($0.baseAddress, CC_LONG(data.count), &hash)
        }
        return Data(hash).map { String(format: "%02hhx", $0) }.joined()
    }

    private func batchDeleteFiles(_ files: [String]) {
        let queue = DispatchQueue(label: "com.example.deleteQueue", attributes: .concurrent)
        let group = DispatchGroup()
        
        files.forEach { file in
            group.enter()
            queue.async {
                defer { group.leave() }
                self.deleteFile(file)
            }
        }
        
        group.wait()
    }
    
    private func parseCSVToArrays(content: String?) -> (fileNames: [String], versions: [String]) {
        var fileNames = [String](), versions = [String]()
        content?.components(separatedBy: .newlines).forEach { line in
            let parts = line.components(separatedBy: ",")
            if parts.count == 2 {
                fileNames.append(parts[0])
                versions.append(parts[1])
            }
        }
        return (fileNames, versions)
    }
    
    private func deleteFile(_ fileName: String) {
        try? fileMng.removeItem(atPath: "\(matRootPath)\(fileName)")
    }
    
    private func replaceFileListCSV(content: String) throws {
        let targetURL = URL(fileURLWithPath: imageListFile)
        try fileMng.createDirectory(
            at: targetURL.deletingLastPathComponent(),
            withIntermediateDirectories: true,
            attributes: nil
        )
        
        try content.write(
            to: targetURL,
            atomically: true,
            encoding: .utf8
        )
    }
    
    private func downloadFileListCSV() {
        let semaphore = DispatchSemaphore(value: 0)
        let url = URL(string: filehostURL)!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.httpBody = "syncType=0".data(using: .utf8)
        var downloadData: Data?
        let config = URLSessionConfiguration.ephemeral
        let session = URLSession(configuration: config)
        
        let task = session.dataTask(with: request) { data, response, error in
            defer { semaphore.signal() }
            
            if let error = error {
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse,
                  httpResponse.statusCode == 200 else {
                print("Error: \((response as? HTTPURLResponse)?.statusCode ?? -1)")
                return
            }
            
            guard error == nil,
                  let httpResponse = response as? HTTPURLResponse,
                  httpResponse.statusCode == 200,
                  let data = data else { return }
            
            downloadData = data
        }
        task.resume()
        semaphore.wait()
        
        guard let downloadData = downloadData else {
            print("Error")
            return
        }
        
        if !fileMng.fileExists(atPath: tmpListFile) {
            fileMng.createFile(atPath: tmpListFile, contents: Data(), attributes: nil)
        }
        if let fileHandle = FileHandle(forWritingAtPath: tmpListFile) {
            fileHandle.write(downloadData)
            fileHandle.synchronizeFile()
            fileHandle.closeFile()
        }
    }
    
    private func downloadAllFiles(fileNames: [String], completion: @escaping ([(String, Result<Void, Error>)]) -> Void) {
        let dispatchGroup = DispatchGroup()
        let serialQueue = DispatchQueue(label: "com.example.downloadResults")
        var results = [(String, Result<Void, Error>)]()
        let maxConcurrent = 5
        let semaphore = DispatchSemaphore(value: maxConcurrent)
        var isSyncAborted = false
        
        for fileName in fileNames {
            if isSyncAborted {
                break
            }
            
            dispatchGroup.enter()
            semaphore.wait()
            
            downloadFile(fileName) { result in
                serialQueue.async {
                    results.append((fileName, result))
                    
                    if case .failure(let error) = result,
                       let nsError = error as? NSError,
                       nsError.domain == "RetryExhausted" {
                        isSyncAborted = true
                    }
                    
                    semaphore.signal()
                    dispatchGroup.leave()
                }
            }
        }
        
        dispatchGroup.notify(queue: .main) {
            completion(results)
        }
    }
    
    private func downloadFile(_ fileName: String, retryCount: Int = 5, completion: @escaping (Result<Void, Error>) -> Void) {
        print("downloadFile file: \(fileName), retryCount: \(retryCount)")
        
        if retryCount <= 0 {
            let exhaustedError = NSError(
                domain: "RetryExhausted", 
                code: -4, 
                userInfo: [
                    NSLocalizedDescriptionKey: "ファイル「\(fileName)」のダウンロードに失敗しました。ネットワーク接続を確認してください。",
                    "fileName": fileName
                ]
            )
            
            DispatchQueue.main.async {
                // カスタムエラー表示用のビューコントローラーを作成
                let errorVC = UIViewController()
                errorVC.modalPresentationStyle = .formSheet
                // サイズをさらに大きくする
                errorVC.preferredContentSize = CGSize(width: 600, height: 700)
                
                // メインビューの設定
                let mainView = UIView()
                mainView.backgroundColor = .systemBackground
                mainView.layer.cornerRadius = 12
                mainView.clipsToBounds = true
                errorVC.view = mainView
                
                // スクロールビューを追加して、コンテンツが大きい場合にスクロールできるようにする
                let scrollView = UIScrollView()
                scrollView.translatesAutoresizingMaskIntoConstraints = false
                mainView.addSubview(scrollView)
                
                // コンテンツビューをスクロールビューに追加
                let contentView = UIView()
                contentView.translatesAutoresizingMaskIntoConstraints = false
                scrollView.addSubview(contentView)
                
                // エラータイトルラベル
                let titleLabel = UILabel()
                titleLabel.text = "ネットワークエラー"
                titleLabel.font = UIFont.boldSystemFont(ofSize: 24)
                titleLabel.textAlignment = .center
                titleLabel.textColor = .systemRed
                
                // 詳細なエラー情報を取得
                let originalError = exhaustedError.userInfo["NSUnderlyingError"] as? NSError
                let errorDomain = originalError?.domain ?? exhaustedError.domain
                let errorCode = originalError?.code ?? exhaustedError.code
                let errorDescription = originalError?.localizedDescription ?? exhaustedError.localizedDescription
                
                // エラーメッセージラベル
                let messageLabel = UILabel()
                messageLabel.text = "ダウンロードに失敗しました。ネットワーク接続を確認してください。"
                messageLabel.font = UIFont.systemFont(ofSize: 18)
                messageLabel.textAlignment = .center
                messageLabel.numberOfLines = 0
                
                // エラー詳細ラベル（技術的な情報）
                let detailLabel = UILabel()
                detailLabel.text = """
                エラードメイン: \(errorDomain)
                エラーコード: \(errorCode)
                説明: \(errorDescription)
                URL: \(self.filehostURL)
                再試行回数: 5回（すべて失敗）
                """
                detailLabel.font = UIFont.systemFont(ofSize: 16)
                detailLabel.textAlignment = .left
                detailLabel.numberOfLines = 0
                
                // ファイル名ラベル
                let fileNameLabel = UILabel()
                fileNameLabel.text = "ダウンロード失敗ファイル: \(fileName)"
                fileNameLabel.font = UIFont.boldSystemFont(ofSize: 16)
                fileNameLabel.textAlignment = .left
                fileNameLabel.numberOfLines = 0
                fileNameLabel.textColor = .systemOrange
                
                // 詳細なログ情報を表示するテキストビュー
                let logTextView = UITextView()
                logTextView.isEditable = false
                logTextView.isSelectable = true
                logTextView.font = UIFont.monospacedSystemFont(ofSize: 12, weight: .regular)
                logTextView.backgroundColor = UIColor.systemGray6
                logTextView.layer.cornerRadius = 8
                logTextView.textContainerInset = UIEdgeInsets(top: 8, left: 8, bottom: 8, right: 8)
                
                // エラーログの詳細情報を生成
                var logText = "エラーログ詳細:\n\n"
                logText += "Error Domain=\(errorDomain) Code=\(errorCode)\n"
                logText += "Description=\(errorDescription)\n\n"
                
                // UserInfoの詳細を追加
                if let originalError = originalError {
                    logText += "UserInfo={\n"
                    for (key, value) in originalError.userInfo {
                        logText += "  \(key): \(value)\n"
                    }
                    logText += "}\n\n"
                } else {
                    for (key, value) in exhaustedError.userInfo {
                        logText += "  \(key): \(value)\n"
                    }
                    logText += "}\n\n"
                }
                
                // ネットワーク接続情報を追加
                logText += "ネットワーク接続情報:\n"
                logText += "URL: \(self.filehostURL)\n"
                logText += "タイムアウト設定: \(0.1)秒\n"
                logText += "リクエストパラメータ: syncType=1&syncFile=\(fileName)\n\n"
                
                logTextView.text = logText
                
                // OKボタン
                let okButton = UIButton(type: .system)
                okButton.setTitle("閉じる", for: .normal)
                okButton.titleLabel?.font = UIFont.boldSystemFont(ofSize: 18)
                okButton.backgroundColor = .systemBlue
                okButton.setTitleColor(.white, for: .normal)
                okButton.layer.cornerRadius = 8
                
                // Auto Layout制約を使用するための設定
                [titleLabel, messageLabel, fileNameLabel, detailLabel, logTextView, okButton].forEach {
                    $0.translatesAutoresizingMaskIntoConstraints = false
                    contentView.addSubview($0)
                }
                
                // スクロールビューの制約
                NSLayoutConstraint.activate([
                    scrollView.topAnchor.constraint(equalTo: mainView.topAnchor),
                    scrollView.leadingAnchor.constraint(equalTo: mainView.leadingAnchor),
                    scrollView.trailingAnchor.constraint(equalTo: mainView.trailingAnchor),
                    scrollView.bottomAnchor.constraint(equalTo: mainView.bottomAnchor),
                    
                    contentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
                    contentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
                    contentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
                    contentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
                    contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor)
                ])
                
                // コンテンツの制約
                NSLayoutConstraint.activate([
                    titleLabel.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
                    titleLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
                    titleLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
                    
                    messageLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 20),
                    messageLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
                    messageLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
                    
                    fileNameLabel.topAnchor.constraint(equalTo: messageLabel.bottomAnchor, constant: 20),
                    fileNameLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
                    fileNameLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
                    
                    detailLabel.topAnchor.constraint(equalTo: fileNameLabel.bottomAnchor, constant: 20),
                    detailLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
                    detailLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
                    
                    logTextView.topAnchor.constraint(equalTo: detailLabel.bottomAnchor, constant: 20),
                    logTextView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
                    logTextView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
                    logTextView.heightAnchor.constraint(greaterThanOrEqualToConstant: 300),
                    
                    okButton.topAnchor.constraint(equalTo: logTextView.bottomAnchor, constant: 20),
                    okButton.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
                    okButton.widthAnchor.constraint(equalToConstant: 150),
                    okButton.heightAnchor.constraint(equalToConstant: 50),
                    okButton.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -20)
                ])
                
                // OKボタンのアクションを修正
                okButton.addAction(UIAction { _ in
                    errorVC.dismiss(animated: true)
                }, for: .touchUpInside)
                
                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                   let rootViewController = windowScene.windows.first?.rootViewController {
                    var topController = rootViewController
                    while let presentedController = topController.presentedViewController {
                        topController = presentedController
                    }
                    topController.present(errorVC, animated: true)
                }
            }
            
            completion(.failure(exhaustedError))
            return
        }
        
        // URL が無効な場合、失敗を返す
        guard let url = URL(string: filehostURL) else {
            completion(.failure(NSError(domain: "InvalidURL", code: -1, userInfo: nil)))
            return
        }
        
        // リクエストを設定
        var request = URLRequest(
            url: url,
            cachePolicy: .reloadIgnoringLocalCacheData,
            timeoutInterval: 100.0  // タイムアウト時間を100秒に変更
        )
        request.httpMethod = "POST"
        let paramStr = "syncType=1&syncFile=\(fileName)"
        guard let encodedParam = paramStr.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed)?.data(using: .utf8) else {
            completion(.failure(NSError(domain: "ParameterEncoding", code: -2, userInfo: nil)))
            return
        }
        request.httpBody = encodedParam
        
        // セッション設定を最適化
        let config = URLSessionConfiguration.ephemeral
        config.timeoutIntervalForRequest = 100  // リクエストタイムアウトを100秒に変更
        config.timeoutIntervalForResource = 100  // リソースタイムアウトを100秒に変更
        config.waitsForConnectivity = true  // 接続を待機するように変更
        config.httpMaximumConnectionsPerHost = 1  // 同時接続数を1に制限
        config.httpShouldUsePipelining = false  // パイプラインを無効化
        
        let session = URLSession(configuration: config)
    
        let fileURL = URL(fileURLWithPath: matRootPath)
            .appendingPathComponent(fileName)
            .standardized
        
        // ディレクトリを作成
        do {
            try FileManager.default.createDirectory(
                at: fileURL.deletingLastPathComponent(),
                withIntermediateDirectories: true,
                attributes: nil
            )
            print("ディレクトリを作成しました: \(fileURL.deletingLastPathComponent().path)")
        } catch {
            print("ディレクトリ作成エラー: \(error)")
            completion(.failure(error))
            return
        }
        
        print("ダウンロード開始: \(fileName)")
        
        // 同期ダウンロード用のセマフォを作成
        let semaphore = DispatchSemaphore(value: 0)
        var downloadError: Error?
        var downloadData: Data?
        
        // データタスクを開始
        let task = session.dataTask(with: request) { data, response, error in
            if let error = error {
                downloadError = error
            } else if let httpResponse = response as? HTTPURLResponse,
                      !(200...299).contains(httpResponse.statusCode) {
                downloadError = NSError(
                    domain: "HTTPError",
                    code: httpResponse.statusCode,
                    userInfo: [NSLocalizedDescriptionKey: "HTTP Error: \(httpResponse.statusCode)"]
                )
            } else {
                downloadData = data
            }
            semaphore.signal()
        }
        task.resume()
        
        // タイムアウトを設定して待機
        let timeoutResult = semaphore.wait(timeout: .now() + 100)
        
        if timeoutResult == .timedOut {
            print("ダウンロードタイムアウト: \(fileName)")
            let nextRetryCount = retryCount - 1
            let delay = Double(5 - nextRetryCount) * 2.0
            DispatchQueue.global().asyncAfter(deadline: .now() + delay) {
                self.downloadFile(fileName, retryCount: nextRetryCount, completion: completion)
            }
            return
        }
        
        if let error = downloadError {
            print("downloadFile file: \(fileName), error: \(error)")
            let nextRetryCount = retryCount - 1
            let delay = Double(5 - nextRetryCount) * 2.0
            DispatchQueue.global().asyncAfter(deadline: .now() + delay) {
                self.downloadFile(fileName, retryCount: nextRetryCount, completion: completion)
            }
            return
        }
        
        guard let data = downloadData else {
            print("空のデータを受信しました: \(fileName)")
            completion(.failure(NSError(
                domain: "EmptyData",
                code: -3,
                userInfo: [NSLocalizedDescriptionKey: "Received empty response data"]
            )))
            return
        }
        
        print("データを受信しました: \(fileName), サイズ: \(data.count)バイト")
        
        // データをファイルに書き込む
        do {
            try data.write(to: fileURL, options: .atomic)
            print("downloadFile suc path: \(fileURL.path)")
            completion(.success(()))
        } catch {
            print("ファイル書き込みエラー: \(fileName), エラー: \(error)")
            completion(.failure(error))
        }
    }

    private func updateVersionFile(for fileName: String, completion: @escaping (Result<Void, Error>) -> Void) {
        do {
            let versionInfo = "\(fileName),\(Date().timeIntervalSince1970)"
            print("updateVersionFile versionInfo: \(versionInfo)")
            try versionInfo.write(
                to: URL(fileURLWithPath: imageListFile),
                atomically: true,
                encoding: .utf8
            )
            completion(.success(()))
        } catch {
            completion(.failure(error))
        }
    }
    
    private func fileExists(atPath path: String) -> Bool {
        return fileMng.fileExists(atPath: path)
    }
    
    struct FileInfo {
        let fileName: String
        let version: String
        let exists: Bool
        let fullPath: String
    }
    
    func getAllFilesInDirectory() -> [String] {
        let enumerator = fileMng.enumerator(atPath: matRootPath)
        var files = [String]()
        // ディレクトリ内のすべてのファイルを列挙
        while let filePath = enumerator?.nextObject() as? String {
            files.append(filePath)
//            print("filePath: \(filePath)")
        }
        return files
    }
    
    func detectFileEncoding(path: String) -> String.Encoding? {
        let rawData = try? Data(contentsOf: URL(fileURLWithPath: path))
        return rawData?.detectStringEncoding()
    }
}

extension Data {
    func detectStringEncoding() -> String.Encoding? {
        if let _ = String(data: self, encoding: .utf8) { return .utf8 }
        if let _ = String(data: self, encoding: .shiftJIS) { return .shiftJIS }
        if self.starts(with: [0xEF, 0xBB, 0xBF]) {
            return .utf8
        }
        return nil
    }
}

