//
//  MenuBarView.swift
//  MatSimulator
//
//  Created by Clamour on 2025/02/10.
//

import UIKit

protocol MenuBarDelegate: AnyObject {
    func menuBarDidSelectBackground()
    func menuBarDidSelectMatCatalog()
    func menuBarDidSelectMatCatalogFromSecondPage()
    func menuBarDidSelectTransformMode()
    func menuBarDidSelectDeformMode()
    func menuBarDidSelectUndo()
    func menuBarDidSelectEraser()
    func menuBarDidSelectSave()
    func menuBarDidSelectFolder()
    func menuBarDidSelectDownload()
    func menuBarDidSelectLogoSelect()
    func menuBarDidSelectOnePoint()
    func menuBarDidSelectTextInsert()
    func menuBarDidSelectSelect()
    func menuBarDidSelectColor()
    func menuBarDidSelectTextPosition()
    func menuBarDidSelectExit()
    func menuBarDidSelectToForward()
    func menuBarDidSelectToBack()
    func menuBarDidSelectRemove()
    func menuBarDidSwitchMode(toEditMode: Bool)
    // テスト用メニュー項目を追加する
    func menuBarDidSelectTestTextPosition()
}

class MenuBarView: UIView {
    private enum MenuButton: String {
        case background = "背景画像の選択"
        case matCatalog = "マット画像の選択"
        case transform = "移動/拡縮/回転"
        case deform = "変形"
        case eraser = "消しゴム"
        case undo = "最初に戻す"
        case save = "保存"
        case folder = "画像の管理"
        case download = "マット画像取得"
        case logoSelect = "ロゴの選択"
        case onePoint = "ワンポイント他"
        case textInsert = "文字の挿入"
        case select = "選択"
        case color = "色の変更"
        case textPosition = "文字の位置"
        case exit = "ログアウト"
        case toForword = "前面へ移動"
        case toBack = "背面へ移動"
        case delete = "削除"
        case testTextPosition = "文字位置テスト"

        var title: String { rawValue }

        var iconName: String {
            switch self {
                case .background: return "Background"
                case .matCatalog: return "Matte"
                case .transform: return "Move"
                case .deform: return "Transform"
                case .eraser: return "Erase"
                case .undo: return "Undo"
                case .save: return "Save"
                case .folder: return "Folder"
                case .download: return "Download"
                case .logoSelect: return "Logo"
                case .onePoint: return "Onepoint"
                case .textInsert: return "Fonts"
                case .select: return "Select"
                case .color: return "Color"
                case .textPosition: return "Fonts"
                case .exit: return "Arrow Right"
                case .toForword: return "ToForward"
                case .toBack: return "ToBack"
                case .delete: return "Remove"
                case .testTextPosition: return "Fonts"
            }
        }
    }

    private enum Colors {
        static let mintGreen = UIColor(red: 0.36, green: 0.72, blue: 0.68, alpha: 1.0)
        static let softPink = UIColor(red: 0.93, green: 0.47, blue: 0.62, alpha: 1.0)
        static let activeTint = UIColor(red: 0.20, green: 0.29, blue: 0.37, alpha: 1.0)
        static let inactiveTint = UIColor(red: 0.53, green: 0.59, blue: 0.64, alpha: 1.0)
        static let inactiveBackground = UIColor(red: 0.95, green: 0.95, blue: 0.97, alpha: 1.0)
    }

    private let buttonContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    private let modeLabel: UILabel = {
        let label = UILabel()
        label.text = ""
        label.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        label.textColor = Colors.activeTint
        label.textAlignment = .center
        return label
    }()

    private let firstButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(named: "ArrangementImage"), for: .normal)
        button.tintColor = Colors.activeTint
        button.backgroundColor = Colors.mintGreen
        button.titleLabel?.font = UIFont.systemFont(ofSize: 20, weight: .bold)
        return button
    }()

    private let secondButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(named: "MatteDesign"), for: .normal)
        button.tintColor = Colors.inactiveTint
        button.backgroundColor = Colors.inactiveBackground
        button.titleLabel?.font = UIFont.systemFont(ofSize: 20, weight: .bold)
        return button
    }()

    private let modeDescriptionLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .bold)
        label.textColor = Colors.activeTint
        label.backgroundColor = Colors.mintGreen
        label.textAlignment = .center
        return label
    }()

    private let firstPageView = UIView()
    private let secondPageView = UIView()
    private var firstPageScrollView: UIScrollView?
    private var secondPageScrollView: UIScrollView?

    public var isEditMode: Bool = false

    weak var delegate: MenuBarDelegate?

    private var secondPageButtons: [UIButton] = []
    private var hasMatImage: Bool = false

    public var eraserButton: UIButton?
    public var logoButton: UIButton?
    public var onePointButton: UIButton?
    public var textButton: UIButton?
    public var transformButton: UIButton?
    public var colorButton: UIButton?
    public var textPositionButton: UIButton?
    public var testTextPositionButton: UIButton?
    public var toForwordButton: UIButton?
    public var toBackButton: UIButton?
    public var deleteButton: UIButton?

    static var shared: MenuBarView?

    override init(frame: CGRect) {
        super.init(frame: frame)
        MenuBarView.shared = self
        setupView()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        MenuBarView.shared = self
        setupView()
    }

    private func setupView() {
        backgroundColor = .systemGroupedBackground
        layer.cornerRadius = 12
        layer.shadowColor = UIColor.black.cgColor
        layer.shadowOpacity = 0.08
        layer.shadowOffset = CGSize(width: 0, height: 2)
        layer.shadowRadius = 6

        setupButtonContainer() // ボタンコンテナを設定
        setupFirstPage()       // 最初のページを設定
        setupSecondPage()      // 2 番目のページを設定
        setupActions()         // アクションを設定
        switchToFirstPage()    // 最初のページに切り替え
    }

    private func setupButtonContainer() {
        addSubview(buttonContainerView)
        buttonContainerView.addSubview(modeLabel)
        buttonContainerView.addSubview(firstButton)
        buttonContainerView.addSubview(secondButton)
        buttonContainerView.addSubview(modeDescriptionLabel)

        buttonContainerView.translatesAutoresizingMaskIntoConstraints = false
        modeLabel.translatesAutoresizingMaskIntoConstraints = false
        firstButton.translatesAutoresizingMaskIntoConstraints = false
        secondButton.translatesAutoresizingMaskIntoConstraints = false
        modeDescriptionLabel.translatesAutoresizingMaskIntoConstraints = false

        // ボタンコンテナのレイアウトを設定
        NSLayoutConstraint.activate([
            buttonContainerView.topAnchor.constraint(equalTo: topAnchor, constant: 16),
            buttonContainerView.leadingAnchor.constraint(equalTo: leadingAnchor),
            buttonContainerView.trailingAnchor.constraint(equalTo: trailingAnchor),
            buttonContainerView.heightAnchor.constraint(equalToConstant: 110),

            modeLabel.topAnchor.constraint(equalTo: buttonContainerView.topAnchor, constant: 4),
            modeLabel.leadingAnchor.constraint(equalTo: buttonContainerView.leadingAnchor),
            modeLabel.trailingAnchor.constraint(equalTo: buttonContainerView.trailingAnchor),
            modeLabel.heightAnchor.constraint(equalToConstant: 30),

            firstButton.topAnchor.constraint(equalTo: modeLabel.bottomAnchor, constant: 4),
            firstButton.leadingAnchor.constraint(equalTo: buttonContainerView.leadingAnchor),
            firstButton.trailingAnchor.constraint(equalTo: secondButton.leadingAnchor),
            firstButton.bottomAnchor.constraint(equalTo: modeDescriptionLabel.topAnchor, constant: 0),
            firstButton.widthAnchor.constraint(equalTo: secondButton.widthAnchor),

            secondButton.topAnchor.constraint(equalTo: modeLabel.bottomAnchor, constant: 4),
            secondButton.trailingAnchor.constraint(equalTo: buttonContainerView.trailingAnchor),
            secondButton.bottomAnchor.constraint(equalTo: modeDescriptionLabel.topAnchor, constant: 0),

            modeDescriptionLabel.leadingAnchor.constraint(equalTo: buttonContainerView.leadingAnchor),
            modeDescriptionLabel.trailingAnchor.constraint(equalTo: buttonContainerView.trailingAnchor),
            modeDescriptionLabel.bottomAnchor.constraint(equalTo: buttonContainerView.bottomAnchor, constant: 0),
            modeDescriptionLabel.heightAnchor.constraint(equalToConstant: 30)
        ])
    }

    private func setupFirstPage() {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = true
        scrollView.alwaysBounceVertical = true
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        addSubview(scrollView)
        firstPageScrollView = scrollView

        NSLayoutConstraint.activate([
            scrollView.topAnchor.constraint(equalTo: buttonContainerView.bottomAnchor, constant: 16),
            scrollView.leadingAnchor.constraint(equalTo: leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: bottomAnchor, constant: -16)
        ])

        firstPageView.translatesAutoresizingMaskIntoConstraints = false
        scrollView.addSubview(firstPageView)

        NSLayoutConstraint.activate([
            firstPageView.topAnchor.constraint(equalTo: scrollView.contentLayoutGuide.topAnchor),
            firstPageView.leadingAnchor.constraint(equalTo: scrollView.contentLayoutGuide.leadingAnchor),
            firstPageView.trailingAnchor.constraint(equalTo: scrollView.contentLayoutGuide.trailingAnchor),
            firstPageView.bottomAnchor.constraint(equalTo: scrollView.contentLayoutGuide.bottomAnchor),
            firstPageView.widthAnchor.constraint(equalTo: scrollView.frameLayoutGuide.widthAnchor)
        ])

        let buttonTypes: [MenuButton] = [.background, .matCatalog, .transform, .deform, .eraser, .undo, .save, .folder, .download, .exit]
        var previousButton: UIButton?
        var lastButtonBottomConstraint: NSLayoutConstraint?

        for type in buttonTypes {
            let button = createMenuButton(type: type)
            firstPageView.addSubview(button)

            if type == .eraser {
                self.eraserButton = button
            }

            NSLayoutConstraint.activate([
                button.leadingAnchor.constraint(equalTo: firstPageView.leadingAnchor, constant: 16),
                button.trailingAnchor.constraint(equalTo: firstPageView.trailingAnchor, constant: -16),
                button.heightAnchor.constraint(equalToConstant: 48)
            ])
            if let previousButton = previousButton {
                button.topAnchor.constraint(equalTo: previousButton.bottomAnchor, constant: 8).isActive = true
            } else {
                button.topAnchor.constraint(equalTo: firstPageView.topAnchor).isActive = true
            }
            previousButton = button

            if type == buttonTypes.last {
                lastButtonBottomConstraint = button.bottomAnchor.constraint(equalTo: firstPageView.bottomAnchor, constant: -16)
                lastButtonBottomConstraint?.isActive = true
            }
        }

        scrollView.layoutIfNeeded()
    }

    private func setupSecondPage() {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = true
        scrollView.alwaysBounceVertical = true
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        addSubview(scrollView)
        secondPageScrollView = scrollView

        NSLayoutConstraint.activate([
            scrollView.topAnchor.constraint(equalTo: buttonContainerView.bottomAnchor, constant: 16),
            scrollView.leadingAnchor.constraint(equalTo: leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: bottomAnchor, constant: -16)
        ])

        secondPageView.translatesAutoresizingMaskIntoConstraints = false
        scrollView.addSubview(secondPageView)

        NSLayoutConstraint.activate([
            secondPageView.topAnchor.constraint(equalTo: scrollView.contentLayoutGuide.topAnchor),
            secondPageView.leadingAnchor.constraint(equalTo: scrollView.contentLayoutGuide.leadingAnchor),
            secondPageView.trailingAnchor.constraint(equalTo: scrollView.contentLayoutGuide.trailingAnchor),
            secondPageView.bottomAnchor.constraint(equalTo: scrollView.contentLayoutGuide.bottomAnchor),
            secondPageView.widthAnchor.constraint(equalTo: scrollView.frameLayoutGuide.widthAnchor)
        ])

        let buttonTypes: [MenuButton] = [.matCatalog, .logoSelect, .onePoint, .textInsert, .select, .transform, .color, .textPosition, .toForword, .toBack, .delete, .save]
        var previousButton: UIButton?
        var lastButtonBottomConstraint: NSLayoutConstraint?

        for type in buttonTypes {
            let button = createMenuButton(type: type)
            secondPageView.addSubview(button)

            if type != .matCatalog {
                secondPageButtons.append(button)
                button.isEnabled = false
                button.alpha = 0.5

                if type == .logoSelect {
                    logoButton = button
                } else if type == .onePoint {
                    onePointButton = button
                } else if type == .textInsert {
                    textButton = button
                } else if type == .transform {
                    transformButton = button
                } else if type == .color {
                    colorButton = button
                } else if type == .textPosition {
                    textPositionButton = button
                } else if type == .testTextPosition {
                    testTextPositionButton = button
                } else if type == .toForword {
                    toForwordButton = button
                } else if type == .toBack {
                    toBackButton = button
                } else if type == .delete {
                    deleteButton = button
                }
            }

            NSLayoutConstraint.activate([
                button.leadingAnchor.constraint(equalTo: secondPageView.leadingAnchor, constant: 16),
                button.trailingAnchor.constraint(equalTo: secondPageView.trailingAnchor, constant: -16),
                button.heightAnchor.constraint(equalToConstant: 48)
            ])

            if let previousButton = previousButton {
                button.topAnchor.constraint(equalTo: previousButton.bottomAnchor, constant: 8).isActive = true
            } else {
                button.topAnchor.constraint(equalTo: secondPageView.topAnchor).isActive = true
            }
            previousButton = button

            if type == buttonTypes.last {
                lastButtonBottomConstraint = button.bottomAnchor.constraint(equalTo: secondPageView.bottomAnchor, constant: -16)
                lastButtonBottomConstraint?.isActive = true
            }
        }

        scrollView.layoutIfNeeded()
    }

    private func setupActions() {
        firstButton.addTarget(self, action: #selector(didTapFirstButton), for: .touchUpInside)
        secondButton.addTarget(self, action: #selector(didTapSecondButton), for: .touchUpInside)
    }

    @objc private func didTapFirstButton() {
        isEditMode = false
        switchToFirstPage()
        updateButtonStyles(activeButton: firstButton, inactiveButton: secondButton)
    }

    @objc private func didTapSecondButton() {
        isEditMode = true
        if !hasMatImage {
            secondPageButtons.forEach { button in
                button.isEnabled = false
                button.alpha = 0.5
            }
        }
        switchToSecondPage()
        updateButtonStyles(activeButton: secondButton, inactiveButton: firstButton)
    }

    private func switchToFirstPage() {
        isEditMode = false
        
        firstPageScrollView?.isHidden = false
        secondPageScrollView?.isHidden = true

        modeDescriptionLabel.text = "マット配置シミュレーション"
        modeDescriptionLabel.backgroundColor = Colors.mintGreen
        delegate?.menuBarDidSwitchMode(toEditMode: false)

        firstPageScrollView?.setNeedsLayout()
        firstPageScrollView?.layoutIfNeeded()

        if let scrollView = firstPageScrollView {
            scrollView.isScrollEnabled = true
            scrollView.setContentOffset(.zero, animated: false)
        }
    }

    private func switchToSecondPage() {
        isEditMode = true

        firstPageScrollView?.isHidden = true
        secondPageScrollView?.isHidden = false

        modeDescriptionLabel.text = "マットデザイン編集"
        modeDescriptionLabel.backgroundColor = Colors.softPink
        delegate?.menuBarDidSwitchMode(toEditMode: true)

        secondPageScrollView?.setNeedsLayout()
        secondPageScrollView?.layoutIfNeeded()

        if let scrollView = secondPageScrollView {
            scrollView.isScrollEnabled = true
            scrollView.setContentOffset(.zero, animated: false)
        }
    }

    private func updateButtonStyles(activeButton: UIButton, inactiveButton: UIButton) {
        UIView.animate(withDuration: 0.05) {
            activeButton.tintColor = Colors.activeTint
            activeButton.backgroundColor = activeButton == self.firstButton ? Colors.mintGreen : Colors.softPink

            inactiveButton.tintColor = Colors.inactiveTint
            inactiveButton.backgroundColor = Colors.inactiveBackground
        }
    }

    private func createMenuButton(type: MenuButton) -> UIButton {
        let button = UIButton(type: .system)

        let iconImage = UIImage(named: type.iconName)?.withRenderingMode(.alwaysOriginal)
        button.setImage(iconImage, for: .normal)
        button.setTitle(type.title, for: .normal)

        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        button.tintColor = .label
        button.setTitleColor(.label, for: .normal)
        button.backgroundColor = .secondarySystemGroupedBackground
        button.layer.cornerRadius = 10
        button.translatesAutoresizingMaskIntoConstraints = false

        button.layer.borderWidth = 1.5
        button.layer.borderColor = UIColor.clear.cgColor

        button.contentHorizontalAlignment = .left
        button.semanticContentAttribute = .forceLeftToRight

        let buttonPadding: CGFloat = 16
        let iconTextSpacing: CGFloat = 22
        button.contentEdgeInsets = UIEdgeInsets(
            top: 0,
            left: buttonPadding,
            bottom: 0,
            right: buttonPadding
        )

        button.imageEdgeInsets = UIEdgeInsets(
            top: 0,
            left: -buttonPadding,
            bottom: 0,
            right: buttonPadding + iconTextSpacing
        )

        button.titleEdgeInsets = UIEdgeInsets(
            top: 0,
            left: -buttonPadding + iconTextSpacing,
            bottom: 0,
            right: 0
        )

        button.imageView?.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            button.imageView!.widthAnchor.constraint(equalToConstant: 24),
            button.imageView!.heightAnchor.constraint(equalToConstant: 24),
            button.imageView!.centerYAnchor.constraint(equalTo: button.centerYAnchor),
            button.imageView!.leadingAnchor.constraint(equalTo: button.leadingAnchor, constant: buttonPadding)
        ])

        button.addAction(UIAction { [weak self] _ in
            guard let self = self else { return }
            self.clearButtonStyle()
            switch type {
                case .background: self.delegate?.menuBarDidSelectBackground()
                case .matCatalog:
                    if self.isEditMode {
                        self.delegate?.menuBarDidSelectMatCatalogFromSecondPage()
                    } else {
                        self.delegate?.menuBarDidSelectMatCatalog()
                    }
                case .transform:
                    self.updateButtonStyle(selectedButton: button)
                    self.delegate?.menuBarDidSelectTransformMode()
                case .deform:
                    self.updateButtonStyle(selectedButton: button)
                    self.delegate?.menuBarDidSelectDeformMode()
                case .eraser:
                    self.updateButtonStyle(selectedButton: button)
                    self.delegate?.menuBarDidSelectEraser()
                case .undo: self.delegate?.menuBarDidSelectUndo()
                case .save: self.delegate?.menuBarDidSelectSave()
                case .folder: self.delegate?.menuBarDidSelectFolder()
                case .download: self.delegate?.menuBarDidSelectDownload()
                // ---
                case .logoSelect: self.delegate?.menuBarDidSelectLogoSelect()
                case .onePoint: self.delegate?.menuBarDidSelectOnePoint()
                case .textInsert: self.delegate?.menuBarDidSelectTextInsert()
                case .select:
                    self.updateButtonStyle(selectedButton: button)
                    self.delegate?.menuBarDidSelectSelect()
                case .color:
                    self.delegate?.menuBarDidSelectColor()
                case .textPosition:
                    self.delegate?.menuBarDidSelectTextPosition()
                case .testTextPosition:
                    self.delegate?.menuBarDidSelectTestTextPosition()
                case .exit:
                    self.delegate?.menuBarDidSelectExit()
                case .delete:
                    self.delegate?.menuBarDidSelectRemove()
                case .toForword:
                    self.delegate?.menuBarDidSelectToForward()
                case .toBack:
                    self.delegate?.menuBarDidSelectToBack()
            }
        }, for: .touchUpInside)

        button.addTouchAnimation()

        return button
    }

    // 第 2 ページのボタンを有効化する
    func enableSecondPageButtons() {
        hasMatImage = true
        UIView.animate(withDuration: 0.3) {
            self.secondPageButtons.forEach { button in
                if button == self.deleteButton {
                    return
                }
                button.isEnabled = true
                button.alpha = 1.0
            }
        }
    }

    // すべてのボタンのスタイルをクリアする
    func clearButtonStyle() {
        updateEraserButtonText("消しゴム")

        firstPageView.subviews.forEach {
            guard let btn = $0 as? UIButton else { return }
            btn.backgroundColor = .secondarySystemGroupedBackground
            btn.setTitleColor(.label, for: .normal)
            btn.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
            btn.layer.borderColor = UIColor.clear.cgColor
        }

        secondPageView.subviews.forEach {
            guard let btn = $0 as? UIButton else { return }
            btn.backgroundColor = .secondarySystemGroupedBackground
            btn.setTitleColor(.label, for: .normal)
            btn.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
            btn.layer.borderColor = UIColor.clear.cgColor
        }
    }

    // 選択されたボタンのスタイルを更新する
    private func updateButtonStyle(selectedButton: UIButton) {
        UIView.animate(withDuration: 0.25) {
            selectedButton.backgroundColor = .systemBlue
            selectedButton.setTitleColor(.lightGray, for: .normal)
            selectedButton.titleLabel?.font = UIFont.systemFont(ofSize: 20, weight: .bold)
            selectedButton.layer.borderColor = UIColor.systemBlue.cgColor
        }
    }

    // 消しゴムボタンのテキストを更新するメソッド
    func updateEraserButtonText(_ text: String) {
        eraserButton?.setTitle(text, for: .normal)
        eraserButton?.titleLabel?.numberOfLines = 0
        eraserButton?.titleLabel?.lineBreakMode = .byWordWrapping
    }

    func updateTransformButtonState(isSelected: Bool, squareType: Int?, isDisabled:Bool = false) {
        guard let transformButton = transformButton else { return }

        if isDisabled {
            UIView.animate(withDuration: 0.3) {
                transformButton.isEnabled = false
                transformButton.alpha = 0.5
            }
            return
        }

        let shouldEnable = isSelected && squareType != nil && squareType != 0

        UIView.animate(withDuration: 0.3) {
            transformButton.isEnabled = shouldEnable
            transformButton.alpha = shouldEnable ? 1.0 : 0.5
        }
    }

    func updateLogoButtonState(forMatType matType: String) {
        guard let logoButton = logoButton else { return }

        let shouldEnable = !(matType == "インサイドセミオーダー" || matType == "家庭用マット")

        UIView.animate(withDuration: 0.3) {
            logoButton.isEnabled = shouldEnable
            logoButton.alpha = shouldEnable ? 1.0 : 0.5
        }
    }

    func updateOnePointButtonState(forMatType matType: String) {
        guard let onePointButton = onePointButton else { return }

        let shouldEnable = !(matType == "家庭用マット")

        UIView.animate(withDuration: 0.3) {
            onePointButton.isEnabled = shouldEnable
            onePointButton.alpha = shouldEnable ? 1.0 : 0.5
        }
    }

    func updateTextButtonState(forMatType matType: String, matInfo: MatInfo? = nil, isDisabled:Bool = false) {
        guard let textButton = textButton else { return }

        print("aqaz updateTextButtonState matType:\(matType), matInfo:\(matInfo), isDisabled:\(isDisabled)")

        if let homeConfig = matInfo?.homeMatConfig {
            print("aqaz updateTextButtonState homeMatConfig exists, positionList count: \(homeConfig.positionList.count)")
            print("aqaz updateTextButtonState textConfig.hasText: \(homeConfig.textConfig.hasText)")
            print("aqaz updateTextButtonState textConfig.textDirection: \(homeConfig.textConfig.textDirection)")
        }

        if isDisabled {
            print("aqaz updateTextButtonState - disabled by parameter")
            UIView.animate(withDuration: 0.3) {
                textButton.isEnabled = false
                textButton.alpha = 0.5
            }
            return
        }

        // 家庭用マットの場合
        if matType == "家庭用マット" {
            print("aqaz updateTextButtonState - is home mat")

            // matInfo が存在し、homeMatConfig が存在する場合
            if let homeConfig = matInfo?.homeMatConfig {
                // 文字設定が可能かつ横書きの場合は有効化
                let hasText = homeConfig.textConfig.hasText
                let isHorizontal = homeConfig.textConfig.textDirection
                let isEnabled = hasText && isHorizontal

                print("aqaz updateTextButtonState - hasText: \(hasText), isHorizontal: \(isHorizontal), isEnabled: \(isEnabled)")

                UIView.animate(withDuration: 0.3) {
                    textButton.isEnabled = isEnabled
                    textButton.alpha = isEnabled ? 1.0 : 0.5
                }
            } else {
                // homeMatConfig が存在しない場合は無効化
                print("aqaz updateTextButtonState - no homeMatConfig, disabling text button")
                UIView.animate(withDuration: 0.3) {
                    textButton.isEnabled = false
                    textButton.alpha = 0.5
                }
            }
        } else {
            // 家庭用マット以外の場合は常に有効化
            print("aqaz updateTextButtonState - not home mat, enabling text button")
            UIView.animate(withDuration: 0.3) {
                textButton.isEnabled = true
                textButton.alpha = 1.0
            }
        }
    }

    // マットタイプに基づいて色変更ボタンの状態を更新する
    func updateColorButtonState(forMatType matType: String, squareType: Int?, isSelected: Bool, isDisabled:Bool = false) {
        guard let colorButton = colorButton else { return }

        if isDisabled {
            UIView.animate(withDuration: 0.3) {
                colorButton.isEnabled = false
                colorButton.alpha = 0.5
            }
            return
        }

        if matType == "家庭用マット" || squareType == 1 || !isSelected {
            UIView.animate(withDuration: 0.3) {
                colorButton.isEnabled = false
                colorButton.alpha = 0.5
            }
        } else {
            UIView.animate(withDuration: 0.3) {
                colorButton.isEnabled = true
                colorButton.alpha = 1.0
            }
        }
    }

    func updateTextPositionButtonState(forMatType matType: String, matInfo: MatInfo? = nil, isSelected: Bool, isDisabled:Bool = false) {
        guard let textPositionButton = textPositionButton else { return }

        if isDisabled {
            UIView.animate(withDuration: 0.3) {
                textPositionButton.isEnabled = false
                textPositionButton.alpha = 0.5
            }
            return
        }
        
        
        var isShow = matInfo?.homeMatConfig?.textConfig.hasText ?? false && matInfo?.homeMatConfig?.textConfig.textDirection ?? false
        

        if matType == "家庭用マット" && isShow && isSelected {
            if isShow {
                UIView.animate(withDuration: 0.3) {
                    textPositionButton.isEnabled = true
                    textPositionButton.alpha = 1.0
                }
            } else {
                UIView.animate(withDuration: 0.3) {
                    textPositionButton.isEnabled = false
                    textPositionButton.alpha = 0.5
                }
            }
        } else {
            UIView.animate(withDuration: 0.3) {
                textPositionButton.isEnabled = false
                textPositionButton.alpha = 0.5
            }
        }
    }



    func updateForwordBackButtonState(isSelected: Bool, squareType: Int?, isDisabled:Bool = false) {
        guard let toForwordButton = toForwordButton, let toBackButton = toBackButton  else { return }

        if isDisabled {
            UIView.animate(withDuration: 0.3) {
                toForwordButton.isEnabled = false
                toForwordButton.alpha = 0.5
            }
            return
        }

        let shouldEnable = isSelected && squareType != nil && squareType != 0

        UIView.animate(withDuration: 0.3) {
            toForwordButton.isEnabled = shouldEnable
            toForwordButton.alpha = shouldEnable ? 1.0 : 0.5
            toBackButton.isEnabled = shouldEnable
            toBackButton.alpha = shouldEnable ? 1.0 : 0.5
        }
    }

    // 削除ボタンの状態を更新する
    func updateDeleteButtonState(isSelected: Bool, squareType: Int?, isDisabled:Bool = false) {
        guard let deleteButton = deleteButton else { return }

        if isDisabled {
            UIView.animate(withDuration: 0.3) {
                deleteButton.isEnabled = false
                deleteButton.alpha = 0.5
            }
            return
        }

        let shouldEnable = isSelected && squareType != nil && squareType != 0

        UIView.animate(withDuration: 0.3) {
            deleteButton.isEnabled = shouldEnable
            deleteButton.alpha = shouldEnable ? 1.0 : 0.5
        }
    }

    public func setModeLabelText(_ text: String) {
        modeLabel.text = text
    }

}

// UIButton の拡張：タッチアニメーションを追加
extension UIButton {
    // タッチ時のアニメーション効果を追加するメソッド
    func addTouchAnimation() {
        addAction(UIAction { _ in
            UIView.animate(withDuration: 0.1, animations: {
                self.transform = CGAffineTransform(scaleX: 0.96, y: 0.96)
                self.backgroundColor = .tertiarySystemGroupedBackground
            }) { _ in
                UIView.animate(withDuration: 0.3,
                             delay: 0,
                             usingSpringWithDamping: 0.6,
                             initialSpringVelocity: 0.5,
                             options: []) {
                    self.transform = .identity
                    self.backgroundColor = .secondarySystemGroupedBackground
                }
            }
        }, for: [.touchDown, .touchDragEnter])

        addAction(UIAction { _ in
            UIView.animate(withDuration: 0.2) {
                self.backgroundColor = .secondarySystemGroupedBackground
            }
        }, for: [.touchUpInside, .touchCancel, .touchDragExit])
    }
}
