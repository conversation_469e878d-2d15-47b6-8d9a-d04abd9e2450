//
//  MatCatalogViewController.swift
//  MatSimulator
//
//  Created by Clamour on 2025/02/19.
//

import UIKit

protocol MatCatalogViewControllerDelegate: AnyObject {
    func matCatalogViewController(_ controller: MatCatalogViewController, didSelectImageNamed imageName: String, isEditMode: Bool, matInfo: MatInfo?)
}

struct MatInfo {
    let name: String
    let matID: String
    let category: String
    let size: String
    let colors: [String]

    // 家庭用マット专用配置
    struct TextConfig {
        let hasText: Bool
        let textDirection: Bool
        let layout: String
        let maxCounts: CharacterLimits
    }

    struct CharacterLimits {
        let kanji: Int
        let hiragana: Int
        let katakana: Int
        let lowercase: Int
        let uppercase: Int
    }

    struct AreaConfig {
        let position: String
        let config: String
    }

    struct HomeMatConfig {
        let textConfig: TextConfig
        let color: String
        let a1: AreaConfig
        let a2: AreaConfig
        let a3: AreaConfig
        let b1: AreaConfig
        let b2: AreaConfig
        let b3: AreaConfig
        let c1: AreaConfig
        let c2: AreaConfig
        let c3: AreaConfig

        // 文字位置情報
        // 位置情報のリスト
        var positionList: [HomeMatPositionInfo] = []

        // デフォルトの文字位置
        var defaultLabelPosition: String {
            print("aqaz HomeMatConfig.defaultLabelPosition called, returning: \(textConfig.layout)")
            return textConfig.layout
        }

        // 指定された位置IDに対応する位置情報を取得
        func getPositionInfo(for positionID: String) -> HomeMatPositionInfo? {
            print("aqaz HomeMatConfig.getPositionInfo called for positionID: \(positionID)")
            print("aqaz HomeMatConfig.getPositionInfo positionList count: \(positionList.count)")

            // 全ての位置情報を表示
            for (index, info) in positionList.enumerated() {
                print("aqaz HomeMatConfig.getPositionInfo position \(index): \(info.labelPosition), enabled: \(info.labelEnable)")
            }

            let result = positionList.first { $0.labelPosition == positionID }
            print("aqaz HomeMatConfig.getPositionInfo result: \(result != nil ? "found" : "not found")")
            return result
        }

        // デフォルト位置の情報を取得
        var defaultPositionInfo: HomeMatPositionInfo? {
            print("aqaz HomeMatConfig.defaultPositionInfo called, defaultLabelPosition: \(defaultLabelPosition)")
            let result = getPositionInfo(for: defaultLabelPosition)
            print("aqaz HomeMatConfig.defaultPositionInfo result: \(result != nil ? "found" : "not found")")
            return result
        }
    }

    var homeMatConfig: HomeMatConfig?

    init(name: String, matID: String, category: String, size: String, colors: [String]) {
        self.name = name
        self.matID = matID
        self.category = category
        self.size = size
        self.colors = colors
    }
}

class MatCatalogViewController: UIViewController {
    weak var delegate: MatCatalogViewControllerDelegate?

    enum DisplayMode {
        case all
        case limited
    }

    private let displayMode: DisplayMode

    private let allMatTypes = [
        "インサイドセミオーダー",
        "インサイド",
        "レギュラー",
        "セレクションオーダーマット", // デザインマット
        "エクステリアマット",
        "ブラッシュアップマット",
        "エレガントマット",
        "うす型吸塵吸水マット",
        "吸塵吸水マット",
        "ベーシックマット",
        "家庭用玄関マット\nオーダーメイドタイプ" // "家庭用マット"
    ]

    private let limitedMatTypes = [
        "インサイドセミオーダー",
        "インサイド",
        "レギュラー",
        "家庭用玄関マット\nオーダーメイドタイプ" // "家庭用マット"
    ]

    private var matTypes: [String] {
        return displayMode == .all ? allMatTypes : limitedMatTypes
    }

    private var categories: [String] = []
    private var selectedCategory: String? = nil

    private let downloader = FileDownloader()

    private var selectedSize: String = "-S"

    private var selectedTabIndex: Int = 0

    private var editedImages: [String] = []

    private var currentImages: [String] = []
    private var sortedMatInfo: [MatInfo] = []


    private lazy var tabsContainer: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        view.backgroundColor = .systemGray6
        view.layer.cornerRadius = 5
        return view
    }()

    private lazy var standardTabButton: UIButton = {
        let button = UIButton(type: .system)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.setTitle("標準", for: .normal)
        button.backgroundColor = .white
        button.setTitleColor(.black, for: .normal)
        button.layer.cornerRadius = 5
        button.tag = 0
        button.addTarget(self, action: #selector(tabButtonTapped(_:)), for: .touchUpInside)
        return button
    }()

    private lazy var editedTabButton: UIButton = {
        let button = UIButton(type: .system)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.setTitle("編集済み", for: .normal)
        button.backgroundColor = .systemGray6
        button.setTitleColor(.darkGray, for: .normal)
        button.layer.cornerRadius = 5
        button.tag = 1
        button.addTarget(self, action: #selector(tabButtonTapped(_:)), for: .touchUpInside)
        return button
    }()

    private lazy var standardContentView: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        view.backgroundColor = .white
        return view
    }()

    private lazy var editedContentView: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        view.backgroundColor = .white
        view.isHidden = true
        return view
    }()

    private lazy var importLabel: UILabel = {
        let label = UILabel()
        label.translatesAutoresizingMaskIntoConstraints = false
        label.text = "取込"
        label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        return label
    }()

    private lazy var importButton: UIButton = {
        let button = UIButton(type: .system)
        button.translatesAutoresizingMaskIntoConstraints = false

        let iconImage = UIImage(named: "Folder")?.withRenderingMode(.alwaysOriginal)
        button.setImage(iconImage, for: .normal)
        button.setTitle("カメラロール取込", for: .normal)

        button.contentHorizontalAlignment = .left
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.layer.borderWidth = 0.5
        button.layer.borderColor = UIColor.lightGray.cgColor
        button.layer.cornerRadius = 5
        button.contentEdgeInsets = UIEdgeInsets(top: 5, left: 10, bottom: 5, right: 10)
        button.addTarget(self, action: #selector(importFromCameraRoll), for: .touchUpInside)
        return button
    }()

    private lazy var sizeButtonsStack: UIStackView = {
        let stack = UIStackView()
        stack.translatesAutoresizingMaskIntoConstraints = false
        stack.axis = .horizontal
        stack.distribution = .fillEqually
        stack.spacing = 5
        return stack
    }()

    private lazy var sButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("S", for: .normal)
        button.backgroundColor = .systemBlue
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 5
        button.addTarget(self, action: #selector(sizeButtonTapped(_:)), for: .touchUpInside)
        button.tag = 0
        return button
    }()

    private lazy var lButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("L", for: .normal)
        button.backgroundColor = .systemGray5
        button.setTitleColor(.systemBlue, for: .normal)
        button.layer.cornerRadius = 5
        button.addTarget(self, action: #selector(sizeButtonTapped(_:)), for: .touchUpInside)
        button.tag = 1
        return button
    }()

    private lazy var splitView: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()

    private lazy var leftPanel: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        view.backgroundColor = .white
        return view
    }()

    private lazy var matTypeLabel: UILabel = {
        let label = UILabel()
        label.translatesAutoresizingMaskIntoConstraints = false
        label.text = "マットタイプ"
        label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        return label
    }()

    private lazy var matTypeButton: UIButton = {
        let button = UIButton(type: .system)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.setTitle("(選択してください)", for: .normal)
        button.contentHorizontalAlignment = .left
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.titleLabel?.numberOfLines = 2
        button.layer.borderWidth = 0.5
        button.layer.borderColor = UIColor.lightGray.cgColor
        button.layer.cornerRadius = 5
        button.contentEdgeInsets = UIEdgeInsets(top: 5, left: 10, bottom: 5, right: 10)
        button.addTarget(self, action: #selector(showMatTypeSelection), for: .touchUpInside)
        return button
    }()

    private lazy var categorySelectionView: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        view.backgroundColor = .white
        view.layer.borderWidth = 1
        view.layer.borderColor = UIColor.lightGray.cgColor
        view.layer.cornerRadius = 5
        view.isHidden = true
        return view
    }()

    private lazy var categoryLabel: UILabel = {
        let label = UILabel()
        label.translatesAutoresizingMaskIntoConstraints = false
        label.text = "カテゴリ"
        label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        return label
    }()

    private lazy var categoryButton: UIButton = {
        let button = UIButton(type: .system)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.setTitle("-", for: .normal)
        button.contentHorizontalAlignment = .left
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.layer.borderWidth = 0.5
        button.layer.borderColor = UIColor.lightGray.cgColor
        button.layer.cornerRadius = 5
        button.contentEdgeInsets = UIEdgeInsets(top: 5, left: 10, bottom: 5, right: 10)
        button.addTarget(self, action: #selector(showCategorySelection), for: .touchUpInside)
        return button
    }()

    private lazy var searchLabel: UILabel = {
        let label = UILabel()
        label.translatesAutoresizingMaskIntoConstraints = false
        label.text = "パターンNo.検索"
        label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        return label
    }()

    private lazy var searchTextField: UITextField = {
        let textField = UITextField()
        textField.translatesAutoresizingMaskIntoConstraints = false
        textField.borderStyle = .roundedRect
        textField.font = UIFont.systemFont(ofSize: 14)
        // textField.placeholder = "検索..."
        textField.isEnabled = false
        textField.returnKeyType = .search
        textField.delegate = self

        let searchIcon = UIImageView(image: UIImage(systemName: "magnifyingglass"))
        searchIcon.tintColor = .gray
        searchIcon.contentMode = .scaleAspectFit
        searchIcon.frame = CGRect(x: 0, y: 0, width: 20, height: 20)

        let leftView = UIView(frame: CGRect(x: 0, y: 0, width: 30, height: 20))
        searchIcon.center = leftView.center
        leftView.addSubview(searchIcon)

        textField.leftView = leftView
        textField.leftViewMode = .always

        return textField
    }()

    private lazy var matTypeSelectionView: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        view.backgroundColor = .white
        view.layer.borderWidth = 1
        view.layer.borderColor = UIColor.lightGray.cgColor
        view.layer.cornerRadius = 5
        view.isHidden = true
        return view
    }()

    private lazy var tableView: UITableView = {
        let table = UITableView()
        table.translatesAutoresizingMaskIntoConstraints = false
        table.register(UITableViewCell.self, forCellReuseIdentifier: "Cell")
        table.delegate = self
        table.dataSource = self
        return table
    }()

    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.itemSize = CGSize(width: 150, height: 150)
        layout.minimumInteritemSpacing = 10
        layout.minimumLineSpacing = 10
        layout.sectionInset = UIEdgeInsets(top: 10, left: 10, bottom: 10, right: 10)

        let collection = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collection.translatesAutoresizingMaskIntoConstraints = false
        collection.backgroundColor = .white
        collection.register(MatImageCell.self, forCellWithReuseIdentifier: "ImageCell")
        collection.delegate = self
        collection.dataSource = self
        return collection
    }()

    init(displayMode: DisplayMode = .all) {
        self.displayMode = displayMode
        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder: NSCoder) {
        self.displayMode = .all
        super.init(coder: coder)
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()

        if selectedTabIndex == 1 {
            loadEditedImages()
        }

        let closeButton = UIButton(type: .system)
        closeButton.setTitle("閉じる", for: .normal)
        closeButton.addTarget(self, action: #selector(dismissView), for: .touchUpInside)
        closeButton.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(closeButton)

        NSLayoutConstraint.activate([
            closeButton.topAnchor.constraint(equalTo: view.topAnchor, constant: 10),
            closeButton.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -10)
        ])
    }

    private func setupUI() {
        view.backgroundColor = .white

        view.addSubview(splitView)
        splitView.addSubview(leftPanel)
        splitView.addSubview(collectionView)

        if displayMode == .all {
            leftPanel.addSubview(tabsContainer)
            tabsContainer.addSubview(standardTabButton)
            tabsContainer.addSubview(editedTabButton)

            leftPanel.addSubview(standardContentView)
            leftPanel.addSubview(editedContentView)

            editedContentView.addSubview(importLabel)
            editedContentView.addSubview(importButton)
        } else {
            leftPanel.addSubview(standardContentView)
        }

        standardContentView.addSubview(sizeButtonsStack)
        sizeButtonsStack.addArrangedSubview(sButton)
        sizeButtonsStack.addArrangedSubview(lButton)

        standardContentView.addSubview(matTypeLabel)
        standardContentView.addSubview(matTypeButton)
        standardContentView.addSubview(categoryLabel)
        standardContentView.addSubview(categoryButton)
        standardContentView.addSubview(searchLabel)
        standardContentView.addSubview(searchTextField)

        NSLayoutConstraint.activate([
            splitView.topAnchor.constraint(equalTo: view.topAnchor),
            splitView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            splitView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            splitView.trailingAnchor.constraint(equalTo: view.trailingAnchor),

            leftPanel.topAnchor.constraint(equalTo: splitView.topAnchor),
            leftPanel.bottomAnchor.constraint(equalTo: splitView.bottomAnchor),
            leftPanel.leadingAnchor.constraint(equalTo: splitView.leadingAnchor),
            leftPanel.widthAnchor.constraint(equalToConstant: 210),
        ])

        if displayMode == .all {
            NSLayoutConstraint.activate([
                // タブコンテナのレイアウト
                tabsContainer.topAnchor.constraint(equalTo: leftPanel.topAnchor, constant: 10),
                tabsContainer.leadingAnchor.constraint(equalTo: leftPanel.leadingAnchor, constant: 10),
                tabsContainer.trailingAnchor.constraint(equalTo: leftPanel.trailingAnchor, constant: -10),
                tabsContainer.heightAnchor.constraint(equalToConstant: 40),

                // タブボタンのレイアウト
                standardTabButton.topAnchor.constraint(equalTo: tabsContainer.topAnchor, constant: 4),
                standardTabButton.leadingAnchor.constraint(equalTo: tabsContainer.leadingAnchor, constant: 4),
                standardTabButton.bottomAnchor.constraint(equalTo: tabsContainer.bottomAnchor, constant: -4),
                standardTabButton.widthAnchor.constraint(equalTo: tabsContainer.widthAnchor, multiplier: 0.5, constant: -6),

                editedTabButton.topAnchor.constraint(equalTo: tabsContainer.topAnchor, constant: 4),
                editedTabButton.trailingAnchor.constraint(equalTo: tabsContainer.trailingAnchor, constant: -4),
                editedTabButton.bottomAnchor.constraint(equalTo: tabsContainer.bottomAnchor, constant: -4),
                editedTabButton.widthAnchor.constraint(equalTo: tabsContainer.widthAnchor, multiplier: 0.5, constant: -6),

                // コンテンツビューのレイアウト
                standardContentView.topAnchor.constraint(equalTo: tabsContainer.bottomAnchor, constant: 10),
                standardContentView.leadingAnchor.constraint(equalTo: leftPanel.leadingAnchor),
                standardContentView.trailingAnchor.constraint(equalTo: leftPanel.trailingAnchor),
                standardContentView.bottomAnchor.constraint(equalTo: leftPanel.bottomAnchor),

                editedContentView.topAnchor.constraint(equalTo: tabsContainer.bottomAnchor, constant: 10),
                editedContentView.leadingAnchor.constraint(equalTo: leftPanel.leadingAnchor),
                editedContentView.trailingAnchor.constraint(equalTo: leftPanel.trailingAnchor),
                editedContentView.bottomAnchor.constraint(equalTo: leftPanel.bottomAnchor),

                // 編集
                importLabel.topAnchor.constraint(equalTo: editedContentView.topAnchor, constant: 10),
                importLabel.leadingAnchor.constraint(equalTo: editedContentView.leadingAnchor, constant: 15),
                importLabel.trailingAnchor.constraint(equalTo: editedContentView.trailingAnchor, constant: -15),

                importButton.topAnchor.constraint(equalTo: importLabel.bottomAnchor, constant: 5),
                importButton.leadingAnchor.constraint(equalTo: editedContentView.leadingAnchor, constant: 15),
                importButton.trailingAnchor.constraint(equalTo: editedContentView.trailingAnchor, constant: -15),
                importButton.heightAnchor.constraint(equalToConstant: 38),
            ])
        } else {
            NSLayoutConstraint.activate([
                standardContentView.topAnchor.constraint(equalTo: leftPanel.topAnchor, constant: 10),
                standardContentView.leadingAnchor.constraint(equalTo: leftPanel.leadingAnchor),
                standardContentView.trailingAnchor.constraint(equalTo: leftPanel.trailingAnchor),
                standardContentView.bottomAnchor.constraint(equalTo: leftPanel.bottomAnchor),
            ])
        }

        NSLayoutConstraint.activate([
            // 標準
            matTypeLabel.topAnchor.constraint(equalTo: standardContentView.topAnchor, constant: 10),
            matTypeLabel.leadingAnchor.constraint(equalTo: standardContentView.leadingAnchor, constant: 15),
            matTypeLabel.trailingAnchor.constraint(equalTo: standardContentView.trailingAnchor, constant: -15),

            matTypeButton.topAnchor.constraint(equalTo: matTypeLabel.bottomAnchor, constant: 5),
            matTypeButton.leadingAnchor.constraint(equalTo: standardContentView.leadingAnchor, constant: 15),
            matTypeButton.trailingAnchor.constraint(equalTo: standardContentView.trailingAnchor, constant: -15),
            matTypeButton.heightAnchor.constraint(equalToConstant: 50),

            categoryLabel.topAnchor.constraint(equalTo: matTypeButton.bottomAnchor, constant: 20),
            categoryLabel.leadingAnchor.constraint(equalTo: standardContentView.leadingAnchor, constant: 15),
            categoryLabel.trailingAnchor.constraint(equalTo: standardContentView.trailingAnchor, constant: -15),

            categoryButton.topAnchor.constraint(equalTo: categoryLabel.bottomAnchor, constant: 5),
            categoryButton.leadingAnchor.constraint(equalTo: standardContentView.leadingAnchor, constant: 15),
            categoryButton.trailingAnchor.constraint(equalTo: standardContentView.trailingAnchor, constant: -15),
            categoryButton.heightAnchor.constraint(equalToConstant: 38),

            searchLabel.topAnchor.constraint(equalTo: categoryButton.bottomAnchor, constant: 20),
            searchLabel.leadingAnchor.constraint(equalTo: standardContentView.leadingAnchor, constant: 15),
            searchLabel.trailingAnchor.constraint(equalTo: standardContentView.trailingAnchor, constant: -15),

            searchTextField.topAnchor.constraint(equalTo: searchLabel.bottomAnchor, constant: 5),
            searchTextField.leadingAnchor.constraint(equalTo: standardContentView.leadingAnchor, constant: 15),
            searchTextField.trailingAnchor.constraint(equalTo: standardContentView.trailingAnchor, constant: -15),
            searchTextField.heightAnchor.constraint(equalToConstant: 38),

            sizeButtonsStack.topAnchor.constraint(equalTo: searchTextField.bottomAnchor, constant: 20),
            sizeButtonsStack.leadingAnchor.constraint(equalTo: standardContentView.leadingAnchor, constant: 15),
            sizeButtonsStack.trailingAnchor.constraint(equalTo: standardContentView.trailingAnchor, constant: -15),
            sizeButtonsStack.heightAnchor.constraint(equalToConstant: 30),
        ])

        NSLayoutConstraint.activate([
            collectionView.topAnchor.constraint(equalTo: splitView.topAnchor),
            collectionView.bottomAnchor.constraint(equalTo: splitView.bottomAnchor),
            collectionView.leadingAnchor.constraint(equalTo: leftPanel.trailingAnchor),
            collectionView.trailingAnchor.constraint(equalTo: splitView.trailingAnchor)
        ])
    }

    @objc private func tabButtonTapped(_ sender: UIButton) {
        standardTabButton.backgroundColor = .systemGray6
        standardTabButton.setTitleColor(.darkGray, for: .normal)
        editedTabButton.backgroundColor = .systemGray6
        editedTabButton.setTitleColor(.darkGray, for: .normal)

        sender.backgroundColor = .white
        sender.setTitleColor(.black, for: .normal)

        selectedTabIndex = sender.tag

        standardContentView.isHidden = selectedTabIndex != 0
        editedContentView.isHidden = selectedTabIndex != 1

        if selectedTabIndex == 0 {
            if let currentMatType = matTypeButton.title(for: .normal), currentMatType != "(選択してください)" {
                filterAndDisplayImages(matType: currentMatType)
            }
        } else {
            loadEditedImages()
            print("load  images count：\(editedImages.count)")
        }
    }

    private func loadEditedImages() {
        let editedMatPath = "\(downloader.matRootPath)EditedMatDesigns"

        if !FileManager.default.fileExists(atPath: editedMatPath) {
            try? FileManager.default.createDirectory(atPath: editedMatPath, withIntermediateDirectories: true, attributes: nil)
        }

        do {
            let files = try FileManager.default.contentsOfDirectory(atPath: editedMatPath)
            editedImages = files.filter { $0.hasSuffix(".png") || $0.hasSuffix(".jpg") || $0.hasSuffix(".jpeg") }
                              .map { "EditedMatDesigns/\($0)" }

            collectionView.reloadData()
        } catch {
            editedImages = []
            collectionView.reloadData()
        }
    }

    @objc private func dismissView() {
        dismiss(animated: true)
    }

    @objc private func showMatTypeSelection() {

        setupMatTypeSelectionView()
        matTypeSelectionView.isHidden = false

        let tapGestureView = UIView()
        tapGestureView.translatesAutoresizingMaskIntoConstraints = false
        tapGestureView.backgroundColor = UIColor.clear
        tapGestureView.tag = 101

        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dismissMatTypeSelection))
        tapGestureView.addGestureRecognizer(tapGesture)

        view.insertSubview(tapGestureView, belowSubview: matTypeSelectionView)

        NSLayoutConstraint.activate([
            tapGestureView.topAnchor.constraint(equalTo: view.topAnchor),
            tapGestureView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            tapGestureView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            tapGestureView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }

    @objc private func dismissMatTypeSelection() {
        matTypeSelectionView.isHidden = true

        if let tapGestureView = view.viewWithTag(101) {
            tapGestureView.removeFromSuperview()
        }
    }

    private func setupMatTypeSelectionView() {
        if matTypeSelectionView.superview != nil {
            return
        }

        view.addSubview(matTypeSelectionView)

        let tableView = UITableView()
        tableView.translatesAutoresizingMaskIntoConstraints = false
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "MatTypeCell")
        tableView.delegate = self
        tableView.dataSource = self
        tableView.tag = 100

        matTypeSelectionView.addSubview(tableView)

        NSLayoutConstraint.activate([
            matTypeSelectionView.topAnchor.constraint(equalTo: matTypeButton.bottomAnchor, constant: 5),
            matTypeSelectionView.leadingAnchor.constraint(equalTo: matTypeButton.leadingAnchor),
            matTypeSelectionView.trailingAnchor.constraint(equalTo: matTypeButton.trailingAnchor),
            matTypeSelectionView.heightAnchor.constraint(equalToConstant: 300),

            tableView.topAnchor.constraint(equalTo: matTypeSelectionView.topAnchor),
            tableView.leadingAnchor.constraint(equalTo: matTypeSelectionView.leadingAnchor),
            tableView.trailingAnchor.constraint(equalTo: matTypeSelectionView.trailingAnchor),
            tableView.bottomAnchor.constraint(equalTo: matTypeSelectionView.bottomAnchor)
        ])
    }

    @objc private func showCategorySelection() {
        setupCategorySelectionView()
        categorySelectionView.isHidden = false

        let tapGestureView = UIView()
        tapGestureView.translatesAutoresizingMaskIntoConstraints = false
        tapGestureView.backgroundColor = UIColor.clear
        tapGestureView.tag = 102

        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dismissCategorySelection))
        tapGestureView.addGestureRecognizer(tapGesture)

        view.insertSubview(tapGestureView, belowSubview: categorySelectionView)

        NSLayoutConstraint.activate([
            tapGestureView.topAnchor.constraint(equalTo: view.topAnchor),
            tapGestureView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            tapGestureView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            tapGestureView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }

    @objc private func dismissCategorySelection() {
        categorySelectionView.isHidden = true

        if let tapGestureView = view.viewWithTag(102) {
            tapGestureView.removeFromSuperview()
        }
    }

    private func setupCategorySelectionView() {
        if categorySelectionView.superview != nil {
            return
        }

        view.addSubview(categorySelectionView)

        let tableView = UITableView()
        tableView.translatesAutoresizingMaskIntoConstraints = false
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "CategoryCell")
        tableView.delegate = self
        tableView.dataSource = self
        tableView.tag = 200

        categorySelectionView.addSubview(tableView)

        NSLayoutConstraint.activate([
            categorySelectionView.topAnchor.constraint(equalTo: categoryButton.bottomAnchor, constant: 5),
            categorySelectionView.leadingAnchor.constraint(equalTo: categoryButton.leadingAnchor),
            categorySelectionView.trailingAnchor.constraint(equalTo: categoryButton.trailingAnchor),
            categorySelectionView.heightAnchor.constraint(equalToConstant: 300),

            tableView.topAnchor.constraint(equalTo: categorySelectionView.topAnchor),
            tableView.leadingAnchor.constraint(equalTo: categorySelectionView.leadingAnchor),
            tableView.trailingAnchor.constraint(equalTo: categorySelectionView.trailingAnchor),
            tableView.bottomAnchor.constraint(equalTo: categorySelectionView.bottomAnchor)
        ])
    }

    @objc private func sizeButtonTapped(_ sender: UIButton) {
        selectedSize = sender.tag == 0 ? "-S" : "-L"

        updateSizeButtonsAppearance()

        if let currentMatType = matTypeButton.title(for: .normal), currentMatType != "(選択してください)" {
            filterAndDisplayImages(matType: currentMatType)
        }
    }

    private func loadImages(for matType: String) {
        let files = downloader.getAllFilesInDirectory()

        categories = []
        var seenCategories = Set<String>()
        // selectedCategory = nil
        // categoryButton.setTitle("-", for: .normal)

        searchTextField.isEnabled = true
        searchTextField.text = ""

        var actualMatType = ""
        if matType == "家庭用玄関マット\nオーダーメイドタイプ" {
            actualMatType = "家庭用マット"
        } else if matType == "セレクションオーダーマット" {
            actualMatType = "デザインマット"
        } else {
            actualMatType = matType
        }

        if actualMatType == "家庭用マット" {
            sButton.setTitle("1", for: .normal)
            lButton.setTitle("2", for: .normal)
        } else {
            sButton.setTitle("S", for: .normal)
            lButton.setTitle("L", for: .normal)
        }

        // マット情報CSVファイルのパスを取得
        let csvPath = "標準マット/\(actualMatType)/マット情報.csv"
        let fullCSVPath = "\(downloader.matRootPath)\(csvPath)"

        if FileManager.default.fileExists(atPath: fullCSVPath) {
            loadMatInfoFromCSV(path: fullCSVPath, matType: actualMatType)
        } else {
            sortedMatInfo = []
        }


        for matInfo in sortedMatInfo {
            let category = matInfo.category
            if !category.isEmpty && !seenCategories.contains(category) {
                categories.append(category)
                seenCategories.insert(category)
            }
        }

        updateSizeButtonsVisibility()

        if categorySelectionView.superview != nil {
            if let tableView = categorySelectionView.subviews.first as? UITableView {
                tableView.reloadData()
            }
        }
        
        filterAndDisplayImages(matType: actualMatType)
    }

    private func updateSizeButtonsVisibility() {
        guard let selectedCategory = selectedCategory else {
            // カテゴリ未選択時は両方表示
            sButton.isHidden = false
            lButton.isHidden = false
            return
        }

        let categoryMatInfos = sortedMatInfo.filter {
            $0.category == selectedCategory &&
            ($0.size == "S" || $0.size == "L")
        }

        let hasS = categoryMatInfos.contains { $0.size == "S" }
        let hasL = categoryMatInfos.contains { $0.size == "L" }

        sButton.isHidden = !hasS
        lButton.isHidden = !hasL

        if hasS && !hasL {
            selectedSize = "-S"
            updateSizeButtonsAppearance()
        } else if !hasS && hasL {
            selectedSize = "-L"
            updateSizeButtonsAppearance()
        }
    }

    private func updateSizeButtonsAppearance() {
        sButton.backgroundColor = selectedSize == "-S" ? .systemBlue : .systemGray5
        sButton.setTitleColor(selectedSize == "-S" ? .white : .systemBlue, for: .normal)
        lButton.backgroundColor = selectedSize == "-L" ? .systemBlue : .systemGray5
        lButton.setTitleColor(selectedSize == "-L" ? .white : .systemBlue, for: .normal)
    }

    private func filterAndDisplayImages(matType: String) {
        var actualMatType = ""
        if matType == "家庭用玄関マット\nオーダーメイドタイプ" {
            actualMatType = "家庭用マット"
        } else if matType == "セレクションオーダーマット" {
            actualMatType = "デザインマット"
        } else {
            actualMatType = matType
        }

        let files = downloader.getAllFilesInDirectory()
        let searchKeyword = searchTextField.text?.lowercased() ?? ""

        let matPath = "標準マット/\(actualMatType)/"
        let filteredImages = files.filter { path in
            let hasCorrectSize = path.contains(selectedSize) || (!path.contains("-S") && !path.contains("-L"))
            let isThumb = path.hasSuffix("_thum.png")
            let notCSV = !path.hasSuffix(".csv")
            let matchesSearch = searchKeyword.isEmpty || path.lowercased().contains(searchKeyword)
            return path.contains(matPath) && hasCorrectSize && isThumb && notCSV && matchesSearch
        }

        if !sortedMatInfo.isEmpty {
            // マットIDと一致する画像を探して並べ替える
            var sortedImages: [String] = []
            var usedMatIDs = Set<String>()

            for matInfo in sortedMatInfo {
                // カテゴリーフィルターが設定されている場合は、一致するもののみ
                if let selectedCategory = selectedCategory, matInfo.category != selectedCategory {
                    continue
                }

                if matInfo.size == selectedSize.replacingOccurrences(of: "-", with: "") ||
                (selectedSize == "-S" && matInfo.size == "S") ||
                (selectedSize == "-L" && matInfo.size == "L") {
                    if usedMatIDs.contains(matInfo.matID) {
                        continue
                    }

                    // マットIDに一致する画像を探す
                    let matchingImages = filteredImages.filter { path in
                        path.contains(matInfo.matID)
                    }

                    if let firstImage = matchingImages.first {
                        sortedImages.append(firstImage)
                        usedMatIDs.insert(matInfo.matID)
                    }
                }
            }

            // 並べ替えられなかった画像も追加
//            let remainingImages = filteredImages.filter { path in
//                !sortedImages.contains(path)
//            }
//            currentImages = sortedImages + remainingImages
            currentImages = sortedImages
        } else {
            currentImages = filteredImages
        }

        collectionView.reloadData()
    }

    private func loadMatInfoFromCSV(path: String, matType: String) {
        print("aqaz loadMatInfoFromCSV path:\(path), matType:\(matType)")
        do {
            let csvContent = try String(contentsOfFile: path, encoding: .utf8)
            let lines = csvContent.components(separatedBy: .newlines)

            print("aqaz CSV lines count:\(lines.count)")

            sortedMatInfo = []

            if matType == "家庭用マット" {
                print("aqaz processing home mat CSV")
                // 家庭用マットの特別処理
                for (index, line) in lines.enumerated() {
                    let components = line.components(separatedBy: ",")
                    print("aqaz line \(index) components count: \(components.count)")
                    if components.count >= 51 {
                        let name = components[0]
                        let matID = components[1]
                        let category = components[2]
                        let size = components[3] == "1" ? "S" : "L"
                        print("aqaz home mat line - name:\(name), matID:\(matID), category:\(category), size:\(size)")
                        if !matID.isEmpty && !size.isEmpty {
//                            if matID.contains("A0007") {
//                                print("007")
//                            }
                            // 文字設定
                            let characterLimits = MatInfo.CharacterLimits(
                                kanji: Int(components[13]) ?? 0,
                                hiragana: Int(components[12]) ?? 0,
                                katakana: Int(components[11]) ?? 0,
                                lowercase: Int(components[9]) ?? 0,
                                uppercase: Int(components[10]) ?? 0
                            )

                            let textConfig = MatInfo.TextConfig(
                                // 文字方向 0:横書 1:縦書
                                hasText: components[4] == "1", 
                                textDirection: components[6] == "0", //文字方向 0:横書 1:縦書

                                layout: components[8],
                                maxCounts: characterLimits
                            )

                            // エリア設定
                            var areaConfigs = MatInfo.HomeMatConfig(
                                textConfig: textConfig,
                                color: components[24],
                                a1: MatInfo.AreaConfig(position: components[26], config: components[27]),
                                a2: MatInfo.AreaConfig(position: components[29], config: components[30]),
                                a3: MatInfo.AreaConfig(position: components[32], config: components[33]),
                                b1: MatInfo.AreaConfig(position: components[35], config: components[36]),
                                b2: MatInfo.AreaConfig(position: components[38], config: components[39]),
                                b3: MatInfo.AreaConfig(position: components[41], config: components[42]),
                                c1: MatInfo.AreaConfig(position: components[44], config: components[45]),
                                c2: MatInfo.AreaConfig(position: components[47], config: components[48]),
                                c3: MatInfo.AreaConfig(position: components[50], config: components[51])
                            )

                            // 位置情報を作成
                            areaConfigs.positionList = createPositionInfos(from: areaConfigs)
                            print("aqaz positionList count: \(areaConfigs.positionList.count)")

                            var matInfo = MatInfo(name: name, matID: matID, category: category, size: size, colors: [])
                            matInfo.homeMatConfig = areaConfigs
                            sortedMatInfo.append(matInfo)
                            print("aqaz added home mat info - name:\(name), matID:\(matID), positionList count:\(matInfo.homeMatConfig?.positionList.count ?? 0))")
                        }
                    }
                }
            } else {
                // 通常処理
                for line in lines {
                    let components = line.components(separatedBy: ",")
                    if components.count >= 3 {
                        let matID = components[0]
                        let category = components.count > 1 ? components[1] : ""
                        let size = components[2]

                        if !matID.isEmpty && !size.isEmpty {
                            var colors: [String] = []

                            for i in 3..<components.count {
                                if !components[i].isEmpty {
                                    colors.append(components[i])
                                }
                            }

                            sortedMatInfo.append(MatInfo(
                                name: matID,
                                matID: matID,
                                category: category,
                                size: size,
                                colors: colors
                            ))
                        }
                    }
                }
            }
        } catch {
            print("CSVファイルの読み込みに失敗しました: \(error)")
            sortedMatInfo = []
        }
    }

    @objc private func importFromCameraRoll() {
        let imagePicker = UIImagePickerController()
        imagePicker.sourceType = .photoLibrary
        imagePicker.delegate = self
        present(imagePicker, animated: true)
    }

    // 位置情報を作成するメソッド
    private func createPositionInfos(from config: MatInfo.HomeMatConfig) -> [HomeMatPositionInfo] {
        print("aqaz createPositionInfos called for config with layout: \(config.textConfig.layout)")
        var positionInfos: [HomeMatPositionInfo] = []

        // 各エリアの位置情報を作成
        addPositionInfo(for: "A-1", area: config.a1, to: &positionInfos)
        addPositionInfo(for: "A-2", area: config.a2, to: &positionInfos)
        addPositionInfo(for: "A-3", area: config.a3, to: &positionInfos)
        addPositionInfo(for: "B-1", area: config.b1, to: &positionInfos)
        addPositionInfo(for: "B-2", area: config.b2, to: &positionInfos)
        addPositionInfo(for: "B-3", area: config.b3, to: &positionInfos)
        addPositionInfo(for: "C-1", area: config.c1, to: &positionInfos)
        addPositionInfo(for: "C-2", area: config.c2, to: &positionInfos)
        addPositionInfo(for: "C-3", area: config.c3, to: &positionInfos)

        print("aqaz createPositionInfos result count: \(positionInfos.count)")
        for (index, info) in positionInfos.enumerated() {
            print("aqaz position \(index): \(info.labelPosition), enabled: \(info.labelEnable)")
        }

        return positionInfos
    }

    // エリア設定から位置情報を作成して追加
    private func addPositionInfo(for position: String, area: MatInfo.AreaConfig, to positionInfos: inout [HomeMatPositionInfo]) {
        print("aqaz addPositionInfo for position: \(position), area.position: \(area.position), area.config: \(area.config)")

        // 文字の設置可否を確認
        // 家庭用マットの場合、全ての位置を有効にする
        let labelEnable = true
        print("aqaz labelEnable: \(labelEnable)")

        if labelEnable {
            // 配置座標情報を解析
            var coordinates: [String] = []

            // area.positionが座標情報を含む場合
            if area.position.contains(":") {
                coordinates = area.position.components(separatedBy: ":")
                print("aqaz using position for coordinates: \(coordinates)")
            } else {
                coordinates = area.config.components(separatedBy: ":")
                print("aqaz using config for coordinates: \(coordinates)")
            }

            print("aqaz coordinates count: \(coordinates.count), coordinates: \(coordinates)")

            if coordinates.count >= 4 {
                let x = Int(coordinates[0]) ?? 0
                let y = Int(coordinates[1]) ?? 0
                let width = Int(coordinates[2]) ?? 0
                let height = Int(coordinates[3]) ?? 0
                print("aqaz parsed coordinates - x:\(x), y:\(y), width:\(width), height:\(height)")

                // 配置方法を解析
                let layout = position.components(separatedBy: "-")
                let vertical: Int
                let horizontal: Int

                // 垂直方向の配置
                switch layout[0] {
                case "A": vertical = 0  // 上揃え
                case "B": vertical = 1  // 中央揃え
                case "C": vertical = 2  // 下揃え
                default: vertical = 1
                }

                // 水平方向の配置
                switch layout[1] {
                case "1": horizontal = 0  // 左揃え
                case "3": horizontal = 2  // 右揃え
                default: horizontal = 1   // 中央揃え
                }

                print("aqaz layout alignment - vertical:\(vertical), horizontal:\(horizontal)")

                // 位置情報を作成して追加
                let positionInfo = HomeMatPositionInfo(
                    labelEnable: labelEnable,
                    labelPosition: position,
                    x: x,
                    y: y,
                    width: width,
                    height: height,
                    vertical: vertical,
                    horizontal: horizontal
                )
                positionInfos.append(positionInfo)
                print("aqaz added position info for \(position)")
            } else {
                print("aqaz ERROR: Invalid coordinates format for position \(position)")
            }
        } else {
            print("aqaz position \(position) is not enabled for text")
        }
    }

    private func saveImageToEditedFolder(_ image: UIImage) {
        let editedMatPath = "\(downloader.matRootPath)EditedMatDesigns"

        if !FileManager.default.fileExists(atPath: editedMatPath) {
            try? FileManager.default.createDirectory(atPath: editedMatPath, withIntermediateDirectories: true, attributes: nil)
        }

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd_HHmmss"
        let fileName = "edited_mat_\(dateFormatter.string(from: Date())).jpg"
        let filePath = "\(editedMatPath)/\(fileName)"

        if let data = image.jpegData(compressionQuality: 0.8) {
            do {
                try data.write(to: URL(fileURLWithPath: filePath))
                loadEditedImages()
            } catch {
                print("画像の保存に失敗しました: \(error)")
            }
        }
    }
}

extension MatCatalogViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if tableView.tag == 100 {
            return matTypes.count
        } else if tableView.tag == 200 {
            return categories.count + 1
        }
        return 0
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if tableView.tag == 100 {
            let cell = tableView.dequeueReusableCell(withIdentifier: "MatTypeCell", for: indexPath)
            cell.textLabel?.text = matTypes[indexPath.row]
            cell.textLabel?.font = UIFont.systemFont(ofSize: 14)
            cell.textLabel?.numberOfLines = 2
            return cell
        } else if tableView.tag == 200 {
            let cell = tableView.dequeueReusableCell(withIdentifier: "CategoryCell", for: indexPath)
            if indexPath.row == 0 {
                cell.textLabel?.text = "すべて"
            } else {
                cell.textLabel?.text = categories[indexPath.row - 1]
            }
            cell.textLabel?.font = UIFont.systemFont(ofSize: 14)
            return cell
        }

        return UITableViewCell()
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        if tableView.tag == 100 {
            let selectedMatType = matTypes[indexPath.row]
            matTypeButton.setTitle(selectedMatType, for: .normal)
            dismissMatTypeSelection()
            loadImages(for: selectedMatType)
        } else if tableView.tag == 200 {
            if indexPath.row == 0 {
                selectedCategory = nil
                categoryButton.setTitle("-", for: .normal)
            } else {
                selectedCategory = categories[indexPath.row - 1]
                categoryButton.setTitle(selectedCategory, for: .normal)
            }
            dismissCategorySelection()

            updateSizeButtonsVisibility()

            if let currentMatType = matTypeButton.title(for: .normal), currentMatType != "(選択してください)" {
                filterAndDisplayImages(matType: currentMatType)
            }
        }
    }
}

// MARK: - UICollectionViewDelegate & DataSource
extension MatCatalogViewController: UICollectionViewDelegate, UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        let count = selectedTabIndex == 0 ? currentImages.count : editedImages.count
        return count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "ImageCell", for: indexPath) as! MatImageCell

        let imagePath: String
        if selectedTabIndex == 0 {
            imagePath = currentImages[indexPath.row]
        } else {
            imagePath = editedImages[indexPath.row]
        }

        let fullPath = "\(downloader.matRootPath)\(imagePath)"

        cell.imageView.image = UIImage(contentsOfFile: fullPath)

        let matID = extractMatID(from: imagePath)
        let displayID = matID.hasSuffix("-S") || matID.hasSuffix("-L") ? 
                        String(matID.dropLast(2)) : matID
        cell.idLabel.text = displayID
        
        return cell
    }

    private func extractMatID(from path: String, getName: Bool = false) -> String {
        print("aqaz extractMatID from path: \(path), getName: \(getName)")

        if getName && path.contains("家庭用マット") {
            print("aqaz extractMatID - processing home mat with getName=true")
            let components = path.components(separatedBy: "/")
            if let fileName = components.last {
                print("aqaz extractMatID - fileName: \(fileName)")
                let nameWithoutExtension = fileName.components(separatedBy: "_thum.png").first ??
                                          fileName.components(separatedBy: ".png").first ?? fileName
                print("aqaz extractMatID - nameWithoutExtension: \(nameWithoutExtension)")

                // ファイル名からパターン番号を抽出
                let parts = nameWithoutExtension.components(separatedBy: "_")
                if parts.count >= 3 {
                    let patternID = parts[2]
                    print("aqaz extractMatID - extracted patternID from parts: \(patternID)")
                    return patternID
                }

                let patternComponents = nameWithoutExtension.components(separatedBy: ",").first?.trimmingCharacters(in: .whitespaces)
                print("aqaz extractMatID - patternComponents: \(patternComponents ?? "nil")")
                let result = patternComponents ?? nameWithoutExtension
                print("aqaz extractMatID - result for home mat with getName=true: \(result)")
                return result
            }
        }

        if path.contains("家庭用マット") {
            print("aqaz extractMatID - processing home mat")
            // ファイル名からパターン番号を抽出
            let components = path.components(separatedBy: "/")
            if let fileName = components.last {
                let parts = fileName.components(separatedBy: "_")
                if parts.count >= 3 {
                    let patternID = parts[2].replacingOccurrences(of: ".png", with: "")
                                      .replacingOccurrences(of: "_thum", with: "")
                    print("aqaz extractMatID - extracted patternID from parts: \(patternID)")
                    return patternID
                }
            }

            if let matchedInfo = sortedMatInfo.first(where: { path.contains($0.matID) }) {
                print("aqaz extractMatID - found matching matID: \(matchedInfo.matID)")
                return matchedInfo.matID
            }
        }

        let patterns = ["[A-Z]{2}-\\d{3}", "[A-Z]{2}\\d{3}", "[A-Z]{1}-\\d{3}"]
        for pattern in patterns {
            if let range = path.range(of: pattern, options: .regularExpression) {
                let result = String(path[range])
                print("aqaz extractMatID - matched pattern \(pattern): \(result)")
                return result
            }
        }

        let components = path.components(separatedBy: "/")
        if let fileName = components.last {
            let nameWithoutExtension = fileName.components(separatedBy: "_thum.png").first ?? fileName
            print("aqaz extractMatID - fallback to fileName: \(nameWithoutExtension)")
            return nameWithoutExtension
        }

        print("aqaz extractMatID - no match found, returning empty string")
        return ""
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let imagePath: String
        if selectedTabIndex == 0 {
            imagePath = currentImages[indexPath.row]
        } else {
            imagePath = editedImages[indexPath.row]
        }

        print("aqaz selected image path: \(imagePath)")

        let matName = extractMatID(from: imagePath, getName: true)
        print("aqaz extracted matName: \(matName)")

        print("aqaz sortedMatInfo count: \(sortedMatInfo.count)")
        if !sortedMatInfo.isEmpty {
            print("aqaz first matInfo: name=\(sortedMatInfo[0].name), matID=\(sortedMatInfo[0].matID)")
            print("aqaz first matInfo homeMatConfig: \(sortedMatInfo[0].homeMatConfig != nil ? "exists" : "nil")")
            if let config = sortedMatInfo[0].homeMatConfig {
                print("aqaz first matInfo positionList count: \(config.positionList.count)")
            }
        }

        // 対応するMatInfoを検索
        var info: MatInfo? = nil

        // 家庭用マットの場合の特別処理
        if imagePath.contains("家庭用マット") {
            // ファイル名からパターン番号を抽出
            let components = imagePath.components(separatedBy: "/")
            if let fileName = components.last {
                let parts = fileName.components(separatedBy: "_")
                if parts.count >= 3 {
                    let patternID = parts[2].replacingOccurrences(of: ".png", with: "")
                                      .replacingOccurrences(of: "_thum", with: "")
                    print("aqaz extracted patternID: \(patternID)")

                    // パターンIDで検索
                    info = sortedMatInfo.first { $0.matID == patternID }
                    print("aqaz info by patternID: \(info != nil ? "found" : "not found")")

                    // 見つからない場合は、最初の家庭用マット情報を使用
                    if info == nil && !sortedMatInfo.isEmpty {
                        info = sortedMatInfo.first { $0.homeMatConfig != nil }
                        print("aqaz using default home mat config as fallback: \(info != nil ? "found" : "not found")")
                    }

                    // 全ての場合で見つからない場合は、デフォルトのMatInfoを作成
                    if info == nil {
                        print("aqaz Creating default MatInfo for home mat")

                        // デフォルトのTextConfigを作成
                        let textConfig = MatInfo.TextConfig(
                            hasText: true,
                            textDirection: true,
                            layout: "B-2",
                            maxCounts: MatInfo.CharacterLimits(
                                kanji: 10,
                                hiragana: 20,
                                katakana: 20,
                                lowercase: 30,
                                uppercase: 30
                            )
                        )

                        // デフォルトのAreaConfigを作成
                        let defaultAreaConfig = MatInfo.AreaConfig(position: "0", config: "0:0:0:0")

                        // A行（上部）の位置情報
                        let a1AreaConfig = MatInfo.AreaConfig(position: "15:15:303:36", config: "0:0") // 左
                        let a2AreaConfig = MatInfo.AreaConfig(position: "15:15:303:36", config: "0:1") // 中央
                        let a3AreaConfig = MatInfo.AreaConfig(position: "15:15:303:36", config: "0:2") // 右

                        // B行（中部）の位置情報
                        let b1AreaConfig = MatInfo.AreaConfig(position: "15:93:303:36", config: "1:0") // 左
                        let b2AreaConfig = MatInfo.AreaConfig(position: "15:93:303:36", config: "1:1") // 中央
                        let b3AreaConfig = MatInfo.AreaConfig(position: "15:93:303:36", config: "1:2") // 右

                        // C行（下部）の位置情報
                        let c1AreaConfig = MatInfo.AreaConfig(position: "15:171:303:36", config: "2:0") // 左
                        let c2AreaConfig = MatInfo.AreaConfig(position: "15:171:303:36", config: "2:1") // 中央
                        let c3AreaConfig = MatInfo.AreaConfig(position: "15:171:303:36", config: "2:2") // 右

                        // デフォルトのHomeMatConfigを作成
                        var homeMatConfig = MatInfo.HomeMatConfig(
                            textConfig: textConfig,
                            color: "000000",
                            a1: a1AreaConfig,  // 上部左
                            a2: a2AreaConfig,  // 上部中央
                            a3: a3AreaConfig,  // 上部右
                            b1: b1AreaConfig,  // 中部左
                            b2: b2AreaConfig,  // 中部中央
                            b3: b3AreaConfig,  // 中部右
                            c1: c1AreaConfig,  // 下部左
                            c2: c2AreaConfig,  // 下部中央
                            c3: c3AreaConfig   // 下部右
                        )

                        // 位置情報を作成
                        homeMatConfig.positionList = createPositionInfos(from: homeMatConfig)

                        // デフォルトのMatInfoを作成
                        info = MatInfo(name: "Default", matID: patternID, category: "家庭用マット", size: "L", colors: [])
                        info?.homeMatConfig = homeMatConfig

                        print("aqaz Created default MatInfo with positionList count: \(homeMatConfig.positionList.count)")
                    }
                }
            }
        } else {
            // 通常のマットの場合
            // 1. 名前で検索
            info = sortedMatInfo.first { $0.name == matName }
            print("aqaz info by name: \(info != nil ? "found" : "not found")")

            // 2. matIDで検索
            if info == nil {
                info = sortedMatInfo.first { $0.matID == matName }
                print("aqaz info by matID: \(info != nil ? "found" : "not found")")
            }
        }

        print("aqaz final info: \(info)")
        if let homeConfig = info?.homeMatConfig {
            print("aqaz homeMatConfig exists, positionList count: \(homeConfig.positionList.count)")
            print("aqaz defaultLabelPosition: \(homeConfig.defaultLabelPosition)")

            // デフォルト位置の情報を取得して表示
            if let defaultInfo = homeConfig.defaultPositionInfo {
                print("aqaz defaultPositionInfo found: \(defaultInfo.labelPosition), enabled: \(defaultInfo.labelEnable)")
            } else {
                print("aqaz defaultPositionInfo not found for \(homeConfig.defaultLabelPosition)")
            }

            // 全ての位置情報を表示
            for (index, posInfo) in homeConfig.positionList.enumerated() {
                print("aqaz position \(index): \(posInfo.labelPosition), enabled: \(posInfo.labelEnable)")
            }
        }

        delegate?.matCatalogViewController(self, didSelectImageNamed: imagePath, isEditMode: displayMode == .limited, matInfo: info)
    }
}

// MARK: - Collection View Cell
class MatImageCell: UICollectionViewCell {
    let imageView: UIImageView = {
        let iv = UIImageView()
        iv.contentMode = .scaleAspectFit
        iv.translatesAutoresizingMaskIntoConstraints = false
        return iv
    }()

    let idLabel: UILabel = {
        let label = UILabel()
        label.translatesAutoresizingMaskIntoConstraints = false
        label.textAlignment = .right
        label.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        label.textColor = .black
        label.backgroundColor = UIColor.white.withAlphaComponent(0.7)
        label.layer.cornerRadius = 4
        label.layer.masksToBounds = true
        return label
    }()

    override init(frame: CGRect) {
        super.init(frame: frame)
        contentView.addSubview(imageView)
        contentView.addSubview(idLabel)

        NSLayoutConstraint.activate([
            imageView.topAnchor.constraint(equalTo: contentView.topAnchor),
            imageView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            imageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            imageView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),

            idLabel.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -5),
            idLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -5),
            idLabel.heightAnchor.constraint(equalToConstant: 20),
            idLabel.widthAnchor.constraint(greaterThanOrEqualToConstant: 50)
        ])
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}

 extension MatCatalogViewController: UITextFieldDelegate {
     func textFieldShouldReturn(_ textField: UITextField) -> Bool {
         textField.resignFirstResponder()

         if let currentMatType = matTypeButton.title(for: .normal), currentMatType != "(選択してください)" {
             filterAndDisplayImages(matType: currentMatType)
         }

         return true
     }
 }

 extension MatCatalogViewController: UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        picker.dismiss(animated: true)

        if let image = info[.originalImage] as? UIImage {
            saveImageToEditedFolder(image)
        }
    }

    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true)
    }
}
