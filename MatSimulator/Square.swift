//
//  Square.swift
//  MatSimulator
//
//  Created by Clamour on 2025/02/11.
//

import simd
import Metal
import UIKit

class Square {
    // 位置（2 次元ベクトル）
    var position: SIMD2<Float> = .zero
    // スケール（幅と高さ）
    var scale: SIMD2<Float> = SIMD2<Float>(1, 1)
    // 回転角度（ラジアン）
    var rotation: Float = 0.0
    // テクスチャ参照
    var texture: MTLTexture?
    // 元のサイズ（幅と高さ）
    var size: SIMD2<Float> = .zero
    var drawableSize: CGSize = .zero
    var originalAspectRatio: Float = 1.0

    // 四隅の変位量（左上、右上、左下、右下）
    var cornerDisplacements: [SIMD2<Float>] = [.zero, .zero, .zero, .zero]
    var color: SIMD4<Float> = SIMD4<Float>(1, 1, 1, 1) // Default to white
    var texturePath: String?
    var squareType: Int = 0 // Mat:0 logo:1 onepoint:2 text:3
    var matType: String = ""
    var isEditMode: Bool = false
    var isHidden: Bool = false
    var originalColor: UIColor?
    var originalImage: UIImage?
    
    // 追加：テキストコンテンツを格納
    var textContent: String? 
        var currentVerticalPosition: VerticalPosition? // record current vertical position
        var currentHorizontalAlignment: HorizontalAlignment? // record current horizontal alignment

    var matInfo: MatInfo?

    private var _transformMatrix: float4x4 = matrix_identity_float4x4

    // 变換行列を計算
    var transform: Transform {
        var transform = Transform()
        transform.matrix = _transformMatrix
        return transform
    }

    // 四隅の座標を計算
    func computeCornerPoints(modelMatrix: float4x4, viewSize: CGSize) -> [CGPoint] {
        // 基準となる四隅の座標
        let baseCorners: [SIMD2<Float>] = [
            [-0.5, -0.5], [0.5, -0.5], [-0.5, 0.5], [0.5, 0.5]
        ]

        return baseCorners.enumerated().map { index, corner in
            let displaced = corner + cornerDisplacements[index] // 変位を適用
            let pos = modelMatrix * SIMD4<Float>(displaced.x, displaced.y, 0, 1)
            // 正規化座標から画面座標に変換
            let x = (pos.x/pos.w + 1) * 0.5 * Float(viewSize.width)
            let y = (1 - pos.y/pos.w) * 0.5 * Float(viewSize.height)
            return CGPoint(x: CGFloat(x), y: CGFloat(y))
        }
    }

    func computeTextureCenter(viewSize: CGSize) -> CGPoint {
        let baseCorners: [SIMD2<Float>] = [
            [-0.5, -0.5], [0.5, -0.5], [-0.5, 0.5], [0.5, 0.5]
        ]

        let transformedCorners = baseCorners.enumerated().map { index, corner -> SIMD4<Float> in
            let displaced = corner + cornerDisplacements[index]
            let pos = transform.matrix * SIMD4<Float>(displaced.x, displaced.y, 0, 1)
            return pos
        }

        var sumX: Float = 0
        var sumY: Float = 0
        for pos in transformedCorners {
            let x = (pos.x / pos.w + 1) * 0.5 * Float(viewSize.width)
            let y = (1 - pos.y / pos.w) * 0.5 * Float(viewSize.height)
            sumX += x
            sumY += y
        }

        let centerX = CGFloat(sumX / 4)
        let centerY = CGFloat(sumY / 4)
        let result = CGPoint(x: centerX, y: centerY)
        
        print(String(format:"🟠 computeTextureCenter -> screen=(%.1f, %.1f)", result.x, result.y))


        return result
    }

    func computeTextureSize(viewSize: CGSize) -> CGSize {
        let baseCorners: [SIMD2<Float>] = [
            [-0.5, -0.5],
            [0.5, -0.5],
            [-0.5, 0.5],
            [0.5, 0.5]
        ]

        let transformedCorners = baseCorners.enumerated().map { index, corner -> CGPoint in
            let displaced = corner + cornerDisplacements[index]
            let pos = transform.matrix * SIMD4<Float>(displaced.x, displaced.y, 0, 1)

            let screenX = (pos.x / pos.w + 1) * 0.5 * Float(viewSize.width)
            let screenY = (1 - pos.y / pos.w) * 0.5 * Float(viewSize.height)

            return CGPoint(x: CGFloat(screenX), y: CGFloat(screenY))
        }

        let height = hypot(
            abs(transformedCorners[0].x - transformedCorners[2].x),
            abs(transformedCorners[0].y - transformedCorners[2].y)
        )
        let width = hypot(
            abs(transformedCorners[0].x - transformedCorners[1].x),
            abs(transformedCorners[0].y - transformedCorners[1].y)
        )

        return CGSize(width: CGFloat(width), height: CGFloat(height))
    }

    // 計算ワールド座標系での四隅の位置
    func calculateWorldNormalizedBounds(viewSize: CGSize) -> (minX: Float, minY: Float, maxX: Float, maxY: Float)? {

        let corners = computeCornerPoints(
            modelMatrix: transform.matrix,
            viewSize: viewSize
        )
        guard !corners.isEmpty else { return nil }

        let xs = corners.map { Float(($0.x / viewSize.width) * 2.0 - 1.0) }
        let ys = corners.map { Float(1.0 - ($0.y / viewSize.height) * 2.0) }

        let minX = xs.min()!, maxX = xs.max()!
        let minY = ys.min()!, maxY = ys.max()!

        // 📍⑥ DEBUG
        print(String(format: "📐 bounds=(%.3f,%.3f)-(%.3f,%.3f)",
                     minX, minY, maxX, maxY))

        return (minX,minY,maxX,maxY)
    }


    /// テキストの正規化サイズを計算
    /// - Parameter viewSize: ビューのサイズ
    /// - Returns: 正規化サイズ (width, height)
    func calculateNormalizedSize(viewSize: CGSize) -> SIMD2<Float> {
        let textureSize = computeTextureSize(viewSize: viewSize)

        // ピクセルサイズを正規化座標系に変換
        let normalizedWidth = Float(textureSize.width) / Float(viewSize.width) * 2.0
        let normalizedHeight = Float(textureSize.height) / Float(viewSize.height) * 2.0

        return SIMD2<Float>(normalizedWidth, normalizedHeight)
    }

    func updateTransformMatrix(preserveScale: Bool = false) {
        // 1. scale.y の校正
        if self.originalAspectRatio > 0 {
            let correctedScaleY = self.scale.x / self.originalAspectRatio
            if abs(self.scale.y - correctedScaleY) > 1e-5 {
                // print(...) // ログ
                self.scale.y = correctedScaleY
            }
        } else { /* エラー処理 */ }

        // 2. ローカル中心点 (通常は 0,0)

        let pivot = SIMD2<Float>(0.0, 0.0)

        // 3. 変換行列の構築 (T * R * S) - 頂点に適用する際は逆順になる

        // --- ステップ a: 原点への平行移動 ---
        var translateToOrigin = Transform()
        translateToOrigin.translate(to: -pivot)

        // --- ステップ b: スケーリング ---
        var scaleMatrix = Transform()
        scaleMatrix.scale(by: self.scale) // 校正後の scale を使用

        // --- ステップ c: 回転 ---
        var rotateMatrix = Transform()
        rotateMatrix.rotate(by: rotation)

        // --- ステップ d: 元の中心点への平行移動 ---
        var translateBack = Transform()
        translateBack.translate(to: pivot)

        // --- ステップ e: 最終的なワールド位置への平行移動 ---

        var translateToFinalPosition = Transform()
        translateToFinalPosition.translate(to: position)

        // --- ローカル変形（ピボットを中心に）：T_back * R * S * T_origin ---
        // 乗算の順序に注意。頂点には右から左へ適用される
        let localTransformMatrix = translateBack.matrix * rotateMatrix.matrix * scaleMatrix.matrix * translateToOrigin.matrix

        // --- ワールド変換の合成: T_final * Local ---
        let modelWorldMatrix = translateToFinalPosition.matrix * localTransformMatrix

        // 4. 画面アスペクト比の修正
        let aspectMatrix = Transform.aspectCorrectionMatrix(drawableSize: drawableSize)

        // --- 最終的な合成: Aspect * Model ---

        _transformMatrix = aspectMatrix * modelWorldMatrix

        let det = matrix_determinant(_transformMatrix)
        let isAffine = abs(_transformMatrix.columns.0.w) < 1e-6 && abs(_transformMatrix.columns.1.w) < 1e-6 && abs(_transformMatrix.columns.2.w) < 1e-6 && abs(_transformMatrix.columns.3.w - 1.0) < 1e-6
         print(String(format: "   Matrix Determinant: %.4f, Is Affine: %d", det, isAffine ? 1:0)) // 检查行列式和仿射性
    }
    
    // MARK: - 純粋に中心を軸に回転（ストレッチ / 変形なし）
    func updateCenterRotateMatrix() {

        // 1) target 位置へ平行移動
        var t = Transform()
        t.translate(to: position)

        // 2) ローカル中心で回転
        t.rotate(by: rotation)

        // 3) 最後に拡大縮小（scale.x / scale.y は等しい必要はない）
        t.scale(by: scale)

        // 4) 画面アスペクト比補正を行うか
        let aspect =  matrix_identity_float4x4

        _transformMatrix = t.matrix

        // ⬇︎ デバッグ出力 --------------------------------------------------

        let p = _transformMatrix.columns.3
        print(String(format: "🌀 simpleRotate  angle=%.3f  pos(%.3f,%.3f)  col3(%.3f,%.3f)",
                     rotation, position.x, position.y, p.x, p.y))
    }

}

struct Transform {
    var matrix: float4x4 = matrix_identity_float4x4

    mutating func translate(to position: SIMD2<Float>) {
        let translation = float4x4(columns: (
            SIMD4<Float>(1, 0, 0, 0),
            SIMD4<Float>(0, 1, 0, 0),
            SIMD4<Float>(0, 0, 1, 0),
            SIMD4<Float>(position.x, position.y, 0, 1)
        ))
        matrix = matrix * translation
    }

    mutating func scale(by scale: SIMD2<Float>) {
        let scaling = float4x4(columns: (
            SIMD4<Float>(scale.x, 0, 0, 0),
            SIMD4<Float>(0, scale.y, 0, 0),
            SIMD4<Float>(0, 0, 1, 0),
            SIMD4<Float>(0, 0, 0, 1)
        ))
        matrix = matrix * scaling
    }

    mutating func rotate(by angle: Float) {
        let cosA = cos(angle)
        let sinA = sin(angle)
        let rotation = float4x4(columns: (
            SIMD4<Float>( cosA, sinA, 0, 0),
            SIMD4<Float>(-sinA, cosA, 0, 0),
            SIMD4<Float>(    0,    0, 1, 0),
            SIMD4<Float>(    0,    0, 0, 1)
        ))
        matrix = matrix * rotation
    }

    static func aspectCorrectionMatrix(drawableSize: CGSize) -> float4x4 {
        let aspect = Float(drawableSize.width / drawableSize.height)
        return float4x4(columns: (
            SIMD4<Float>(1.0 / aspect, 0, 0, 0),
            SIMD4<Float>(0, 1.0, 0, 0),
            SIMD4<Float>(0, 0, 1, 0),
            SIMD4<Float>(0, 0, 0, 1)
        ))
    }
}

extension float4x4 {
    // 行列式を計算
    var determinant: Float {
        let cols = self.columns

        let a = cols.0.x
        let b = cols.1.y * (cols.2.z * cols.3.w - cols.2.w * cols.3.z)
        let c = cols.1.z * (cols.2.y * cols.3.w - cols.2.w * cols.3.y)
        let d = cols.1.w * (cols.2.y * cols.3.z - cols.2.z * cols.3.y)

        let term1 = a * (b - c + d)

        let e = cols.0.y
        let f = cols.1.x * (cols.2.z * cols.3.w - cols.2.w * cols.3.z)
        let g = cols.1.z * (cols.2.x * cols.3.w - cols.2.w * cols.3.x)
        let h = cols.1.w * (cols.2.x * cols.3.z - cols.2.z * cols.3.x)

        let term2 = e * (f - g + h)

        let i = cols.0.z
        let j = cols.1.x * (cols.2.y * cols.3.w - cols.2.w * cols.3.y)
        let k = cols.1.y * (cols.2.x * cols.3.w - cols.2.w * cols.3.x)
        let l = cols.1.w * (cols.2.x * cols.3.y - cols.2.y * cols.3.x)

        let term3 = i * (j - k + l)

        let m = cols.0.w
        let n = cols.1.x * (cols.2.y * cols.3.z - cols.2.z * cols.3.y)
        let o = cols.1.y * (cols.2.x * cols.3.z - cols.2.z * cols.3.x)
        let p = cols.1.z * (cols.2.x * cols.3.y - cols.2.y * cols.3.x)

        let term4 = m * (n - o + p)

        return term1 - term2 + term3 - term4
    }

    // 特異行列かどうかを判定
    var isSingular: Bool {
        return abs(determinant) < 1e-6
    }
}


