//
//  AppDelegate.swift
//  MatSimulator
//
//  Created by Clamour on 2025/02/06.
//

import UIKit

@main
class AppDelegate: UIResponder, UIApplicationDelegate {
    var window: UIWindow?
    
    func application(_ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {
        return .landscape
    }
    
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        if #available(iOS 13.0, *) {
            window?.overrideUserInterfaceStyle = .light
        }

        let userName = getUserName()
    
        NotificationCenter.default.addObserver(forName: UIApplication.didBecomeActiveNotification, object: nil, queue: .main) { [weak self] _ in
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                if let menuBarView = MenuBarView.shared {
                    menuBarView.setModeLabelText(userName)
                } else {
                }
            }
        }
        
        return true
    }

    // MARK: UISceneSession Lifecycle

    func application(_ application: UIApplication, configurationForConnecting connectingSceneSession: UISceneSession, options: UIScene.ConnectionOptions) -> UISceneConfiguration {
        return UISceneConfiguration(name: "Default Configuration", sessionRole: connectingSceneSession.role)
    }

    func application(_ application: UIApplication, didDiscardSceneSessions sceneSessions: Set<UISceneSession>) {
    }

    func applicationDidBecomeActive(_ application: UIApplication) {


        //return; (本番用：コメントを付ける事、テスト用：コメントを外す事)
        
        // Pastbordを参照してログインをしているか確認する
        guard let uuid = UIDevice.current.identifierForVendor else { return }
        let board = UIPasteboard(name: UIPasteboard.Name(rawValue: uuid.uuidString), create: false)
        var isAuth = 0
        
        do {
            if let board = board {
                isAuth = 1
                let items = board.items
                var userId: String?
                var tenpoCd: String?
                var loginTime: String?
                
                for dic in items {
                    for (key, value) in dic {
                        if key == "userId" {
                            userId = dataToString(value)
                        } else if key == "tenpoCode" {
                            tenpoCd = dataToString(value)
                        } else if key == "loginTime" {
                            loginTime = dataToString(value)
                        }
                    }
                }
                
                if let loginTime = loginTime, !loginTime.isEmpty {
                    let formatter = DateFormatter()
                    formatter.dateFormat = "yyyy/MM/dd HH:mm:ss"
                    if let loginDate = formatter.date(from: loginTime) {
                        let nowDate = Date()
                        let since = nowDate.timeIntervalSince(loginDate)
                        let sinceTime = since * 1.0
                        isAuth = sinceTime > 0.0 ? 2 : 0
                    }
                }
            } else {
                isAuth = 1
            }
        } catch {
            isAuth = 1
        }
        
        if isAuth == 1 {
            let alert = UIAlertController(title: "お知らせ", 
                                        message: "ログインされていません。\n営業支援システムでログイン\nしてください。", 
                                        preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "OK", style: .default))
            window?.rootViewController?.present(alert, animated: true)
        } else if isAuth == 2 {
            let alert = UIAlertController(title: "お知らせ", 
                                        message: "ログインが無効になりました。\n営業支援システムで再度ログインして\nください。", 
                                        preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "OK", style: .default))
            window?.rootViewController?.present(alert, animated: true)
        }
    }
    
    func getUserName() -> String {
        var userId = ""
        guard let uuid = UIDevice.current.identifierForVendor else { return "" }
        let board = UIPasteboard(name: UIPasteboard.Name(rawValue: uuid.uuidString), create: false)
        
        if let board = board {
            let items = board.items
            for dic in items {
                for (key, value) in dic {
                    if key == "userName" {
                        userId = dataToString(value)
                    }
                }
            }
        }

        userId = "テスト設定担当者" //(本番用：コメントを付ける事、テスト用：コメントを外す事)

        return userId
    }

    func dataToString(_ value: Any) -> String {
        if let stringValue = value as? String {
            return stringValue
        } else if let dataValue = value as? Data {
            return String(data: dataValue, encoding: .utf8) ?? ""
        }
        return ""
    }
}

