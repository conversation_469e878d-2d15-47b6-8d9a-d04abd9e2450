//
//  Shaders.metal
//  MatSimulator
//
//  Created by Clamour on 2025/02/15.
//

#include <metal_stdlib>
using namespace metal;

struct VertexIn {
    float4 position [[attribute(0)]];
    float2 texCoord [[attribute(1)]];
};

struct VertexOut {
    float4 position [[position]];
    float2 texCoord;
};

vertex VertexOut vertex_shader(VertexIn in [[stage_in]],
                              constant float4x4 &modelMatrix [[buffer(1)]],
                              constant float2 *cornerDisplacements [[buffer(2)]]) {
    VertexOut out;
    
    float2 topLeftDisp = cornerDisplacements[0];
    float2 topRightDisp = cornerDisplacements[1];
    float2 bottomLeftDisp = cornerDisplacements[2];
    float2 bottomRightDisp = cornerDisplacements[3];

    float u = in.texCoord.x;
    float v = in.texCoord.y;
    float2 topDisp = topLeftDisp * (1.0 - u) + topRightDisp * u;
    float2 bottomDisp = bottomLeftDisp * (1.0 - u) + bottomRightDisp * u;
    float2 displacement = topDisp * v + bottomDisp * (1.0 - v);
    
    float4 displacedPos = in.position + float4(displacement, 0, 0);
    out.position = modelMatrix * displacedPos;
    out.texCoord = in.texCoord;
    
    return out;
}

fragment float4 fragment_shader(VertexOut in [[stage_in]],
                               texture2d<float> texture [[texture(0)]],
                               sampler textureSampler [[sampler(0)]],
                               constant float4 &color [[buffer(1)]]) {
    float4 texColor = texture.sample(textureSampler, in.texCoord);
    return texColor; 
}
