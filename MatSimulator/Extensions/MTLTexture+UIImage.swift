import Metal
import UIKit

extension MTLTexture {
    func toUIImage() -> UIImage? {
        let width = self.width
        let height = self.height
        let rowBytes = width * 4
        let totalBytes = rowBytes * height
        
        // RGB色空間を作成
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        // データバッファを初期化
        var data = [UInt8](repeating: 0, count: totalBytes)
        
        // テクスチャの領域を定義
        let region = MTLRegionMake2D(0, 0, width, height)
        // テクスチャからデータを取得
        self.getBytes(&data, bytesPerRow: rowBytes, from: region, mipmapLevel: 0)
        
        // 色の順序を変換（BGRAからRGBA）
        for i in stride(from: 0, to: totalBytes, by: 4) {
            let temp = data[i]
            data[i] = data[i + 2]
            data[i + 2] = temp
        }
        
        // CGContextを作成
        guard let context = CGContext(
            data: &data,
            width: width,
            height: height,
            bitsPerComponent: 8,
            bytesPerRow: rowBytes,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue
        ) else { return nil }
        
        // CGImageを作成
        guard let cgImage = context.makeImage() else { return nil }
        // UIImageを返す
        return UIImage(cgImage: cgImage)
    }
}
