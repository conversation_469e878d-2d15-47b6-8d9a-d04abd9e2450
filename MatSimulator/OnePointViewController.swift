//
//  OnePointViewController.swift
//  MatSimulator
//
//  Created by Clamour on 2025/03/09.
//

import UIKit

// ワンポイント選択画面からのデリゲートプロトコル
protocol OnePointViewControllerDelegate: AnyObject {
    func onePointViewController(_ controller: OnePointViewController, didSelectImageAt path: String)
}

class OnePointViewController: UIViewController {
    
    // MARK: - Properties
    weak var delegate: OnePointViewControllerDelegate?
    
    // 画像を表示するコレクションビュー
    private let collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = 10
        layout.minimumInteritemSpacing = 10
        layout.sectionInset = UIEdgeInsets(top: 10, left: 10, bottom: 10, right: 10)
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .white
        collectionView.showsVerticalScrollIndicator = false
        collectionView.register(OnePointImageCell.self, forCellWithReuseIdentifier: "ImageCell")
        return collectionView
    }()
    
    // カテゴリーを表示するテーブルビュー
    private let tableView: UITableView = {
        let tableView = UITableView()
        tableView.backgroundColor = .white
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "CategoryCell")
        tableView.showsVerticalScrollIndicator = false
        return tableView
    }()
    
    // カテゴリーリスト
    private var categories: [String] = ["ワンポイント", "ピクトサイン", "メッセージパターン"]
    private var allCategories: [String] = ["ワンポイント", "ピクトサイン", "メッセージパターン"]
    // 選択されたカテゴリーのインデックス
    private var selectedCategoryIndex = 0
    // 現在表示中の画像パス配列
    private var currentImages: [String] = []
    // 画像の名前を保存する配列
    private var imageNames: [String] = []
    // ファイルダウンローダーインスタンス
    private let downloader = FileDownloader()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupDelegates()
        loadCategories()
        loadImages()
    }
    
    private func setupUI() {
        view.backgroundColor = .systemBackground
        title = "ワンポイント他"
        
        view.addSubview(tableView)
        view.addSubview(collectionView)
        
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            image: UIImage(systemName: "xmark"),
            style: .plain,
            target: self,
            action: #selector(dismissViewController)
        )
        
        let closeButton = UIButton(type: .system)
        closeButton.setTitle("閉じる", for: .normal)
        closeButton.addTarget(self, action: #selector(dismissViewController), for: .touchUpInside)
        closeButton.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(closeButton)
        
        NSLayoutConstraint.activate([
            closeButton.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 10),
            closeButton.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20)
        ])
        
        tableView.backgroundColor = .systemGray6
        collectionView.backgroundColor = .white
    }
    
    private func setupConstraints() {
        tableView.translatesAutoresizingMaskIntoConstraints = false
        collectionView.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            tableView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            tableView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            tableView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor),
            tableView.widthAnchor.constraint(equalTo: view.widthAnchor, multiplier: 0.3),
            
            collectionView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            collectionView.leadingAnchor.constraint(equalTo: tableView.trailingAnchor),
            collectionView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            collectionView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor)
        ])
    }
    
    private func setupDelegates() {
        tableView.delegate = self
        tableView.dataSource = self
        collectionView.delegate = self
        collectionView.dataSource = self
    }
    
    @objc private func dismissViewController() {
        dismiss(animated: true)
    }
    
    private func loadCategories() {
    }
    
    private func loadImages() {
        if !categories.isEmpty {
            loadImagesFromCSV(for: categories[selectedCategoryIndex])
        }
    }

    private func loadImagesFromCSV(for category: String) {
        currentImages.removeAll()
        imageNames.removeAll()
        
        let csvPath = "\(downloader.matRootPath)標準ロゴ/\(category)/\(category)情報.csv"
        
        do {
            let csvContent = try String(contentsOfFile: csvPath, encoding: .utf8)
            let rows = csvContent.components(separatedBy: "\n")
            
            // CSVの各行を処理
            for (_, row) in rows.enumerated() {
                if row.isEmpty { continue }
                
                let columns = row.components(separatedBy: ",")
                if columns.count >= 2 {
                    let fileName = columns[0].trimmingCharacters(in: .whitespacesAndNewlines)
                    let displayName = fileName
                    
                    let imagePath = "標準ロゴ/\(category)/\(fileName)"
                    
                    currentImages.append(imagePath)
                    imageNames.append(displayName)
                    
                }
            }
            
            collectionView.reloadData()
            
        } catch {
            
            let files = downloader.getAllFilesInDirectory()
            
            currentImages = files.filter { path in
                let categoryPath = "標準ロゴ/\(category)/"
                let isThumb = path.hasSuffix("_thum.png") || path.hasSuffix(".png")
                return path.contains(categoryPath) && isThumb
            }
            
            imageNames = currentImages.map { path in
                let components = path.components(separatedBy: "/")
                return components.last ?? ""
            }
            
            collectionView.reloadData()
        }
    }

    private func loadImages(for category: String) {
        let files = downloader.getAllFilesInDirectory()
        
        currentImages = files.filter { path in
            let categoryPath = "標準ロゴ/\(category)/"
            let isThumb = path.hasSuffix("_thum.png") || path.hasSuffix(".png")
            print("aqaz path: \(path)")
            return path.contains(categoryPath) && isThumb
        }
        
        collectionView.reloadData()
    }
    

    private func filterImagesBySelectedCategory() {
        if !categories.isEmpty {
            loadImages(for: categories[selectedCategoryIndex])
        }
    }

    func updateCategoryVisibility(showOnePointAndPict: Bool) {
        if showOnePointAndPict {
            categories = allCategories
        } else {
            categories = allCategories.filter { $0 == "メッセージパターン" }
        }
        selectedCategoryIndex = 0
        tableView.reloadData()
        loadImagesFromCSV(for: categories[selectedCategoryIndex])
    }
}

// MARK: - UITableViewDelegate, UITableViewDataSource
extension OnePointViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return categories.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "CategoryCell", for: indexPath)
        cell.textLabel?.text = categories[indexPath.row]
        cell.textLabel?.font = UIFont.systemFont(ofSize: 14)
        
        if indexPath.row == selectedCategoryIndex {
            cell.backgroundColor = UIColor.systemGray5
            cell.textLabel?.textColor = .systemBlue
        } else {
            cell.backgroundColor = .white
            cell.textLabel?.textColor = .darkText
        }
        
        return cell
    }
    
    // テーブルの行が選択された時の処理
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        selectedCategoryIndex = indexPath.row
        tableView.reloadData()
        loadImagesFromCSV(for: categories[selectedCategoryIndex])
    }
    
    // テーブルの行の高さを返す
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 50
    }
}

extension OnePointViewController: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    // コレクションビューのアイテム数を返す
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return currentImages.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "ImageCell", for: indexPath) as? OnePointImageCell else {
            return UICollectionViewCell()
        }
        
        let imagePath = currentImages[indexPath.item]
        let fullPath = "\(downloader.matRootPath)\(imagePath)"
        
        let imageName = indexPath.item < imageNames.count ? imageNames[indexPath.item] : ""
        
        if let image = UIImage(contentsOfFile: fullPath) {
            cell.configure(with: image, name: imageName)
        }
        
        return cell
    }
    
    // コレクションビューのセルのサイズを返す
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let width = (collectionView.bounds.width - 40) / 3
        return CGSize(width: width, height: width * 1.2)
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let imagePath = currentImages[indexPath.item]
        var fullPath = "\(downloader.matRootPath)\(imagePath)"
        
        if !fullPath.hasSuffix(".png") && !fullPath.hasSuffix(".jpg") && !fullPath.hasSuffix(".jpeg") {
            fullPath += ".png"
        }
        
        if let delegate = delegate {
            delegate.onePointViewController(self, didSelectImageAt: fullPath)
        }
        
        dismiss(animated: true)
    }
}

// MARK: - Custom Cells
// ワンポイント画像を表示するためのカスタムセル
class OnePointImageCell: UICollectionViewCell {
    // 画像を表示するイメージビュー
    let imageView: UIImageView = {
        let iv = UIImageView()
        iv.contentMode = .scaleAspectFit
        iv.translatesAutoresizingMaskIntoConstraints = false
        return iv
    }()

    // ファイル名を表示するラベル
    let nameLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        label.textColor = .black
        label.textAlignment = .right
        label.backgroundColor = UIColor.white.withAlphaComponent(0.7)
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    // 初期化処理
    override init(frame: CGRect) {
        super.init(frame: frame)
        contentView.addSubview(imageView)
        contentView.addSubview(nameLabel)
        NSLayoutConstraint.activate([
            imageView.topAnchor.constraint(equalTo: contentView.topAnchor),
            imageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            imageView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            imageView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -20),
            
            nameLabel.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -2),
            nameLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 2),
            nameLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -2)
        ])
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // セルを画像と名前で設定するメソッド
    func configure(with image: UIImage, name: String) {
        imageView.image = image
        nameLabel.text = name
        nameLabel.isHidden = false
        
        contentView.bringSubviewToFront(nameLabel)
    }
    
    // セルの再利用前の準備
    override func prepareForReuse() {
        super.prepareForReuse()
        imageView.image = nil
        nameLabel.text = nil
    }
}
