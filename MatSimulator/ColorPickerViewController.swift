//
//  ColorPickerViewController.swift
//  MatSimulator
//
//  Created by Clamour on 2025/03/13.
//

import UIKit

protocol ColorPickerViewControllerDelegate: AnyObject {
    func colorPickerViewController(_ controller: ColorPickerViewController, didSelectColor color: UIColor)
    func colorPickerViewController(_ controller: ColorPickerViewController, didReplaceColor originalColor: UIColor, withColor newColor: UIColor, sourceImage: UIImage?)
}

class ColorPickerViewController: UIViewController {
    
    weak var delegate: ColorPickerViewControllerDelegate?
    
    private var selectedImageColor: UIColor?
    private var selectedCommonColor: UIColor?
    
    private var sourceImage: UIImage?
    private var extractedColors: [UIColor] = []
    
    private var showOnlyType0Colors: Bool = false
    private var commonColors: [UIColor] = []
    private var type1Colors: [UIColor] = []
    private var type2Colors: [UIColor] = []
    
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 10
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()
    
    private lazy var closeButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("閉じる", for: .normal)
        button.addTarget(self, action: #selector(closeButtonTapped), for: .touchUpInside)
        button.translatesAutoresizingMaskIntoConstraints = false
        return button
    }()
    
    private lazy var dividerView: UIView = {
        let view = UIView()
        view.backgroundColor = .lightGray
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()
    
    private lazy var leftContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()
    
    private lazy var rightContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()
    
    private lazy var leftTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "画像の色"
        label.font = .systemFont(ofSize: 16, weight: .medium)
        label.textAlignment = .center
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    private lazy var basicColorsLabel: UILabel = {
        let label = UILabel()
        label.text = "基本色"
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textAlignment = .left
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    private lazy var mixedColorsLabel: UILabel = {
        let label = UILabel()
        label.text = "混合色"
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textAlignment = .left
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    private lazy var extractedColorsCollectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = 2
        layout.minimumInteritemSpacing = 2
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .white
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(ColorCell.self, forCellWithReuseIdentifier: "ColorCell")
        collectionView.translatesAutoresizingMaskIntoConstraints = false
        collectionView.showsVerticalScrollIndicator = true
        collectionView.alwaysBounceVertical = true
        return collectionView
    }()
    
    private lazy var commonColorsCollectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = 10
        layout.minimumInteritemSpacing = 10
        layout.headerReferenceSize = CGSize(width: 0, height: 30)
        layout.footerReferenceSize = CGSize(width: 0, height: 20)
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .white
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(ColorCell.self, forCellWithReuseIdentifier: "ColorCell")
        collectionView.register(UICollectionReusableView.self, forSupplementaryViewOfKind: UICollectionView.elementKindSectionHeader, withReuseIdentifier: "HeaderView")
        collectionView.register(UICollectionReusableView.self, forSupplementaryViewOfKind: UICollectionView.elementKindSectionFooter, withReuseIdentifier: "FooterView")
        collectionView.translatesAutoresizingMaskIntoConstraints = false
        return collectionView
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        setupColors()

        setupUI()

        if !extractedColors.isEmpty {
            let indexPath = IndexPath(item: 0, section: 0)
            extractedColorsCollectionView.selectItem(at: indexPath, animated: false, scrollPosition: .top)
            selectedImageColor = extractedColors[0]
        }
    }
    
    private func setupColors() {
        if showOnlyType0Colors {
            commonColors = DuskinColors.colors
                .filter { $0.type == 0 }
                .sorted { $0.no < $1.no }
                .map { $0.uiColor }
            type1Colors = []
            type2Colors = []
        } else {
            type1Colors = DuskinColors.colors
                .filter { $0.type == 1 }
                .sorted { $0.no < $1.no }
                .map { $0.uiColor }
            
            type2Colors = DuskinColors.colors
                .filter { $0.type == 2 }
                .sorted { $0.no < $1.no }
                .map { $0.uiColor }
            
            commonColors = type1Colors + type2Colors
        }
    }
    
    private func setupUI() {
        view.addSubview(containerView)
        
        containerView.addSubview(closeButton)
        containerView.addSubview(dividerView)
        containerView.addSubview(leftContainerView)
        containerView.addSubview(rightContainerView)
        
        leftContainerView.addSubview(leftTitleLabel)
        leftContainerView.addSubview(extractedColorsCollectionView)
        
        rightContainerView.addSubview(commonColorsCollectionView)
        
        NSLayoutConstraint.activate([
            containerView.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            containerView.centerYAnchor.constraint(equalTo: view.centerYAnchor),
            containerView.widthAnchor.constraint(equalTo: view.widthAnchor, multiplier: 0.8),
            containerView.heightAnchor.constraint(equalTo: view.heightAnchor, multiplier: 0.8),
            
            closeButton.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 20),
            closeButton.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),

            dividerView.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 50),
            dividerView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            dividerView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            dividerView.heightAnchor.constraint(equalToConstant: 1),
            
            leftContainerView.topAnchor.constraint(equalTo: dividerView.bottomAnchor),
            leftContainerView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            leftContainerView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor),
            leftContainerView.widthAnchor.constraint(equalTo: containerView.widthAnchor, multiplier: 0.15),
            
            rightContainerView.topAnchor.constraint(equalTo: dividerView.bottomAnchor),
            rightContainerView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            rightContainerView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor),
            rightContainerView.leadingAnchor.constraint(equalTo: leftContainerView.trailingAnchor),
            
            leftTitleLabel.topAnchor.constraint(equalTo: leftContainerView.topAnchor, constant: 20),
            leftTitleLabel.leadingAnchor.constraint(equalTo: leftContainerView.leadingAnchor),
            leftTitleLabel.trailingAnchor.constraint(equalTo: leftContainerView.trailingAnchor),
            
            extractedColorsCollectionView.topAnchor.constraint(equalTo: leftTitleLabel.bottomAnchor, constant: 20),
            extractedColorsCollectionView.leadingAnchor.constraint(equalTo: leftContainerView.leadingAnchor, constant: 20),
            extractedColorsCollectionView.trailingAnchor.constraint(equalTo: leftContainerView.trailingAnchor, constant: -20),
            extractedColorsCollectionView.bottomAnchor.constraint(equalTo: leftContainerView.bottomAnchor, constant: -20),
        ])
        
        NSLayoutConstraint.activate([
            commonColorsCollectionView.topAnchor.constraint(equalTo: rightContainerView.topAnchor, constant: 20),
            commonColorsCollectionView.leadingAnchor.constraint(equalTo: rightContainerView.leadingAnchor, constant: 20),
            commonColorsCollectionView.trailingAnchor.constraint(equalTo: rightContainerView.trailingAnchor, constant: -20),
            commonColorsCollectionView.bottomAnchor.constraint(equalTo: rightContainerView.bottomAnchor, constant: -20)
        ])
    }
    
    func setSourceImage(_ image: UIImage) {
        sourceImage = image
        extractedColors = extractColors(from: image)
        extractedColorsCollectionView.reloadData()
        
        selectedImageColor = nil
        selectedCommonColor = nil
        extractedColorsCollectionView.indexPathsForSelectedItems?.forEach {
            extractedColorsCollectionView.deselectItem(at: $0, animated: false)
        }
        commonColorsCollectionView.indexPathsForSelectedItems?.forEach {
            commonColorsCollectionView.deselectItem(at: $0, animated: false)
        }

        if !extractedColors.isEmpty {
            let indexPath = IndexPath(item: 0, section: 0)
            extractedColorsCollectionView.selectItem(at: indexPath, animated: false, scrollPosition: .top)
            selectedImageColor = extractedColors[0]
        }
    }
    
    private func extractColors(from image: UIImage, maxColors: Int = 16) -> [UIColor] {
        guard let cgImage = image.cgImage else { return [] }
        
        let width = cgImage.width
        let height = cgImage.height
        
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let bitmapInfo = CGBitmapInfo(rawValue: CGImageAlphaInfo.premultipliedLast.rawValue)
        guard let context = CGContext(data: nil,
                                      width: width,
                                      height: height,
                                      bitsPerComponent: 8,
                                      bytesPerRow: width * 4,
                                      space: colorSpace,
                                      bitmapInfo: bitmapInfo.rawValue) else { return [] }
        
        context.interpolationQuality = .none
        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))
        
        guard let pixelData = context.data else { return [] }
        
        var colorCounts: [UIColor: Int] = [:]
        let totalPixels = width * height
        let samplingStep = max(1, totalPixels / 10000)
        
        for i in stride(from: 0, to: totalPixels * 4, by: 4 * samplingStep) {
            let pixelInfo = pixelData.advanced(by: i).assumingMemoryBound(to: UInt8.self)
            
            let r = CGFloat(pixelInfo[0]) / 255.0
            let g = CGFloat(pixelInfo[1]) / 255.0
            let b = CGFloat(pixelInfo[2]) / 255.0
            let a = CGFloat(pixelInfo[3]) / 255.0
            
            if a < 0.1 { continue }
            
            let color = UIColor(red: r, green: g, blue: b, alpha: 1.0)
            colorCounts[color, default: 0] += 1
        }
        
        let sortedColors = colorCounts
            .map { ($0.key, $0.value) }
            .sorted { $0.1 > $1.1 }
            .map { $0.0 }
                
        return Array(sortedColors.prefix(min(sortedColors.count, maxColors)))
    }

    private func findClosestDuskinColor(_ color: UIColor, from duskinColors: [DuskinColor]) -> UIColor {
        var r: CGFloat = 0, g: CGFloat = 0, b: CGFloat = 0, a: CGFloat = 0
        color.getRed(&r, green: &g, blue: &b, alpha: &a)
        
        var closestColor = duskinColors[0]
        var minDistance = CGFloat.greatestFiniteMagnitude
        
        for duskinColor in duskinColors {
            let distance = sqrt(pow(r - duskinColor.r, 2) + pow(g - duskinColor.g, 2) + pow(b - duskinColor.b, 2))
            if distance < minDistance {
                minDistance = distance
                closestColor = duskinColor
            }
        }
        
        return closestColor.uiColor
    }
    
    @objc private func closeButtonTapped() {
        dismiss(animated: true)
    }

    func setShowOnlyType0Colors(_ show: Bool) {
        showOnlyType0Colors = show
        setupColors()
        
        for subview in rightContainerView.subviews {
            subview.removeFromSuperview()
        }
        
        rightContainerView.addSubview(commonColorsCollectionView)
        
        NSLayoutConstraint.activate([
            commonColorsCollectionView.topAnchor.constraint(equalTo: rightContainerView.topAnchor, constant: 20),
            commonColorsCollectionView.leadingAnchor.constraint(equalTo: rightContainerView.leadingAnchor, constant: 20),
            commonColorsCollectionView.trailingAnchor.constraint(equalTo: rightContainerView.trailingAnchor, constant: -20),
            commonColorsCollectionView.bottomAnchor.constraint(equalTo: rightContainerView.bottomAnchor, constant: -20)
        ])
        
        commonColorsCollectionView.reloadData()
    }
}

// MARK: - UICollectionViewDataSource
extension ColorPickerViewController: UICollectionViewDataSource {
    // コレクションビューのセクション内のアイテム数を返す
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if collectionView == extractedColorsCollectionView {
            return extractedColors.count
        } else {
            if !showOnlyType0Colors && !type1Colors.isEmpty && !type2Colors.isEmpty {
                return section == 0 ? type1Colors.count : type2Colors.count
            } else {
                return commonColors.count
            }
        }
    }
    
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        if collectionView == extractedColorsCollectionView {
            return 1
        } else {
            if !showOnlyType0Colors && !type1Colors.isEmpty && !type2Colors.isEmpty {
                return 2
            } else {
                return 1
            }
        }
    }
    
    // 各セルの設定
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "ColorCell", for: indexPath) as! ColorCell
        
        if collectionView == extractedColorsCollectionView {
            cell.configure(with: extractedColors[indexPath.item])
        } else {
            if !showOnlyType0Colors && !type1Colors.isEmpty && !type2Colors.isEmpty {
                if indexPath.section == 0 {
                    cell.configure(with: type1Colors[indexPath.item])
                } else {
                    cell.configure(with: type2Colors[indexPath.item])
                }
            } else {
                cell.configure(with: commonColors[indexPath.item])
            }
        }
        
        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension ColorPickerViewController: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if collectionView == extractedColorsCollectionView {
            selectedImageColor = extractedColors[indexPath.item]

            if let selectedIndexPath = commonColorsCollectionView.indexPathsForSelectedItems?.first {
                commonColorsCollectionView.deselectItem(at: selectedIndexPath, animated: true)
            }
            selectedCommonColor = nil
            
        } else {
            if !showOnlyType0Colors && !type1Colors.isEmpty && !type2Colors.isEmpty {
                if indexPath.section == 0 {
                    selectedCommonColor = type1Colors[indexPath.item]
                } else {
                    selectedCommonColor = type2Colors[indexPath.item]
                }
            } else {
                selectedCommonColor = commonColors[indexPath.item]
            }
            
            if let originalColor = selectedImageColor, let newColor = selectedCommonColor {
                delegate?.colorPickerViewController(self, didReplaceColor: originalColor, withColor: newColor, sourceImage: sourceImage)

                if let selectedIndexPath = extractedColorsCollectionView.indexPathsForSelectedItems?.first {
                    extractedColorsCollectionView.deselectItem(at: selectedIndexPath, animated: true)
                }
                selectedImageColor = nil
                
                if let selectedIndexPath = commonColorsCollectionView.indexPathsForSelectedItems?.first {
                    commonColorsCollectionView.deselectItem(at: selectedIndexPath, animated: true)
                }
                selectedCommonColor = nil
            } else {
                delegate?.colorPickerViewController(self, didSelectColor: selectedCommonColor!)

                if let selectedIndexPath = commonColorsCollectionView.indexPathsForSelectedItems?.first {
                    commonColorsCollectionView.deselectItem(at: selectedIndexPath, animated: true)
                }
                selectedCommonColor = nil
            }
        }
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension ColorPickerViewController: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        if collectionView == extractedColorsCollectionView {
            let width = collectionView.bounds.width - 10
            return CGSize(width: width, height: width * 0.7)
        } else {
            let width = (collectionView.bounds.width - 70) / 8
            return CGSize(width: width, height: width)
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, referenceSizeForHeaderInSection section: Int) -> CGSize {
        if collectionView == commonColorsCollectionView && !showOnlyType0Colors && !type1Colors.isEmpty && !type2Colors.isEmpty {
            return CGSize(width: collectionView.bounds.width, height: 30)
        }
        return .zero
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, referenceSizeForFooterInSection section: Int) -> CGSize {
        if collectionView == commonColorsCollectionView && !showOnlyType0Colors && !type1Colors.isEmpty && !type2Colors.isEmpty && section == 0 {
            return CGSize(width: collectionView.bounds.width, height: 20)
        }
        return .zero
    }

    func collectionView(_ collectionView: UICollectionView, viewForSupplementaryElementOfKind kind: String, at indexPath: IndexPath) -> UICollectionReusableView {
        if kind == UICollectionView.elementKindSectionHeader && collectionView == commonColorsCollectionView && !showOnlyType0Colors {
            let headerView = collectionView.dequeueReusableSupplementaryView(ofKind: kind, withReuseIdentifier: "HeaderView", for: indexPath)
            
            headerView.subviews.forEach { $0.removeFromSuperview() }
            
            let label = UILabel()
            label.translatesAutoresizingMaskIntoConstraints = false
            label.font = .systemFont(ofSize: 14, weight: .medium)
            
            if indexPath.section == 0 {
                label.text = "基本色"
            } else {
                label.text = "混合色"
            }
            
            headerView.addSubview(label)
            
            NSLayoutConstraint.activate([
                label.leadingAnchor.constraint(equalTo: headerView.leadingAnchor, constant: 5),
                label.trailingAnchor.constraint(equalTo: headerView.trailingAnchor, constant: -5),
                label.centerYAnchor.constraint(equalTo: headerView.centerYAnchor)
            ])
            
            return headerView
        }
        
        return collectionView.dequeueReusableSupplementaryView(ofKind: kind, withReuseIdentifier: "FooterView", for: indexPath)
    }
}

class ColorCell: UICollectionViewCell {
    private let colorView: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 4
        view.layer.borderWidth = 1
        view.layer.borderColor = UIColor.lightGray.cgColor
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()
    
    private let selectionIndicator: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 10
        view.layer.borderWidth = 2
        view.layer.borderColor = UIColor.systemBlue.cgColor
        view.backgroundColor = .clear
        view.isHidden = true
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        contentView.addSubview(colorView)
        contentView.addSubview(selectionIndicator)
        
        NSLayoutConstraint.activate([
            colorView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 4),
            colorView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 4),
            colorView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -4),
            colorView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -4),
            
            selectionIndicator.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 2),
            selectionIndicator.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 2),
            selectionIndicator.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -2),
            selectionIndicator.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -2)
        ])
    }
    
    func configure(with color: UIColor) {
        colorView.backgroundColor = color
        
        var red: CGFloat = 0, green: CGFloat = 0, blue: CGFloat = 0, alpha: CGFloat = 0
            color.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
            
            let brightness = (red * 299 + green * 587 + blue * 114) / 1000
            colorView.layer.borderColor = brightness < 0.5 ? UIColor.white.cgColor : UIColor.black.cgColor
    }

    override var isSelected: Bool {
        didSet {
            colorView.layer.borderWidth = isSelected ? 2 : 1
            selectionIndicator.isHidden = !isSelected
            
            if isSelected {
                contentView.transform = CGAffineTransform(scaleX: 1.05, y: 1.05)
                UIView.animate(withDuration: 0.2) {
                    self.contentView.transform = .identity
                }
            }
        }
    }
}
