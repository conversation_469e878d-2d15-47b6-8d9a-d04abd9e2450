//
//  ViewController.swift
//  MatSimulator
//
//  Created by Clamour on 2025/02/06.
//

import UIKit
import Metal
import MetalKit
import AVFoundation

typealias TargetDimensions = (scale: SIMD2<Float>, aspectRatio: Float, textureSize: CGSize)

class ViewController: UIViewController {
    var metalView: MTKView!
    var renderer: Renderer!
    private var rotePointImageView: UIImageView?

    // Text positioning manager
    private let textPositionManager = TextPositionManager()

    var currentAngle: CGFloat {
        return CGFloat(metalAngleToUIKit(Float(metalAngle)))
    }
    var metalAngle: CGFloat = 0
    let rotePointImageSize: CGSize = CGSizeMake(40, 40)

    private var selectedCorner: Int?
    private var lastPanLocation: CGPoint = .zero
    private var currentTextInputView: UITextView?
    private var lastCenterPanLocation: CGPoint = .zero
    private var hasShownTextInsertAlertInCurrentSession = false
    var dimensions: TargetDimensions? = nil

    private lazy var backgroundImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.translatesAutoresizingMaskIntoConstraints = false
        return imageView
    }()

    let downloader = FileDownloader()

    private lazy var activityIndicator: UIActivityIndicatorView = {
        let indicator = UIActivityIndicatorView(style: .large)
        indicator.color = .white
        indicator.hidesWhenStopped = true
        indicator.translatesAutoresizingMaskIntoConstraints = false
        indicator.backgroundColor = UIColor.black.withAlphaComponent(0.7)
        indicator.layer.cornerRadius = 10
        return indicator
    }()

    private lazy var loadingContainer: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.7)
        view.layer.cornerRadius = 10
        view.isHidden = true
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()

    private lazy var loadingLabel: UILabel = {
        let label = UILabel()
        label.text = "ダウンロード中..." // ダウンロード進捗表示用ラベル
        label.textColor = .white
        label.font = .systemFont(ofSize: 15)
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()

    private lazy var menuBar: MenuBarView = {
        let menuBar = MenuBarView()
        menuBar.delegate = self
        menuBar.translatesAutoresizingMaskIntoConstraints = false
        return menuBar
    }()

    // ユーザー操作モード列挙型
    enum InteractionMode {
        case transform  // 変形モード（移動/回転/拡縮）
        case deform     // 変形モード（コーナー変形）
        case eraser     // 消しゴムモード
        case restore    // 復元モード
        case select     // 選択モード
        case rectangle  // 四角形選択モード
        case polygon    // 多角形選択モード
        case freeform   // 自由曲線選択モード
        case none
    }

    private var currentMode: InteractionMode = .transform

    private var accumulatedRotation: Float = 0.0

    private var cornerControlViews: [UIImageView] = []

    private var selectionBoxView: UIView?

    private var restoreIndicatorView: UIView?

    private var firstRectPoint: CGPoint?
    private var rectangleSelectionView: UIView?
    private var rectangleSelectionLayer: CAShapeLayer?
    private var rectangleFirstPointMarker: UIImageView?

    private var polygonPoints: [CGPoint] = []
    private var polygonSelectionView: UIView?
    private var polygonSelectionLayer: CAShapeLayer?

    private var freeformPoints: [CGPoint] = []
    private var freeformSelectionView: UIView?
    private var freeformSelectionLayer: CAShapeLayer?
    private var isDrawingFreeform: Bool = false

    private var initialScale: SIMD2<Float>?

    private var textPositionPopupView: UIView?

    override func viewDidLoad() {
        super.viewDidLoad()
        let backgroundLayer = CALayer()
        backgroundLayer.contents = UIImage(named: "CheckBackground")?.cgImage
        backgroundLayer.frame = CGRect(x: 240, y: 0, width: view.bounds.width - 240, height: view.bounds.height)
        view.layer.addSublayer(backgroundLayer)

        view.addSubview(backgroundImageView)
        NSLayoutConstraint.activate([
            backgroundImageView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 240),
            backgroundImageView.topAnchor.constraint(equalTo: view.topAnchor),
            backgroundImageView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            backgroundImageView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])

        view.addSubview(menuBar)
        NSLayoutConstraint.activate([
            menuBar.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            menuBar.topAnchor.constraint(equalTo: view.topAnchor),
            menuBar.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            menuBar.widthAnchor.constraint(equalToConstant: 240)
        ])

        metalView = MTKView(frame: CGRect(x: 240, y: 0, width: view.bounds.width - 240, height: view.bounds.height))
        metalView.device = MTLCreateSystemDefaultDevice()
        metalView.backgroundColor = .clear
        metalView.isOpaque = false
        metalView.clearColor = MTLClearColorMake(0, 0, 0, 0)
        metalView.framebufferOnly = false
        view.addSubview(metalView)
        renderer = Renderer(metalView: metalView)
        metalView.delegate = renderer
        view.addSubview(loadingContainer)
        loadingContainer.addSubview(activityIndicator)
        loadingContainer.addSubview(loadingLabel)
        NSLayoutConstraint.activate([
            loadingContainer.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            loadingContainer.centerYAnchor.constraint(equalTo: view.centerYAnchor),
            loadingContainer.widthAnchor.constraint(equalToConstant: 120),
            loadingContainer.heightAnchor.constraint(equalToConstant: 120),
            activityIndicator.centerXAnchor.constraint(equalTo: loadingContainer.centerXAnchor),
            activityIndicator.centerYAnchor.constraint(equalTo: loadingContainer.centerYAnchor, constant: -10),
            loadingLabel.centerXAnchor.constraint(equalTo: loadingContainer.centerXAnchor),
            loadingLabel.topAnchor.constraint(equalTo: activityIndicator.bottomAnchor, constant: 8)
        ])

        addCornerGestureRecognizers() // コーナー用ジェスチャー認識を追加
        addEraserGestureRecognizer()
    }

    private func addCornerGestureRecognizers() {
        let pan = UIPanGestureRecognizer(target: self, action: #selector(handleCornerPan(_:)))
        pan.delegate = self
        metalView.addGestureRecognizer(pan)
    }

    private func startDownload() {
        let alert = UIAlertController(
            title: "確認",
            message: "マット画像をダウンロードしますか？",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "キャンセル", style: .cancel))
        alert.addAction(UIAlertAction(title: "OK", style: .default) { [weak self] _ in
            self?.loadingContainer.isHidden = false
            self?.activityIndicator.startAnimating()

            Task {
                do {
                    let success = try await self?.downloader.downloadImgFile() ?? false

                    await MainActor.run {
                        self?.loadingContainer.isHidden = true
                        self?.activityIndicator.stopAnimating()

                        let alert = UIAlertController(
                            title: success ? "マット画像取得が完了しました" : "マット画像取得が失敗しました",
                            message: nil,
                            preferredStyle: .alert
                        )
                        alert.addAction(UIAlertAction(title: "OK", style: .default))
                        self?.present(alert, animated: true)
                    }
                } catch {
                    await MainActor.run {
                        self?.loadingContainer.isHidden = true
                        self?.activityIndicator.stopAnimating()

                        let alert = UIAlertController(
                            title: "ダウンロードエラー",
                            message: error.localizedDescription,
                            preferredStyle: .alert
                        )
                        alert.addAction(UIAlertAction(title: "OK", style: .default))
                        self?.present(alert, animated: true)
                    }
                }
            }
        })

        present(alert, animated: true)
    }

    private func addEraserGestureRecognizer() {
        // 既存のジェスチャー認識器を削除
        metalView.gestureRecognizers?.forEach { gesture in
            if gesture is UIPanGestureRecognizer || gesture is UITapGestureRecognizer {
                metalView.removeGestureRecognizer(gesture)
            }
        }

        let eraserGesture = UIPanGestureRecognizer(target: self, action: #selector(handleEraserGesture(_:)))
        eraserGesture.delegate = self
        metalView.addGestureRecognizer(eraserGesture)
    }

    @objc private func showMatCatalog(limitedMode: Bool = false) {
        let matCatalogVC = MatCatalogViewController(displayMode: limitedMode ? .limited : .all)
        matCatalogVC.delegate = self
        matCatalogVC.modalPresentationStyle = .formSheet
        matCatalogVC.preferredContentSize = CGSize(
            width: view.bounds.width * 0.8,
            height: view.bounds.height * 0.8
        )
        present(matCatalogVC, animated: true)
    }

    @objc private func showBackgroundImagePicker() {
        let backgroundVC = BackgroundImageViewController()
        backgroundVC.delegate = self
        backgroundVC.modalPresentationStyle = .formSheet
        backgroundVC.preferredContentSize = CGSize(
            width: view.bounds.width * 0.8,
            height: view.bounds.height * 0.8
        )
        present(backgroundVC, animated: true)
    }

    override var supportedInterfaceOrientations: UIInterfaceOrientationMask {
        return .landscape
    }

    override var shouldAutorotate: Bool {
        return true
    }
}

extension ViewController: BackgroundImageViewControllerDelegate {
    func backgroundImageViewController(_ controller: BackgroundImageViewController, didSelect image: UIImage) {
        backgroundImageView.image = image
        controller.dismiss(animated: true)
    }
}

extension ViewController: MatCatalogViewControllerDelegate {
    func matCatalogViewController(_ controller: MatCatalogViewController, didSelectImageNamed imageName: String, isEditMode: Bool, matInfo: MatInfo?) {
        let fullImageName = imageName.replacingOccurrences(of: "_thum", with: "")
        let fullPath = "\(downloader.matRootPath)\(fullImageName)"

        print("aqaz V matCatalogViewController matInfo:\(matInfo)")
        if let homeConfig = matInfo?.homeMatConfig {
            print("aqaz V homeMatConfig exists, positionList count: \(homeConfig.positionList.count)")
            print("aqaz V defaultLabelPosition: \(homeConfig.defaultLabelPosition)")
            print("aqaz V textConfig.hasText: \(homeConfig.textConfig.hasText)")

            // デフォルト位置の情報を取得して表示
            if let defaultInfo = homeConfig.defaultPositionInfo {
                print("aqaz V defaultPositionInfo found: \(defaultInfo.labelPosition), enabled: \(defaultInfo.labelEnable)")
            } else {
                print("aqaz V defaultPositionInfo not found for \(homeConfig.defaultLabelPosition)")
            }
        }

        print("Selected image path: \(fullImageName)")
        print("Full path: \(fullPath)")

        // matType を取得
        let components = fullImageName.components(separatedBy: "/")
        let matType = components.count >= 2 ? components[1] : ""

        if FileManager.default.fileExists(atPath: fullPath) {
            print("matType:\(matType), isEditMode:\(isEditMode)")
            if isEditMode && renderer.currentSquare != nil {
                let currentMatType = renderer.currentSquare!.matType
                if currentMatType != matType {
                    clearAdditionalElements()
                }
            }
            let isReplacingExistingSquare = !isEditMode && renderer.currentSquare != nil
            print("aqaz V calling addSquare with matInfo: \(matInfo)")
            let success = renderer.addSquare(imageName: fullPath, squareType: 0, matType: matType, isEditMode: isEditMode, matInfo: matInfo)
            if success {
                if isEditMode {
                    renderer.updateSquareScaleToCustomSize(option: .large)
                    if let square = renderer.currentSquare {
                        square.updateTransformMatrix(preserveScale: true)
                    }

                    menuBar.enableSecondPageButtons()
                    autoSelectAddedElement()
                    // 色ボタンの状態を更新
                    menuBar.updateColorButtonState(forMatType: matType, squareType: 0, isSelected: true)
                    menuBar.updateLogoButtonState(forMatType: matType)
                    menuBar.updateOnePointButtonState(forMatType: matType)
                    menuBar.updateTextButtonState(forMatType: matType, matInfo: matInfo)
                    menuBar.updateTextPositionButtonState(forMatType: matType, isSelected: false, isDisabled: true)
                    menuBar.updateTransformButtonState(isSelected: true, squareType: 0)
                    menuBar.updateForwordBackButtonState(isSelected: true, squareType: 0)
                } else {

                    if !isReplacingExistingSquare {
                        let image = UIImage(contentsOfFile: imageName)
                        renderer.updateSquareScaleToCustomSize(option: .medium)
                    }
                }
                clearAllControlElements()

            }
        }

        controller.dismiss(animated: true)
    }

    private func clearAdditionalElements() {
        let elementsToRemove = renderer.squares.filter { square in
            return square.squareType == 1 || square.squareType == 2 || square.squareType == 3
        }

        for element in elementsToRemove {
            if let index = renderer.squares.firstIndex(where: { $0 === element }) {
                renderer.squares.remove(at: index)
            }
        }

        metalView.setNeedsDisplay()
    }
}

extension ViewController: MenuBarDelegate {
    func menuBarDidSelectTestTextPosition() {
        guard let square = renderer.currentSquare, square.squareType == 3 else {
            // テキストオブジェクトが選択されている場合のみ位置調整インターフェイスを表示する
            let alert = UIAlertController(
                title: "インフォメーション",
                message: "テキストオブジェクトを選択してください", // 選択してください
                preferredStyle: .alert
            )
            alert.addAction(UIAlertAction(title: "確認", style: .default))
            present(alert, animated: true)
            return
        }

        // 位置調整インターフェイスを作成
        let adjustView = UIView()
        adjustView.backgroundColor = UIColor.white
        adjustView.layer.cornerRadius = 10
        adjustView.layer.shadowColor = UIColor.black.cgColor
        adjustView.layer.shadowOffset = CGSize(width: 0, height: 2)
        adjustView.layer.shadowOpacity = 0.3
        adjustView.layer.shadowRadius = 4

        // インターフェイスのサイズを設定
        let viewWidth: CGFloat = 300
        let viewHeight: CGFloat = 200
        adjustView.frame = CGRect(x: 0, y: 0, width: viewWidth, height: viewHeight)

        // タイトルを追加
        let titleLabel = UILabel()
        titleLabel.text = "テキスト位置調整"
        titleLabel.textAlignment = .center
        titleLabel.font = UIFont.boldSystemFont(ofSize: 18)
        titleLabel.frame = CGRect(x: 0, y: 10, width: viewWidth, height: 30)
        adjustView.addSubview(titleLabel)

        // 垂直位置コントロール
        let verticalLabel = UILabel()
        verticalLabel.text = "垂直位置："
        verticalLabel.textColor = .darkGray
        verticalLabel.font = UIFont.systemFont(ofSize: 16)
        verticalLabel.frame = CGRect(x: 20, y: 50, width: 80, height: 30)
        adjustView.addSubview(verticalLabel)

        // 垂直位置ボタン
        let verticalStackView = UIStackView()
        verticalStackView.axis = .horizontal
        verticalStackView.distribution = .fillEqually
        verticalStackView.spacing = 10
        verticalStackView.frame = CGRect(x: 110, y: 50, width: 170, height: 30)
        adjustView.addSubview(verticalStackView)

        for position in VerticalPosition.allCases {
            let button = UIButton(type: .system)
            button.setTitle(position.description, for: .normal)
            button.backgroundColor = .lightGray
            button.setTitleColor(.darkGray, for: .normal)
            button.layer.cornerRadius = 4
            button.tag = position.rawValue
            button.addTarget(self, action: #selector(adjustVerticalPositionTapped(_:)), for: .touchUpInside)
            verticalStackView.addArrangedSubview(button)
        }

        // 水平位置コントロール
        let horizontalLabel = UILabel()
        horizontalLabel.text = "水平位置："
        horizontalLabel.textColor = .darkGray
        horizontalLabel.font = UIFont.systemFont(ofSize: 16)
        horizontalLabel.frame = CGRect(x: 20, y: 100, width: 80, height: 30)
        adjustView.addSubview(horizontalLabel)

        // 水平位置ボタン
        let horizontalStackView = UIStackView()
        horizontalStackView.axis = .horizontal
        horizontalStackView.distribution = .fillEqually
        horizontalStackView.spacing = 10
        horizontalStackView.frame = CGRect(x: 110, y: 100, width: 170, height: 30)
        adjustView.addSubview(horizontalStackView)

        for alignment in HorizontalAlignment.allCases {
            let button = UIButton(type: .system)
            button.setTitle(alignment.description, for: .normal)
            button.backgroundColor = .lightGray
            button.setTitleColor(.darkGray, for: .normal)
            button.layer.cornerRadius = 4
            button.tag = alignment.rawValue
            button.addTarget(self, action: #selector(adjustHorizontalAlignmentTapped(_:)), for: .touchUpInside)
            horizontalStackView.addArrangedSubview(button)
        }

        // 適用ボタン
        let applyButton = UIButton(type: .system)
        applyButton.setTitle("適用", for: .normal)
        applyButton.backgroundColor = .systemBlue
        applyButton.setTitleColor(.white, for: .normal)
        applyButton.layer.cornerRadius = 5
        applyButton.frame = CGRect(x: viewWidth/2 - 50, y: 150, width: 100, height: 40)
        applyButton.addTarget(self, action: #selector(applyTextPositionAdjustment), for: .touchUpInside)
        adjustView.addSubview(applyButton)

        // 位置調整インターフェイスを表示
        let popoverViewController = UIViewController()
        popoverViewController.view = adjustView
        popoverViewController.preferredContentSize = adjustView.frame.size
        popoverViewController.modalPresentationStyle = .popover

        if let popover = popoverViewController.popoverPresentationController {
            popover.sourceView = menuBar.testTextPositionButton
            popover.sourceRect = menuBar.testTextPositionButton!.bounds
            popover.permittedArrowDirections = .any
            popover.delegate = self
        }

        present(popoverViewController, animated: true)
    }

     // 背景画像選択
    func menuBarDidSelectBackground() {
        clearAllControlElements()
        showBackgroundImagePicker()
    }

    // マットカタログ
    func menuBarDidSelectMatCatalog() {
        clearAllControlElements()
        showMatCatalog(limitedMode: false)
    }

     // 2 ページ目からマットカタログ
    func menuBarDidSelectMatCatalogFromSecondPage() {
        clearAllControlElements()
        showMatCatalog(limitedMode: true)
    }

    // 移動モード
    func menuBarDidSelectTransformMode() { // move
        currentMode = .transform

        cornerControlViews.forEach { $0.removeFromSuperview() }
        cornerControlViews.removeAll()

        addArrowImages()
        addRotePoint()
        addCenterControlPoint()
    }

    // 変形モード
    func menuBarDidSelectDeformMode() { // Deform
        currentMode = .deform

        clearTransformControlElements()
        if cornerControlViews.isEmpty {
            createCornerControlViews()
        }
        cornerControlViews.forEach { $0.isHidden = false }
    }

    // 変形コントロール要素をクリアする
    private func clearTransformControlElements() {
        if let rotePointImage = metalView.subviews.first(where: {
            ($0 as? UIImageView)?.image == UIImage(named: "RotePoint")
        }) {
            rotePointImage.removeFromSuperview()
        }

        if let centerPoint = metalView.subviews.first(where: {
            ($0 as? UIImageView)?.accessibilityIdentifier == "centerControlPoint"
        }) {
            centerPoint.removeFromSuperview()
        }

        cornerControlViews.forEach { $0.removeFromSuperview() }
        cornerControlViews.removeAll()
    }

    // 消しゴム
    func menuBarDidSelectEraser() {
        // currentMode = .eraser
        clearAllControlElements()

        // 消しゴムメニューを表示
        showEraserMenu();
    }

    // 消しゴムメニューのアクション
    private func showEraserMenu() {
        if let existingMenu = view.viewWithTag(999) {
            existingMenu.removeFromSuperview()
        }

        // メニューコンテナの作成
        let menuContainer = UIView()
        menuContainer.tag = 999
        menuContainer.backgroundColor = UIColor.white
        menuContainer.layer.cornerRadius = 8
        menuContainer.layer.shadowColor = UIColor.black.cgColor
        menuContainer.layer.shadowOffset = CGSize(width: 0, height: 2)
        menuContainer.layer.shadowOpacity = 0.3
        menuContainer.layer.shadowRadius = 4
        menuContainer.translatesAutoresizingMaskIntoConstraints = false

        // メニュー項目
        let menuItems = ["大", "小", "四角形", "多角形", "自由曲線", "復元"]
        let itemHeight: CGFloat = 44
        let menuWidth: CGFloat = 120

        menuContainer.frame = CGRect(x: 0, y: 0, width: menuWidth, height: CGFloat(menuItems.count) * itemHeight)

        for (index, title) in menuItems.enumerated() {
            let button = UIButton(type: .system)
            button.setTitle(title, for: .normal)
            button.setTitleColor(.darkGray, for: .normal)
            button.backgroundColor = .white
            button.frame = CGRect(x: 0, y: CGFloat(index) * itemHeight, width: menuWidth, height: itemHeight)
            button.tag = index
            button.addTarget(self, action: #selector(eraserMenuItemTapped(_:)), for: .touchUpInside)

            // 区切り線（最後の項目以外）
            if index < menuItems.count - 1 {
                let separator = UIView()
                separator.backgroundColor = UIColor.lightGray.withAlphaComponent(0.3)
                separator.frame = CGRect(x: 10, y: button.frame.maxY - 0.5, width: menuWidth - 20, height: 0.5)
                menuContainer.addSubview(separator)
            }

            menuContainer.addSubview(button)
        }

        view.addSubview(menuContainer)

        // メニューの位置を設定（消しゴムボタンの近く）
        if let eraserButton = menuBar.eraserButton {
            let buttonFrame = eraserButton.convert(eraserButton.bounds, to: view)
            menuContainer.frame.origin = CGPoint(
                x: buttonFrame.maxX + 10,
                y: buttonFrame.minY
            )
        } else {
            // フォールバック位置
            menuContainer.center = view.center
        }

        // メニューを表示するアニメーション
        menuContainer.alpha = 0
        UIView.animate(withDuration: 0.2) {
            menuContainer.alpha = 1
        }

        // メニュー以外をタップしたら閉じる
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dismissEraserMenu))
        tapGesture.cancelsTouchesInView = false
        view.addGestureRecognizer(tapGesture)
    }

    // 消しゴムメニュー項目
    @objc private func eraserMenuItemTapped(_ sender: UIButton) {
        // メニュー項目がタップされた時の処理
        switch sender.tag {
        case 0: // 大
            currentMode = .eraser
            renderer.setEraserSize(size: .large)
            menuBar.updateEraserButtonText("消しゴム >\n 大")
            addEraserGestureRecognizer()
        case 1: // 小
            currentMode = .eraser
            renderer.setEraserSize(size: .small)
            menuBar.updateEraserButtonText("消しゴム >\n 小")
            addEraserGestureRecognizer()
        case 2: // 四角形
            currentMode = .rectangle
            addRectangleSelectionGesture()
            menuBar.updateEraserButtonText("消しゴム >\n 四角形")
            // renderer.setEraserMode(mode: .rectangle)
        case 3: // 多角形
            currentMode = .polygon
            addPolygonSelectionGesture()
            menuBar.updateEraserButtonText("消しゴム >\n 多角形")
        case 4: // 自由曲線
            currentMode = .freeform
            addFreeformSelectionGesture()
            menuBar.updateEraserButtonText("消しゴム >\n 自由曲線")
        case 5: // 復元
            currentMode = .restore
            menuBar.updateEraserButtonText("消しゴム >\n 復元")
            addEraserGestureRecognizer()
//            renderer.restoreErased()
        default:
            break
        }

        // メニューを閉じる
        dismissEraserMenu()
    }

    // 消しゴムメニューを閉じる
    @objc private func dismissEraserMenu() {
        // タップジェスチャーを削除
        view.gestureRecognizers?.forEach { gesture in
            if let tapGesture = gesture as? UITapGestureRecognizer,
               gesture.view == view {
                view.removeGestureRecognizer(tapGesture)
            }
        }

        // メニューを閉じるアニメーション
        if let menuContainer = view.viewWithTag(999) {
            UIView.animate(withDuration: 0.2, animations: {
                menuContainer.alpha = 0
            }) { _ in
                menuContainer.removeFromSuperview()
            }
        }
    }

    @objc private func handleEraserGesture(_ gesture: UIPanGestureRecognizer) {
        guard currentMode == .eraser || currentMode == .restore else { return }

        let location = gesture.location(in: metalView)

        switch gesture.state {
        case .began:
            renderer.resetErasePosition()
            renderer.resetRestorePosition()
            if currentMode == .eraser {
                renderer.eraseAtLocation(location)
            } else if currentMode == .restore {
                showRestoreIndicator(at: location)
                renderer.restoreAtLocation(location)
                UIImpactFeedbackGenerator(style: .medium).impactOccurred()
            }
        case .changed:
            if currentMode == .eraser {
                renderer.eraseAtLocation(location)
            } else if currentMode == .restore {
                renderer.restoreAtLocation(location)
                updateRestoreIndicator(at: location)
            }
            metalView.setNeedsDisplay()
        case .ended, .cancelled:
            renderer.resetErasePosition()
            renderer.resetRestorePosition()
            restoreIndicatorView?.removeFromSuperview()
            restoreIndicatorView = nil
            UIImpactFeedbackGenerator(style: .heavy).impactOccurred()
        default:
            break
        }
    }

    private func showRestoreIndicator(at location: CGPoint) {
        restoreIndicatorView?.removeFromSuperview()

        let size: CGFloat = 80.0
        let indicatorView = UIView(frame: CGRect(
            x: location.x - size/2,
            y: location.y - size/2,
            width: size,
            height: size
        ))

        indicatorView.layer.cornerRadius = size/2
        indicatorView.layer.borderColor = UIColor.green.cgColor
        indicatorView.layer.borderWidth = 2.0
        indicatorView.backgroundColor = UIColor.green.withAlphaComponent(0.2)

        metalView.addSubview(indicatorView)
        restoreIndicatorView = indicatorView
    }

    private func addRectangleSelectionGesture() {
        metalView.gestureRecognizers?.forEach { gesture in
            if let tapGesture = gesture as? UITapGestureRecognizer {
                metalView.removeGestureRecognizer(tapGesture)
            }
        }
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleRectangleSelectionTap(_:)))
        metalView.addGestureRecognizer(tapGesture)
    }

    @objc private func handleRectangleSelectionTap(_ gesture: UITapGestureRecognizer) {
        guard currentMode == .rectangle else { return }
        let location = gesture.location(in: metalView)

        if let selectionView = rectangleSelectionView, let selectionLayer = rectangleSelectionLayer {
            let path = selectionLayer.path!
            let isInside = path.contains(location)
            if let square = renderer.currentSquare, let texture = square.texture {
                UIImpactFeedbackGenerator(style: .medium).impactOccurred()
                let alertTitle = isInside ? "内部消去" : "外部消去"
                let alertMessage = isInside ? "選択した範囲の内部を消去します。\nよろしいですか？" : "選択した範囲の外部を消去します。\nよろしいですか？"
                let alert = UIAlertController(
                    title: alertTitle,
                    message: alertMessage,
                    preferredStyle: .alert
                )

                alert.addAction(UIAlertAction(title: "キャンセル", style: .cancel))
                alert.addAction(UIAlertAction(title: "OK", style: .destructive) { [weak self] _ in
                    self?.eraseRectangleArea(isInside: isInside, rectangle: selectionLayer.path!, square: square, texture: texture)
                    self?.clearRectangleSelection()
                })
                self.present(alert, animated: true)
                return
            }
        }

        if firstRectPoint == nil {
            firstRectPoint = location
            showFirstPointMarker(at: location)
        } else if let firstPoint = firstRectPoint {
            createRectangleSelection(from: firstPoint, to: location)
            firstRectPoint = nil
            rectangleFirstPointMarker?.removeFromSuperview()
            rectangleFirstPointMarker = nil
        }
    }

    private func showFirstPointMarker(at location: CGPoint) {
        rectangleFirstPointMarker?.removeFromSuperview()

        let marker = UIImageView(image: UIImage(named: "cerclePoint"))
        marker.frame = CGRect(x: 0, y: 0, width: 40, height: 40)
        marker.center = location
        metalView.addSubview(marker)

        rectangleFirstPointMarker = marker
    }

    private func createRectangleSelection(from: CGPoint, to: CGPoint) {
        clearRectangleSelection()

        let minX = min(from.x, to.x)
        let minY = min(from.y, to.y)
        let width = abs(to.x - from.x)
        let height = abs(to.y - from.y)

        let rect = CGRect(x: minX, y: minY, width: width, height: height)

        let selectionView = UIView(frame: metalView.bounds)
        selectionView.backgroundColor = UIColor.clear

        let selectionLayer = CAShapeLayer()
        selectionLayer.fillColor = UIColor.white.withAlphaComponent(0.3).cgColor
        selectionLayer.strokeColor = UIColor.red.cgColor
        selectionLayer.lineWidth = 2.0
        selectionLayer.lineDashPattern = [4, 4]

        let path = UIBezierPath(rect: rect)
        selectionLayer.path = path.cgPath

        selectionView.layer.addSublayer(selectionLayer)
        metalView.addSubview(selectionView)

        rectangleSelectionView = selectionView
        rectangleSelectionLayer = selectionLayer
    }

    private func clearRectangleSelection() {
        rectangleSelectionView?.removeFromSuperview()
        rectangleSelectionView = nil
        rectangleSelectionLayer = nil
        firstRectPoint = nil
        rectangleFirstPointMarker?.removeFromSuperview()
        rectangleFirstPointMarker = nil
    }

    private func eraseRectangleArea(isInside: Bool, rectangle: CGPath, square: Square, texture: MTLTexture) {
        let boundingBox = rectangle.boundingBox
        let region = MTLRegionMake2D(0, 0, texture.width, texture.height)
        var pixels = [UInt8](repeating: 0, count: texture.width * texture.height * 4)
        texture.getBytes(&pixels,
                        bytesPerRow: texture.width * 4,
                        from: region,
                        mipmapLevel: 0)

        let centerPoint = CGPoint(x: boundingBox.midX, y: boundingBox.midY)
        renderer.saveErasedPixels(square: square, region: region, pixels: pixels, position: centerPoint)

        let corners = square.computeCornerPoints(
            modelMatrix: square.transform.matrix,
            viewSize: metalView.bounds.size
        )

        let minX = corners.map { $0.x }.min() ?? 0
        let minY = corners.map { $0.y }.min() ?? 0
        let maxX = corners.map { $0.x }.max() ?? metalView.bounds.width
        let maxY = corners.map { $0.y }.max() ?? metalView.bounds.height

        let imageRect = CGRect(x: minX, y: minY, width: maxX - minX, height: maxY - minY)

        let selectionPath = UIBezierPath(cgPath: rectangle)

        for y in 0..<texture.height {
            for x in 0..<texture.width {
                let viewX = minX + (CGFloat(x) / CGFloat(texture.width)) * imageRect.width
                let viewY = minY + (CGFloat(y) / CGFloat(texture.height)) * imageRect.height

                let viewPoint = CGPoint(x: viewX, y: viewY)
                let pointInSelection = selectionPath.contains(viewPoint)

                if (pointInSelection && isInside) || (!pointInSelection && !isInside) {
                    let pixelIndex = (y * texture.width + x) * 4
                    pixels[pixelIndex + 3] = 0
                }
            }
        }

        texture.replace(region: region, mipmapLevel: 0, withBytes: pixels, bytesPerRow: texture.width * 4)
        metalView.setNeedsDisplay()

        let feedbackLabel = UILabel()
        feedbackLabel.text = isInside ? "領域の内容が削除された" : "領域外の内容が削除された"
        feedbackLabel.textColor = .white
        feedbackLabel.backgroundColor = UIColor.black.withAlphaComponent(0.7)
        feedbackLabel.textAlignment = .center
        feedbackLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        feedbackLabel.layer.cornerRadius = 8
        feedbackLabel.layer.masksToBounds = true
        feedbackLabel.sizeToFit()

        let padding: CGFloat = 16
        feedbackLabel.frame = CGRect(
            x: (metalView.bounds.width - feedbackLabel.frame.width - padding) / 2,
            y: (metalView.bounds.height - feedbackLabel.frame.height - padding) / 2,
            width: feedbackLabel.frame.width + padding,
            height: feedbackLabel.frame.height + padding
        )

        metalView.addSubview(feedbackLabel)

        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            UIView.animate(withDuration: 0.3, animations: {
                feedbackLabel.alpha = 0
            }) { _ in
                feedbackLabel.removeFromSuperview()
            }
        }
    }

    private func updateRestoreIndicator(at location: CGPoint) {
        guard let indicatorView = restoreIndicatorView else { return }

        let size = indicatorView.frame.width
        indicatorView.frame = CGRect(
            x: location.x - size/2,
            y: location.y - size/2,
            width: size,
            height: size
        )
    }

    private func addPolygonSelectionGesture() {
        metalView.gestureRecognizers?.forEach { gesture in
            if let tapGesture = gesture as? UITapGestureRecognizer {
                metalView.removeGestureRecognizer(tapGesture)
            }
        }
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handlePolygonSelectionTap(_:)))
        metalView.addGestureRecognizer(tapGesture)

        polygonPoints.removeAll()
        polygonSelectionView?.removeFromSuperview()
        polygonSelectionView = nil
        polygonSelectionLayer = nil
    }

    @objc private func handlePolygonSelectionTap(_ gesture: UITapGestureRecognizer) {
        guard currentMode == .polygon else { return }
        let location = gesture.location(in: metalView)

        // 多角形が完成していて、選択レイヤーが存在する場合
        if let selectionView = polygonSelectionView, let selectionLayer = polygonSelectionLayer, polygonPoints.count > 2 {
            // パスが閉じられている場合（多角形が完成している場合）
            if selectionLayer.fillColor != nil && selectionLayer.fillColor != UIColor.clear.cgColor {
                let path = selectionLayer.path!
                let isInside = UIBezierPath(cgPath: path).contains(location)

                if let square = renderer.currentSquare, let texture = square.texture {
                    UIImpactFeedbackGenerator(style: .medium).impactOccurred()

                    let alertTitle = isInside ? "内部消去" : "外部消去"
                    let alertMessage = isInside ? "選択した範囲の内部を消去します。\nよろしいですか？" : "選択した範囲の外部を消去します。\nよろしいですか？"
                    let alert = UIAlertController(
                        title: alertTitle,
                        message: alertMessage,
                        preferredStyle: .alert
                    )

                    alert.addAction(UIAlertAction(title: "キャンセル", style: .cancel))
                    alert.addAction(UIAlertAction(title: "OK", style: .destructive) { [weak self] _ in
                        self?.erasePolygonArea(isInside: isInside)
                    })

                    self.present(alert, animated: true)
                    return
                }
            }
        }

        // 多角形作成中の処理
        if polygonPoints.count > 2 {
            let firstPoint = polygonPoints.first!
            let distance = hypot(location.x - firstPoint.x, location.y - firstPoint.y)
            if distance < 30 {
                // 多角形を閉じる
                completePolygonSelection()
                return
            }
        }

        polygonPoints.append(location)

        if polygonPoints.count == 1 {
            showFirstPointMarker(at: location)
        }

        updatePolygonPath()
    }

    private func updatePolygonPath() {
        if polygonPoints.count < 2 {
            return
        }

        if polygonSelectionView == nil {
            let selectionView = UIView(frame: metalView.bounds)
            selectionView.backgroundColor = UIColor.clear

            let selectionLayer = CAShapeLayer()
            selectionLayer.fillColor = UIColor.clear.cgColor
            selectionLayer.strokeColor = UIColor.red.cgColor
            selectionLayer.lineWidth = 2.0
            selectionLayer.lineDashPattern = [4, 4]

            selectionView.layer.addSublayer(selectionLayer)
            metalView.addSubview(selectionView)

            polygonSelectionView = selectionView
            polygonSelectionLayer = selectionLayer
        }

        let path = UIBezierPath()
        path.move(to: polygonPoints[0])

        for i in 1..<polygonPoints.count {
            path.addLine(to: polygonPoints[i])
        }

        polygonSelectionLayer?.path = path.cgPath
    }

    private func completePolygonSelection() {
        guard let selectionLayer = polygonSelectionLayer else { return }

        let path = UIBezierPath()
        path.move(to: polygonPoints[0])

        for i in 1..<polygonPoints.count {
            path.addLine(to: polygonPoints[i])
        }

        path.close()
        selectionLayer.path = path.cgPath
        selectionLayer.fillColor = UIColor.white.withAlphaComponent(0.3).cgColor

        // 確認ダイアログは表示せず、次のタップを待つ
    }

    private func erasePolygonArea(isInside: Bool) {
        guard let square = renderer.currentSquare,
              let texture = square.texture,
              let selectionLayer = polygonSelectionLayer else {
            clearPolygonSelection()
            return
        }

        let region = MTLRegionMake2D(0, 0, texture.width, texture.height)
        var pixels = [UInt8](repeating: 0, count: texture.width * texture.height * 4)
        texture.getBytes(&pixels,
                        bytesPerRow: texture.width * 4,
                        from: region,
                        mipmapLevel: 0)

        var centerX: CGFloat = 0
        var centerY: CGFloat = 0
        for point in polygonPoints {
            centerX += point.x
            centerY += point.y
        }
        centerX /= CGFloat(polygonPoints.count)
        centerY /= CGFloat(polygonPoints.count)
        let centerPoint = CGPoint(x: centerX, y: centerY)

        renderer.saveErasedPixels(square: square, region: region, pixels: pixels, position: centerPoint)

        let corners = square.computeCornerPoints(
            modelMatrix: square.transform.matrix,
            viewSize: metalView.bounds.size
        )

        let minX = corners.map { $0.x }.min() ?? 0
        let minY = corners.map { $0.y }.min() ?? 0
        let maxX = corners.map { $0.x }.max() ?? metalView.bounds.width
        let maxY = corners.map { $0.y }.max() ?? metalView.bounds.height

        let imageRect = CGRect(x: minX, y: minY, width: maxX - minX, height: maxY - minY)

        let selectionPath = UIBezierPath(cgPath: selectionLayer.path!)

        for y in 0..<texture.height {
            for x in 0..<texture.width {
                let viewX = minX + (CGFloat(x) / CGFloat(texture.width)) * imageRect.width
                let viewY = minY + (CGFloat(y) / CGFloat(texture.height)) * imageRect.height

                let viewPoint = CGPoint(x: viewX, y: viewY)
                let pointInSelection = selectionPath.contains(viewPoint)

                if (pointInSelection && isInside) || (!pointInSelection && !isInside) {
                    let pixelIndex = (y * texture.width + x) * 4
                    pixels[pixelIndex + 3] = 0
                }
            }
        }

        texture.replace(region: region, mipmapLevel: 0, withBytes: pixels, bytesPerRow: texture.width * 4)
        metalView.setNeedsDisplay()

        showEraseFeedback(isInside: isInside)
        clearPolygonSelection()
    }

    private func clearPolygonSelection() {
        polygonPoints.removeAll()
        polygonSelectionView?.removeFromSuperview()
        polygonSelectionView = nil
        polygonSelectionLayer = nil
        rectangleFirstPointMarker?.removeFromSuperview()
        rectangleFirstPointMarker = nil
    }

    private func showEraseFeedback(isInside: Bool) {
        let feedbackLabel = UILabel()
        feedbackLabel.text = isInside ? "領域の内容が削除された" : "領域外の内容が削除された"
        feedbackLabel.textColor = .white
        feedbackLabel.backgroundColor = UIColor.black.withAlphaComponent(0.7)
        feedbackLabel.textAlignment = .center
        feedbackLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        feedbackLabel.layer.cornerRadius = 8
        feedbackLabel.layer.masksToBounds = true
        feedbackLabel.sizeToFit()

        let padding: CGFloat = 16
        feedbackLabel.frame = CGRect(
            x: (metalView.bounds.width - feedbackLabel.frame.width - padding) / 2,
            y: (metalView.bounds.height - feedbackLabel.frame.height - padding) / 2,
            width: feedbackLabel.frame.width + padding,
            height: feedbackLabel.frame.height + padding
        )

        metalView.addSubview(feedbackLabel)

        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            UIView.animate(withDuration: 0.3, animations: {
                feedbackLabel.alpha = 0
            }) { _ in
                feedbackLabel.removeFromSuperview()
            }
        }
    }

    private func addFreeformSelectionGesture() {
        metalView.gestureRecognizers?.forEach { gesture in
            if gesture is UIPanGestureRecognizer || gesture is UITapGestureRecognizer {
                metalView.removeGestureRecognizer(gesture)
            }
        }

        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handleFreeformSelectionPan(_:)))
        metalView.addGestureRecognizer(panGesture)

        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleFreeformSelectionTap(_:)))
        metalView.addGestureRecognizer(tapGesture)

        freeformPoints.removeAll()
        freeformSelectionView?.removeFromSuperview()
        freeformSelectionView = nil
        freeformSelectionLayer = nil
        isDrawingFreeform = false
    }

    @objc private func handleFreeformSelectionPan(_ gesture: UIPanGestureRecognizer) {
        guard currentMode == .freeform else { return }

        let location = gesture.location(in: metalView)

        switch gesture.state {
        case .began:
            freeformPoints.removeAll()
            freeformPoints.append(location)
            isDrawingFreeform = true

            if freeformSelectionView == nil {
                let selectionView = UIView(frame: metalView.bounds)
                selectionView.backgroundColor = UIColor.clear

                let selectionLayer = CAShapeLayer()
                selectionLayer.fillColor = UIColor.clear.cgColor
                selectionLayer.strokeColor = UIColor.red.cgColor
                selectionLayer.lineWidth = 2.0

                selectionView.layer.addSublayer(selectionLayer)
                metalView.addSubview(selectionView)

                freeformSelectionView = selectionView
                freeformSelectionLayer = selectionLayer
            }

            showFirstPointMarker(at: location)

        case .changed:
            freeformPoints.append(location)
            updateFreeformPath()

            if freeformPoints.count > 10 {
                let firstPoint = freeformPoints.first!
                let distance = hypot(location.x - firstPoint.x, location.y - firstPoint.y)
                if distance < 30 {
                    completeFreeformSelection()
                    gesture.state = .ended
                    return
                }
            }
        case .ended, .cancelled:
            if isDrawingFreeform && freeformPoints.count > 2 {
                freeformPoints.append(freeformPoints.first!)
                updateFreeformPath()
                completeFreeformSelection()
            } else {
                clearFreeformSelection()
            }

        default:
            break
        }
    }

    private func updateFreeformPath() {
        guard let selectionLayer = freeformSelectionLayer else { return }

        let path = UIBezierPath()
        path.move(to: freeformPoints[0])

        for i in 1..<freeformPoints.count {
            path.addLine(to: freeformPoints[i])
        }

        selectionLayer.path = path.cgPath
    }

    private func completeFreeformSelection() {
        guard let selectionLayer = freeformSelectionLayer else { return }

        let path = UIBezierPath()
        path.move(to: freeformPoints[0])
        for i in 1..<freeformPoints.count {
            path.addLine(to: freeformPoints[i])
        }
        path.close()
        selectionLayer.path = path.cgPath
        selectionLayer.fillColor = UIColor.white.withAlphaComponent(0.3).cgColor
        isDrawingFreeform = false
    }

     @objc private func handleFreeformSelectionTap(_ gesture: UITapGestureRecognizer) {
        guard currentMode == .freeform else { return }

        let location = gesture.location(in: metalView)

        if let selectionView = freeformSelectionView, let selectionLayer = freeformSelectionLayer,
           !isDrawingFreeform && selectionLayer.fillColor != nil && selectionLayer.fillColor != UIColor.clear.cgColor {

            let path = selectionLayer.path!
            let isInside = UIBezierPath(cgPath: path).contains(location)

            if let square = renderer.currentSquare, let texture = square.texture {
                UIImpactFeedbackGenerator(style: .medium).impactOccurred()

                let alertTitle = isInside ? "内部消去" : "外部消去"
                let alertMessage = isInside ? "選択した範囲の内部を消去します。\nよろしいですか？" : "選択した範囲の外部を消去します。\nよろしいですか？"
                let alert = UIAlertController(
                    title: alertTitle,
                    message: alertMessage,
                    preferredStyle: .alert
                )

                alert.addAction(UIAlertAction(title: "キャンセル", style: .cancel))
                alert.addAction(UIAlertAction(title: "OK", style: .destructive) { [weak self] _ in
                    self?.eraseFreeformArea(isInside: isInside)
                })

                self.present(alert, animated: true)
            }
        }
    }

    private func eraseFreeformArea(isInside: Bool) {
        guard let square = renderer.currentSquare,
              let texture = square.texture,
              let selectionLayer = freeformSelectionLayer else {
            clearFreeformSelection()
            return
        }

        let region = MTLRegionMake2D(0, 0, texture.width, texture.height)
        var pixels = [UInt8](repeating: 0, count: texture.width * texture.height * 4)
        texture.getBytes(&pixels,
                        bytesPerRow: texture.width * 4,
                        from: region,
                        mipmapLevel: 0)

        var centerX: CGFloat = 0
        var centerY: CGFloat = 0
        for point in freeformPoints {
            centerX += point.x
            centerY += point.y
        }
        centerX /= CGFloat(freeformPoints.count)
        centerY /= CGFloat(freeformPoints.count)
        let centerPoint = CGPoint(x: centerX, y: centerY)

        renderer.saveErasedPixels(square: square, region: region, pixels: pixels, position: centerPoint)

        let corners = square.computeCornerPoints(
            modelMatrix: square.transform.matrix,
            viewSize: metalView.bounds.size
        )

        let minX = corners.map { $0.x }.min() ?? 0
        let minY = corners.map { $0.y }.min() ?? 0
        let maxX = corners.map { $0.x }.max() ?? metalView.bounds.width
        let maxY = corners.map { $0.y }.max() ?? metalView.bounds.height

        let imageRect = CGRect(x: minX, y: minY, width: maxX - minX, height: maxY - minY)

        let selectionPath = UIBezierPath(cgPath: selectionLayer.path!)

        for y in 0..<texture.height {
            for x in 0..<texture.width {
                let viewX = minX + (CGFloat(x) / CGFloat(texture.width)) * imageRect.width
                let viewY = minY + (CGFloat(y) / CGFloat(texture.height)) * imageRect.height

                let viewPoint = CGPoint(x: viewX, y: viewY)
                let pointInSelection = selectionPath.contains(viewPoint)

                if (pointInSelection && isInside) || (!pointInSelection && !isInside) {
                    let pixelIndex = (y * texture.width + x) * 4
                    pixels[pixelIndex + 3] = 0
                }
            }
        }

        texture.replace(region: region, mipmapLevel: 0, withBytes: pixels, bytesPerRow: texture.width * 4)
        metalView.setNeedsDisplay()
        showEraseFeedback(isInside: isInside)
        clearFreeformSelection()
    }

    private func clearFreeformSelection() {
        freeformPoints.removeAll()
        freeformSelectionView?.removeFromSuperview()
        freeformSelectionView = nil
        freeformSelectionLayer = nil
        isDrawingFreeform = false
        rectangleFirstPointMarker?.removeFromSuperview()
        rectangleFirstPointMarker = nil
    }

    // 元に戻すボタン
    func menuBarDidSelectUndo() {
        let alert = UIAlertController(
            title: "確認",
            message: "マット配置を初期に戻します。\nよろしいですか？",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "キャンセル", style: .cancel))
        alert.addAction(UIAlertAction(title: "OK", style: .destructive) { [weak self] _ in
            // OK が押された場合の処理
            self?.clearAllControlElements()
            self?.clearRectangleSelection()
            self?.clearPolygonSelection()
            self?.clearFreeformSelection()

            self?.selectionBoxView?.removeFromSuperview()
            self?.selectionBoxView = nil

            if let square = self?.renderer.currentSquare {
                square.position = SIMD2<Float>(0, 0)
                square.rotation = 0
                let scaleX = Float(1.0)
                let scaleY = scaleX / square.originalAspectRatio
                square.scale = SIMD2<Float>(scaleX, scaleY)
                square.cornerDisplacements = [SIMD2<Float>(0, 0), SIMD2<Float>(0, 0), SIMD2<Float>(0, 0), SIMD2<Float>(0, 0)]
                square.updateTransformMatrix()
            }

            self?.renderer.updateSquareScaleToCustomSize(option: .medium)
            self?.renderer.restoreAllForCurrentSquare()
            self?.metalView.setNeedsDisplay()
        })

        present(alert, animated: true)
    }

    // 保存ボタン
    func menuBarDidSelectSave() {
        let alert = UIAlertController(
            title: "確認",
            message: "画像を保存しますか？",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "キャンセル", style: .cancel))
        alert.addAction(UIAlertAction(title: "OK", style: .default) { [weak self] _ in
            self?.loadingLabel.text = "保存中..."
            self?.loadingContainer.isHidden = false
            self?.activityIndicator.startAnimating()
            self?.saveCurrentImage()
        })

        present(alert, animated: true)
    }

    private func saveCurrentImage() {
        // 現在の画面をキャプチャして画像として保存
        UIGraphicsBeginImageContextWithOptions(metalView.bounds.size, false, UIScreen.main.scale)

        // 背景画像を描画
        if let backgroundImage = backgroundImageView.image {
            backgroundImage.draw(in: metalView.bounds)
        } else {
            // 背景がない場合は白色で塗りつぶす
            UIColor.white.setFill()
            UIRectFill(metalView.bounds)
        }

        // MetalView の内容を描画
        metalView.drawHierarchy(in: metalView.bounds, afterScreenUpdates: true)

        if let compositeImage = UIGraphicsGetImageFromCurrentImageContext() {
            UIGraphicsEndImageContext()

            // isEditMode に基づいてフォルダとファイル名を決定
            let isEditMode = menuBar.isEditMode
            let folderName = isEditMode ? "EditedMatDesigns" : "CompositeImages"
            let filePrefix = isEditMode ? "edited_mat_" : "composite_"

            // ファイル名を生成（現在の日時を使用）
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyyMMdd_HHmmss"
            let fileName = "\(filePrefix)\(dateFormatter.string(from: Date())).jpg"

            // 保存先フォルダのパスを取得
            guard let documentsURL = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
                self.showSaveError("ドキュメントフォルダにアクセスできません")
                return
            }

            let saveDirectoryURL = documentsURL.appendingPathComponent(folderName)

            // フォルダが存在しない場合は作成
            if !FileManager.default.fileExists(atPath: saveDirectoryURL.path) {
                do {
                    try FileManager.default.createDirectory(at: saveDirectoryURL, withIntermediateDirectories: true)
                } catch {
                    self.showSaveError("フォルダの作成に失敗しました：\(error.localizedDescription)")
                    return
                }
            }

            // 画像を保存
            let fileURL =  saveDirectoryURL.appendingPathComponent(fileName)
            if let imageData = compositeImage.jpegData(compressionQuality: 0.9) {
                do {
                    try imageData.write(to: fileURL)

                    DispatchQueue.main.async {
                        self.loadingContainer.isHidden = true
                        self.activityIndicator.stopAnimating()

                        let successAlert = UIAlertController(
                            title: "保存完了",
                            message: "画像が正常に保存されました",
                            preferredStyle: .alert
                        )
                        successAlert.addAction(UIAlertAction(title: "OK", style: .default))
                        self.present(successAlert, animated: true)
                    }
                } catch {
                    self.showSaveError("画像の保存に失敗しました：\(error.localizedDescription)")
                }
            } else {
                self.showSaveError("画像データの作成に失敗しました")
            }
        } else {
            UIGraphicsEndImageContext()
            self.showSaveError("画像のキャプチャに失敗しました")
        }
    }

    private func showSaveError(_ message: String) {
        DispatchQueue.main.async {
            self.loadingContainer.isHidden = true
            self.activityIndicator.stopAnimating()

            let errorAlert = UIAlertController(
                title: "保存エラー",
                message: message,
                preferredStyle: .alert
            )
            errorAlert.addAction(UIAlertAction(title: "OK", style: .default))
            self.present(errorAlert, animated: true)
        }
    }

    // 画像管理
    func menuBarDidSelectFolder() {
        let photoCatalogVC = PhotoCatalogViewController()
        photoCatalogVC.delegate = self
        photoCatalogVC.modalPresentationStyle = .formSheet
        photoCatalogVC.preferredContentSize = CGSize(
            width: view.bounds.width * 0.8,
            height: view.bounds.height * 0.8
        )
        present(photoCatalogVC, animated: true)
    }

    // ダウンロード
    func menuBarDidSelectDownload() {
        startDownload()
    }

    // ロゴ選択
    func menuBarDidSelectLogoSelect() {
        let logoSelectorVC = LogoSelectorViewController()
        logoSelectorVC.delegate = self
        logoSelectorVC.modalPresentationStyle = .formSheet
        logoSelectorVC.preferredContentSize = CGSize(
            width: view.bounds.width * 0.8,
            height: view.bounds.height * 0.8
        )
        present(logoSelectorVC, animated: true)
    }

    // ワンポイント
    func menuBarDidSelectOnePoint() {
        let onePointVC = OnePointViewController()
        onePointVC.delegate = self
        onePointVC.modalPresentationStyle = .formSheet
        let isRegularMat = renderer.squares
            .filter { $0.squareType == 0 && $0.isEditMode }
            .first?.matType == "レギュラー"
        onePointVC.updateCategoryVisibility(showOnePointAndPict: !isRegularMat)
        onePointVC.preferredContentSize = CGSize(
            width: view.bounds.width * 0.8,
            height: view.bounds.height * 0.8
        )
        present(onePointVC, animated: true)
    }

    // テキスト
    func menuBarDidSelectTextInsert() {
        if !hasShownTextInsertAlertInCurrentSession {
            let alert = UIAlertController(
                title: "確認",
                message: "使用されるフォントは iPad の\n標準フォントであり、実際にマットで\n使われるフォントとは異なります。",
                preferredStyle: .alert
            )
            alert.addAction(UIAlertAction(title: "OK", style: .default) { [weak self] _ in
                self?.hasShownTextInsertAlertInCurrentSession = true
                self?.showTextInputView()
            })
            present(alert, animated: true)
        } else {
            showTextInputView()
        }
    }

    private func showTextInputView() {
        print("aqaz showTextInputView - currentSquare: \(renderer.currentSquare)")
        print("aqaz showTextInputView - matType: \(renderer.currentSquare?.matType ?? "nil")")
        print("aqaz showTextInputView - matInfo: \(renderer.currentSquare?.matInfo)")

        textPositionManager.reset()
    
        if let square = renderer.currentSquare, 
        square.matType == "家庭用マット", 
        let matInfo = square.matInfo,
        let homeMatConfig = matInfo.homeMatConfig,
        let defaultPosition = homeMatConfig.defaultPositionInfo?.labelPosition {
            
            let verticalChar = defaultPosition.prefix(1)
            let horizontalChar = defaultPosition.suffix(1)
            
            switch verticalChar {
            case "A":
                textPositionManager.verticalPosition = .top
            case "B":
                textPositionManager.verticalPosition = .middle
            case "C":
                textPositionManager.verticalPosition = .bottom
            default:
                break
            }
            
            switch horizontalChar {
            case "1":
                textPositionManager.horizontalAlignment = .left
            case "2":
                textPositionManager.horizontalAlignment = .center
            case "3":
                textPositionManager.horizontalAlignment = .right
            default:
                break
            }
            
            print("aqaz showTextInputView - : \(defaultPosition)")
        }

        let textInputView = UITextView()
        textInputView.backgroundColor = UIColor.white.withAlphaComponent(0.8)
        textInputView.textColor = UIColor.black
        textInputView.font = UIFont.systemFont(ofSize: 72)
        textInputView.layer.borderColor = UIColor.darkGray.cgColor
        textInputView.layer.borderWidth = 1.0
        textInputView.layer.cornerRadius = 5.0
        textInputView.autocorrectionType = .no
        textInputView.returnKeyType = .done

        let centerX = metalView.bounds.width / 2
        let centerY = metalView.bounds.height / 3
        let height: CGFloat = 80
        let minWidth: CGFloat = 400
        let maxWidth: CGFloat = metalView.bounds.width * 0.8

        textInputView.frame = CGRect(x: centerX - minWidth/2, y: centerY - height/2, width: minWidth, height: height)

        metalView.addSubview(textInputView)

        textInputView.delegate = self
        textInputView.becomeFirstResponder()

        let toolbar = UIToolbar(frame: CGRect(x: 0, y: 0, width: view.frame.width, height: 44))
        let flexSpace = UIBarButtonItem(barButtonSystemItem: .flexibleSpace, target: nil, action: nil)
        let doneButton = UIBarButtonItem(barButtonSystemItem: .done, target: self, action: #selector(textInputDone(_:)))
        toolbar.items = [flexSpace, doneButton]
        textInputView.inputAccessoryView = toolbar

        self.currentTextInputView = textInputView
    }

    @objc private func verticalPositionButtonTapped(_ sender: UIButton) {
        guard let position = VerticalPosition(rawValue: sender.tag) else { return }

        textPositionManager.verticalPosition = position

        // Update button appearances
        if let stackView = sender.superview as? UIStackView {
            for case let button as UIButton in stackView.arrangedSubviews {
                let isSelected = button.tag == position.rawValue
                button.backgroundColor = isSelected ? .systemBlue : .lightGray
                button.setTitleColor(isSelected ? .white : .darkGray, for: .normal)
            }
        }
    }

    @objc private func horizontalAlignmentButtonTapped(_ sender: UIButton) {
        guard let alignment = HorizontalAlignment(rawValue: sender.tag) else { return }

        textPositionManager.horizontalAlignment = alignment

        // Update button appearances
        if let stackView = sender.superview as? UIStackView {
            for case let button as UIButton in stackView.arrangedSubviews {
                let isSelected = button.tag == alignment.rawValue
                button.backgroundColor = isSelected ? .systemBlue : .lightGray
                button.setTitleColor(isSelected ? .white : .darkGray, for: .normal)
            }
        }
    }

    @objc private func textInputDone(_ sender: UIBarButtonItem) {
        guard let textView = currentTextInputView, let text = textView.text, !text.isEmpty else {
            dismissTextInputView()
            return
        }
        
        // 1. Retrieve character limit configuration (if exists) ---
        var limits: MatInfo.CharacterLimits? = nil
        var isHomeMatWithLimits = false 
        if let currentMat = renderer.currentSquare, 
           currentMat.matType == "家庭用マット",  // 家庭用のマットの場合
           let info = currentMat.matInfo,
           let config = info.homeMatConfig {
            limits = config.textConfig.maxCounts
            isHomeMatWithLimits = limits != nil // 制限が設定されている場合のみ
            print("🏠 Home Mat :\(limits)")
        } else if let mat = renderer.squares.first, mat.matType == "家庭用マット", let info = mat.matInfo, let config = info.homeMatConfig {
             // If the current selection isn't a mat, try to get from the base mat (more robust)
             
            limits = config.textConfig.maxCounts
            isHomeMatWithLimits = limits != nil
            print("🏠 ベースマットから取得：\(limits)")
        }

        // --- 2. 家庭用マットで制限がある場合、チェックを実行 ---
        if isHomeMatWithLimits, let characterLimits = limits {
            // 2.a テキスト内容を分析して、含まれる文字の種類を決定する
            var containsKanji = false
            var containsHiragana = false
            var containsKatakana = false
            var containsLowercase = false
            var containsUppercase = false
            // 数字や記号なども考慮する必要があるかもしれません。あなたの CharacterLimits の定義に従ってください。

            for scalar in text.unicodeScalars {
                // 以下はサンプル判定であり、Unicode 範囲などに応じて精密な調整が必要な場合があります
                if scalar.properties.isIdeographic { // 表意文字かどうかを確認する（漢字を含む可能性があります）
                    containsKanji = true
                } else if scalar.value >= 0x3040 && scalar.value <= 0x309F { // 平假名範囲
                    containsHiragana = true
                } else if scalar.value >= 0x30A0 && scalar.value <= 0x30FF { // 片假名範囲
                    // ただし、片假名範囲は片假名を含む可能性があるため、片假名範囲を片假名範囲と判断する必要があります
                    containsKatakana = true
                } else if scalar.properties.isLowercase { // 小文字
                    containsLowercase = true
                } else if scalar.properties.isUppercase { // 大文字
                    containsUppercase = true
                }
            }

            print("🔍 テキストに含まれるタイプ：漢字=\(containsKanji), 平仮名=\(containsHiragana), 片仮名=\(containsKatakana), 小文字=\(containsLowercase), 大文字=\(containsUppercase)")

            // 2.b 実際に含まれる型に対応する制約の最小値を見つける
            var applicableLimits: [Int] = []
            if containsKanji { applicableLimits.append(characterLimits.kanji) }
            if containsHiragana { applicableLimits.append(characterLimits.hiragana) }
            if containsKatakana { applicableLimits.append(characterLimits.katakana) }
            if containsLowercase { applicableLimits.append(characterLimits.lowercase) }
            if containsUppercase { applicableLimits.append(characterLimits.uppercase) }
            // 他の定義したタイプを追加してください

            // 制限されたタイプが含まれていない場合は、デフォルトの最大長を設定するか制限しないでください
            let finalLengthLimit = applicableLimits.min() ?? Int.max // リストが空の場合は制限しない

            print("📊 最終的に有効な長さ制限：\(finalLengthLimit)")

            // 2.c 総長が最終制限を超えていないかチェック
            // 注意：ここでの長さは文字数（text.count）であり、バイト数ではありません
            if text.count > finalLengthLimit {
                // 超過制限、ユーザーに通知して操作を停止
                let alert = UIAlertController(
                    title: "文字数超過",
                    message: "入力された文字数が制限\(finalLengthLimit) 文字を超えています。",
                    preferredStyle: .alert
                )
                alert.addAction(UIAlertAction(title: "OK", style: .default) { [weak self] _ in
                     // 入力欄を維持して、ユーザーに修正を許可することができます
                     // self?.currentTextInputView?.becomeFirstResponder()
                })
                present(alert, animated: true)
                // dismissTextInputView() // 一時的に閉じず、ユーザーに修正させる可能性がある
                return // テキストの追加を禁止する
            }
             print("✅ 長さチェック通過。")
        } else {
             print("ℹ️ 非家庭用マットまたは制限なし配置、長さチェックを実行しません。")
        }

        print("aqaz textInputDone renderer.currentSquare:\(renderer.currentSquare)")

        if let square = renderer.currentSquare, square.matType == "家庭用マット", let matInfo = square.matInfo,
           let homeMatConfig = matInfo.homeMatConfig, homeMatConfig.textConfig.hasText && homeMatConfig.textConfig.textDirection {

            print("aqaz textInputDone matInfo:\(matInfo)")

            // Get position info from text position manager
            let positionIdentifier = textPositionManager.positionIdentifier
            print("aqaz textInputDone using position from manager: \(positionIdentifier)")

            // Try to get position info from home mat config
            var positionInfo: HomeMatPositionInfo?

            if let configPositionInfo = homeMatConfig.getPositionInfo(for: positionIdentifier) {
                // Use position info from config if available
                positionInfo = configPositionInfo
                print("aqaz textInputDone found position info in config for \(positionIdentifier)")
            } else {
                // Create position info from text position manager
                positionInfo = textPositionManager.createPositionInfo()
                print("aqaz textInputDone created position info from manager: \(positionInfo!)")
            }

            print("aqaz textInputDone positionInfo:\(positionInfo!)")
            
            guard let matSquare = renderer.squares.first(where: { $0.squareType == 0 && !$0.isHidden }) else {
                print("エラー：ベースマップオブジェクトが見つかりません。")
                dismissTextInputView()
                return
            }

            // テキストのアライメントを設定
            let targetAlignment = textPositionManager.horizontalAlignment.textAlignment
            let targetHorizontal = textPositionManager.horizontalAlignment // 从 manager 初期アライメントの取得

            let tempLabelForCalc = Square() // 一時オブジェクト
            tempLabelForCalc.scale = SIMD2<Float>(1.0, 0.16) // 初期スケールを仮定して計算する
            guard let targetDimensions = calculateTargetDimensions(for: tempLabelForCalc, matSquare: matSquare) else {
                print("エラー：初期目標寸法を計算できません。")
                dismissTextInputView()
                return
            }
            self.dimensions = targetDimensions

            // テキスト画像を作成
            guard let textImage = createTextImageWithAlignment(text: text, alignment: targetAlignment, targetAspectRatio: CGFloat(targetDimensions.aspectRatio)),
                      let imagePath = saveTextImageTemporarily(image: textImage) else {
                    print("エラー：テキスト画像の作成または保存に失敗しました。")
                    dismissTextInputView()
                    return
                }
            let image = textImage
            if  let imagePath = saveTextImageTemporarily(image: image) {
                let success = renderer.addSquare(imageName: imagePath, squareType: 3, isEditMode: true, matInfo: matInfo)
                if success {
                    clearAllControlElements()
                    //renderer.updateSquareScaleToCustomSize(option: .tiny)
                    
                    // 最後に——絶対に最後に！——もう一度位置を計算する
                    if let mat = renderer.squares.first(where: { $0.squareType == 0 }),
                       let label = renderer.currentSquare {
//                        renderer.updateLabelPosition(for: mat,
//                                                     label: label,
//                                                     h: textPositionManager.horizontalAlignment,
//                                                     v: textPositionManager.verticalPosition)
                        if let positionName = matInfo.homeMatConfig?.defaultPositionInfo?.labelPosition {
                            let verticalChar = positionName.prefix(1)
                            let horizontalChar = positionName.suffix(1)
                            
                            // Set vertical position
                            switch verticalChar {
                            case "A":
                                textPositionManager.verticalPosition = .top
                            case "B":
                                textPositionManager.verticalPosition = .middle
                            case "C":
                                textPositionManager.verticalPosition = .bottom
                            default:
                                break
                            }
                            
                            // Set horizontal alignment
                            switch horizontalChar {
                            case "1":
                                textPositionManager.horizontalAlignment = .left
                            case "2":
                                textPositionManager.horizontalAlignment = .center
                            case "3":
                                textPositionManager.horizontalAlignment = .right
                            default:
                                break
                            }
                        }
                        
//                        renderer.updateLabelPosition_Relative(for: mat, label: label, h: textPositionManager.horizontalAlignment, v: textPositionManager.verticalPosition)
                    }

                    // text square
                    if let newSquare = renderer.currentSquare {
                        print("aqaz textInputDone textSquare created")

                        // --- ⑤ 目標に合わせて Square プロパティをオーバーライド ---
                        newSquare.textContent = text                       // テキスト内容を記録
                        newSquare.originalAspectRatio = targetDimensions.aspectRatio // 計算目標のアスペクト比を設定
                        newSquare.scale = targetDimensions.scale           // 計算目標のスケールを設定
                        newSquare.currentHorizontalAlignment = targetHorizontal // 初期アライメントを記録

                        // --- ⑥ 初期定位を行う ---
                        // 既存の関数を使用して垂直位置を調整する
                        updateLabelVerticalPositionImmediately(label: newSquare, mat: matSquare, targetV: textPositionManager.verticalPosition) // 初期垂直位置を設定

                        // updateLabelVerticalPositionImmediately 内部で updateTransformMatrix を呼び出します
                       // だからここで再度呼び出す必要はありません

                        print("✅ テキストの追加が完了し、目標のサイズと位置が適用されました。")

                        currentMode = .select
                        createSelectionBox()
                        menuBar.updateTextButtonState(forMatType: "", isDisabled: true)
//                        menuBar.updateTransformButtonState(isSelected: true, squareType: 3, isDisabled: true)
//                        menuBar.updateColorButtonState(forMatType: "", squareType: 3, isSelected: true, isDisabled: true)
//                        menuBar.updateForwordBackButtonState(isSelected: true, squareType: 3, isDisabled: true)
//                        menuBar.updateDeleteButtonState(isSelected: true, squareType: textSquare.squareType)
                        menuBar.updateTextPositionButtonState(forMatType: "家庭用マット", matInfo: matInfo, isSelected: true)
                    }
                }
            }
        } else {
            let textImage = createTextImage(text: text)

            if let image = textImage, let imagePath = saveTextImageTemporarily(image: image) {
                let success = renderer.addSquare(imageName: imagePath, squareType: 3, isEditMode: true)
                if success {
                    clearAllControlElements()
                    renderer.updateSquareScaleToCustomSize(option: .tiny)

                    if let square = renderer.currentSquare {
                        let textViewCenter = CGPoint(
                            x: textView.frame.midX,
                            y: textView.frame.midY
                        )
                        
                        square.textContent = text

                        let viewWidth = metalView.bounds.width
                        let viewHeight = metalView.bounds.height

                        let normalizedX = (textViewCenter.x / viewWidth) * 2 - 1
                        let normalizedY = -((textViewCenter.y / viewHeight) * 2 - 1)

                        let currentPos = SIMD3<Float>(square.transform.matrix.columns.3.x,
                                                     square.transform.matrix.columns.3.y,
                                                     0)

                        let translation = CGPoint(
                            x: CGFloat(Float(normalizedX) - currentPos.x),
                            y: CGFloat(Float(normalizedY) - currentPos.y)
                        )

                        renderer.updateSquarePosition(translation: translation)

                        currentMode = .select
                        createSelectionBox()
                        menuBar.updateDeleteButtonState(isSelected: true, squareType: square.squareType)
                        menuBar.updateColorButtonState(forMatType: "", squareType: 3, isSelected: true)
                        menuBar.updateTransformButtonState(isSelected: true, squareType: 3)
                        menuBar.updateForwordBackButtonState(isSelected: true, squareType: 3)
                        menuBar.updateTextPositionButtonState(forMatType: "", isSelected: true)
                    }
                }
            }
        }

        dismissTextInputView()
    }

    private func dismissTextInputView() {
        // Remove the entire container view that holds the text input and position controls
        currentTextInputView?.removeFromSuperview()
        currentTextInputView = nil
    }

    private func createTextImage(text: String) -> UIImage? {
        return createTextImageWithAlignment(text: text, alignment: .center)
    }

    // 選択ボタン
    func menuBarDidSelectSelect() {
        currentMode = .select
        clearAllControlElements()
        addTapGestureForSelection()
    }

    private func addTapGestureForSelection() {
        var removedCount = 0
        metalView.gestureRecognizers?.forEach { gesture in
            if let tapGesture = gesture as? UITapGestureRecognizer {
                metalView.removeGestureRecognizer(tapGesture)
                removedCount += 1
            }
        }
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleSelectionTap(_:)))
        tapGesture.delegate = self
        metalView.addGestureRecognizer(tapGesture)
    }

    @objc private func handleSelectionTap(_ gesture: UITapGestureRecognizer) {
        print("handleSelectionTap")
        guard currentMode == .select else { return }

        let location = gesture.location(in: metalView)
        selectionBoxView?.removeFromSuperview()
        selectionBoxView = nil

        let expandedLocation = location
        let hitTestRadius: CGFloat = 30.0

        if renderer.selectSquareAt(location: expandedLocation, hitRadius: hitTestRadius, inEditMode: menuBar.isEditMode) {
            if menuBar.isEditMode {
                createSelectionBox()
            }

            if let square = renderer.currentSquare {
                menuBar.updateDeleteButtonState(isSelected: true, squareType: square.squareType)
                menuBar.updateColorButtonState(forMatType: square.matType, squareType: square.squareType, isSelected: true)
                menuBar.updateTransformButtonState(isSelected: true, squareType: square.squareType)
                menuBar.updateForwordBackButtonState(isSelected: true, squareType: square.squareType)
                menuBar.updateTextPositionButtonState(forMatType: square.matType, isSelected: true)
            }
        } else {
            menuBar.updateDeleteButtonState(isSelected: false, squareType: nil)
            menuBar.updateColorButtonState(forMatType: "", squareType: 0, isSelected: false)
            menuBar.updateTransformButtonState(isSelected: false, squareType: 0)
            menuBar.updateForwordBackButtonState(isSelected: false, squareType: 0)
            menuBar.updateTextPositionButtonState(forMatType: "", isSelected: false)
        }
    }

    private func createSelectionBox() {
        print("createSelectionBox")
        guard let square = renderer.currentSquare else { return }

        selectionBoxView?.removeFromSuperview()

        if square.squareType == 3 && square.textContent != nil && square.matInfo == nil {
            let corners = square.computeCornerPoints(
                modelMatrix: square.transform.matrix,
                viewSize: metalView.bounds.size
            )
            
            let textWidthRatio: CGFloat = 0.8
            
            let centerX = (corners[0].x + corners[1].x + corners[2].x + corners[3].x) / 4
            let centerY = (corners[0].y + corners[1].y + corners[2].y + corners[3].y) / 4
            
            let originalWidth = max(
                hypot(corners[1].x - corners[0].x, corners[1].y - corners[0].y),
                hypot(corners[3].x - corners[2].x, corners[3].y - corners[2].y)
            )
            
            let newWidth = originalWidth * textWidthRatio
            let widthDiff = (originalWidth - newWidth) / 2
            
            let adjustedCorners: [CGPoint] = [
                CGPoint(x: corners[0].x + widthDiff * cos(currentAngle), y: corners[0].y + widthDiff * sin(currentAngle)),
                CGPoint(x: corners[1].x - widthDiff * cos(currentAngle), y: corners[1].y - widthDiff * sin(currentAngle)),
                CGPoint(x: corners[2].x + widthDiff * cos(currentAngle), y: corners[2].y + widthDiff * sin(currentAngle)),
                CGPoint(x: corners[3].x - widthDiff * cos(currentAngle), y: corners[3].y - widthDiff * sin(currentAngle))
            ]
            
            let selectionBox = CAShapeLayer()
            selectionBox.fillColor = UIColor.clear.cgColor
            selectionBox.strokeColor = UIColor.orange.cgColor
            selectionBox.lineWidth = 3.0
            
            let path = UIBezierPath()
            let orderedCorners = [adjustedCorners[0], adjustedCorners[1], adjustedCorners[3], adjustedCorners[2]]
            
            path.move(to: orderedCorners[0])
            for i in 1..<orderedCorners.count {
                path.addLine(to: orderedCorners[i])
            }
            path.close()
            
            selectionBox.path = path.cgPath
            
            let containerView = UIView(frame: metalView.bounds)
            containerView.backgroundColor = UIColor.clear
            containerView.layer.addSublayer(selectionBox)
            
            metalView.addSubview(containerView)
            selectionBoxView = containerView
        } else {
            let corners = square.computeCornerPoints(
                modelMatrix: square.transform.matrix,
                viewSize: metalView.bounds.size
            )

            let selectionBox = CAShapeLayer()
            selectionBox.fillColor = UIColor.clear.cgColor
            selectionBox.strokeColor = UIColor.orange.cgColor
            selectionBox.lineWidth = 3.0

            let path = UIBezierPath()
            let orderedCorners = [corners[0], corners[1], corners[3], corners[2]]

            path.move(to: orderedCorners[0])
            for i in 1..<orderedCorners.count {
                path.addLine(to: orderedCorners[i])
            }
            path.close()

            selectionBox.path = path.cgPath

            let containerView = UIView(frame: metalView.bounds)
            containerView.backgroundColor = UIColor.clear
            containerView.layer.addSublayer(selectionBox)

            metalView.addSubview(containerView)
            selectionBoxView = containerView
        }
    }

    private func updateSelectionBoxIfNeeded() {
        if let containerView = selectionBoxView, let square = renderer.currentSquare {
            if square.squareType == 3 && square.textContent != nil && square.matInfo == nil {
                let corners = square.computeCornerPoints(
                    modelMatrix: square.transform.matrix,
                    viewSize: metalView.bounds.size
                )
                
                let textWidthRatio: CGFloat = 0.8
                
                let originalWidth = max(
                    hypot(corners[1].x - corners[0].x, corners[1].y - corners[0].y),
                    hypot(corners[3].x - corners[2].x, corners[3].y - corners[2].y)
                )
                
                let newWidth = originalWidth * textWidthRatio
                let widthDiff = (originalWidth - newWidth) / 2
                
                let adjustedCorners: [CGPoint] = [
                    CGPoint(x: corners[0].x + widthDiff * cos(currentAngle), y: corners[0].y + widthDiff * sin(currentAngle)),
                    CGPoint(x: corners[1].x - widthDiff * cos(currentAngle), y: corners[1].y - widthDiff * sin(currentAngle)),
                    CGPoint(x: corners[2].x + widthDiff * cos(currentAngle), y: corners[2].y + widthDiff * sin(currentAngle)),
                    CGPoint(x: corners[3].x - widthDiff * cos(currentAngle), y: corners[3].y - widthDiff * sin(currentAngle))
                ]
                
                if let selectionBox = containerView.layer.sublayers?.first as? CAShapeLayer {
                    let path = UIBezierPath()
                    let orderedCorners = [adjustedCorners[0], adjustedCorners[1], adjustedCorners[3], adjustedCorners[2]]
                    
                    path.move(to: orderedCorners[0])
                    for i in 1..<orderedCorners.count {
                        path.addLine(to: orderedCorners[i])
                    }
                    path.close()
                    selectionBox.path = path.cgPath
                }
            } else {
                let corners = square.computeCornerPoints(
                    modelMatrix: square.transform.matrix,
                    viewSize: metalView.bounds.size
                )

                if let selectionBox = containerView.layer.sublayers?.first as? CAShapeLayer {
                    let path = UIBezierPath()
                    let orderedCorners = [corners[0], corners[1], corners[3], corners[2]]

                    path.move(to: orderedCorners[0])
                    for i in 1..<orderedCorners.count {
                        path.addLine(to: orderedCorners[i])
                    }
                    path.close()
                    selectionBox.path = path.cgPath
                }
            }
        }
    }

    // 色選択
    func menuBarDidSelectColor() {
        guard let square = renderer.currentSquare else { return }

        let colorPickerVC = ColorPickerViewController()
        colorPickerVC.delegate = self
        colorPickerVC.modalPresentationStyle = .formSheet
        colorPickerVC.preferredContentSize = CGSize(
            width: view.bounds.width * 0.8,
            height: view.bounds.height * 0.8
        )

        let isRegularMat = renderer.squares
            .filter { $0.squareType == 0 && $0.isEditMode }
            .first?.matType == "レギュラー"

        colorPickerVC.setShowOnlyType0Colors(isRegularMat)

        if let image = renderer.currentImage ?? UIImage(contentsOfFile: square.texturePath ?? "") {
            colorPickerVC.setSourceImage(image)
        }
        present(colorPickerVC, animated: true)
    }

    // ログアウト
    func menuBarDidSelectExit() {
        let alert = UIAlertController(
            title: "ログアウト",
            message: "編集中のデザインは破棄されます。\nログアウトしますか？",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "キャンセル", style: .cancel))
        alert.addAction(UIAlertAction(title: "OK", style: .destructive) { [weak self] _ in
            exit(0)
        })
        present(alert, animated: true)
    }

    // 削除
    func menuBarDidSelectRemove() {
        guard let square = renderer.currentSquare else { return }
        guard square.squareType > 0 else { return }

        let alert = UIAlertController(
            title: "確認",
            message: "選択したオブジェクト\nを削除しますか？",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "キャンセル", style: .cancel))
        alert.addAction(UIAlertAction(title: "削除", style: .destructive) { [weak self] _ in
            if let index = self?.renderer.squares.firstIndex(where: { $0 === self?.renderer.currentSquare }) {
                self?.renderer.squares.remove(at: index)
                self?.renderer.currentSquare = nil
                self?.clearAllControlElements()
                self?.selectionBoxView?.removeFromSuperview()
                self?.selectionBoxView = nil
                self?.metalView.setNeedsDisplay()

                if let menuBar = self?.menuBar {
                    menuBar.updateDeleteButtonState(isSelected: false, squareType: nil)
                }
            }
        })

        present(alert, animated: true)
    }

    func menuBarDidSelectToForward() {
        guard let currentSquare = renderer.currentSquare,
              currentSquare.squareType > 0,
              let currentIndex = renderer.squares.firstIndex(where: { $0 === currentSquare }) else {
            return
        }

        if currentIndex == renderer.squares.count - 1 {
            return
        }

        renderer.squares.remove(at: currentIndex)
        renderer.squares.insert(currentSquare, at: currentIndex + 1)

        renderer.currentSquareIndex = currentIndex + 1

        metalView.setNeedsDisplay()
    }

    func menuBarDidSelectToBack() {
        guard let currentSquare = renderer.currentSquare,
              currentSquare.squareType > 0,
              let currentIndex = renderer.squares.firstIndex(where: { $0 === currentSquare }) else {
            return
        }

        let matIndices = renderer.squares.indices.filter { renderer.squares[$0].squareType == 0 }
        let lastMatIndex = matIndices.last ?? 0

        if currentIndex == lastMatIndex + 1 {
            return
        }

        renderer.squares.remove(at: currentIndex)
        renderer.squares.insert(currentSquare, at: currentIndex - 1)

        renderer.currentSquareIndex = currentIndex - 1

        metalView.setNeedsDisplay()
    }

    func menuBarDidSelectTextPosition() {
        let popoverView = UIView()
        popoverView.backgroundColor = UIColor.white
        popoverView.layer.cornerRadius = 8
        popoverView.layer.shadowColor = UIColor.black.cgColor
        popoverView.layer.shadowOffset = CGSize(width: 0, height: 2)
        popoverView.layer.shadowOpacity = 0.3
        popoverView.layer.shadowRadius = 4

        let buttonWidth: CGFloat = 80
        let buttonHeight: CGFloat = 50
        let padding: CGFloat = 15
        let columns: CGFloat = 3
        let rows: CGFloat = 3

        let popoverWidth = columns * buttonWidth + (columns + 1) * padding
        let popoverHeight = rows * buttonHeight + (rows + 1) * padding + buttonHeight + padding * 2

        let positions = [
            "A-1", "A-2", "A-3",
            "B-1", "B-2", "B-3",
            "C-1", "C-2", "C-3"
        ]

        var enabledPositions: [String: Bool] = [:]

        for position in positions {
            enabledPositions[position] = true
        }

        if let square = renderer.currentSquare,
           let matInfo = square.matInfo,
           let homeMatConfig = matInfo.homeMatConfig {

            let areaConfigs: [String: MatInfo.AreaConfig] = [
                "A-1": homeMatConfig.a1,
                "A-2": homeMatConfig.a2,
                "A-3": homeMatConfig.a3,
                "B-1": homeMatConfig.b1,
                "B-2": homeMatConfig.b2,
                "B-3": homeMatConfig.b3,
                "C-1": homeMatConfig.c1,
                "C-2": homeMatConfig.c2,
                "C-3": homeMatConfig.c3
            ]

            for (position, config) in areaConfigs {
                enabledPositions[position] = config.position.count > 1
            }
        }

        for (index, position) in positions.enumerated() {
            let row = CGFloat(index / 3)
            let column = CGFloat(index % 3)

            let button = UIButton(type: .system)
            button.setTitle(position, for: .normal)
            button.setTitleColor(UIColor.black, for: .normal)
            button.layer.cornerRadius = 5
            button.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .medium)

            let isEnabled = enabledPositions[position] ?? true
            button.isEnabled = isEnabled

            if isEnabled {
                button.setTitleColor(UIColor.blue, for: .normal)
                if position == textPositionManager.positionIdentifier {
                    button.titleLabel?.font = UIFont.systemFont(ofSize: 24, weight: .medium)
                } else {
                    button.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .medium)
                }
            } else {
                button.setTitleColor(UIColor.lightGray, for: .normal)
//                button.backgroundColor = UIColor.systemGray6
            }

            let x = padding + column * (buttonWidth + padding)
            let y = padding + row * (buttonHeight + padding)
            button.frame = CGRect(x: x, y: y, width: buttonWidth, height: buttonHeight)

            button.tag = index
            button.addTarget(self, action: #selector(textPositionButtonTapped(_:)), for: .touchUpInside)

            popoverView.addSubview(button)
        }

        let fontSampleButton = UIButton(type: .system)
        fontSampleButton.setTitle("文字見本", for: .normal)
        fontSampleButton.setTitleColor(UIColor.blue, for: .normal)
        fontSampleButton.layer.cornerRadius = 8
        fontSampleButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        
        let fontButtonWidth: CGFloat = 120
        let fontButtonX = (popoverWidth - fontButtonWidth) / 2
        let fontButtonY = rows * buttonHeight + (rows + 1) * padding + padding
        fontSampleButton.frame = CGRect(x: fontButtonX, y: fontButtonY, width: fontButtonWidth, height: buttonHeight)
        
        fontSampleButton.addTarget(self, action: #selector(showFontSample), for: .touchUpInside)
        popoverView.addSubview(fontSampleButton)

        popoverView.frame = CGRect(x: 0, y: 0, width: popoverWidth, height: popoverHeight)

        let popoverViewController = UIViewController()
        popoverViewController.view = popoverView
        popoverViewController.preferredContentSize = popoverView.frame.size
        popoverViewController.modalPresentationStyle = .popover

        if let popover = popoverViewController.popoverPresentationController {
            popover.sourceView = menuBar.textPositionButton
            popover.sourceRect = menuBar.textPositionButton!.bounds
            popover.permittedArrowDirections = .any
            popover.delegate = self
        }

        present(popoverViewController, animated: true)
    }

    @objc private func showFontSample() {
        let overlayView = UIView(frame: UIScreen.main.bounds)
        overlayView.backgroundColor = UIColor.black.withAlphaComponent(0.7)
        
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.translatesAutoresizingMaskIntoConstraints = false
        
        if let sampleImage = UIImage(named: "fonts-sample") {
            imageView.image = sampleImage
        } else {
            print("error: Image not found")
        }
        
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dismissFontSample(_:)))
        overlayView.addGestureRecognizer(tapGesture)
        
        overlayView.addSubview(imageView)
        
        NSLayoutConstraint.activate([
            imageView.centerXAnchor.constraint(equalTo: overlayView.centerXAnchor),
            imageView.centerYAnchor.constraint(equalTo: overlayView.centerYAnchor),
            imageView.widthAnchor.constraint(lessThanOrEqualTo: overlayView.widthAnchor, multiplier: 0.9),
            imageView.heightAnchor.constraint(lessThanOrEqualTo: overlayView.heightAnchor, multiplier: 0.9)
        ])
        
        if let keyWindow = UIApplication.shared.windows.first(where: { $0.isKeyWindow }) {
            keyWindow.addSubview(overlayView)
        }
    }
    
    @objc private func dismissFontSample(_ gesture: UITapGestureRecognizer) {
        gesture.view?.removeFromSuperview()
    }

    @objc private func textPositionButtonTapped(_ sender: UIButton) {
        guard let square = renderer.currentSquare else {
            dismiss(animated: true)
            return
        }

        let position = sender.tag
        let positionName = sender.title(for: .normal) ?? ""
        print("選択した位置：\(positionName)")

        // Parse position name (e.g., "A-1") to set vertical position and horizontal alignment
        if positionName.count >= 3 {
            let verticalChar = positionName.prefix(1)
            let horizontalChar = positionName.suffix(1)

            // Set vertical position
            switch verticalChar {
            case "A":
                textPositionManager.verticalPosition = .top
            case "B":
                textPositionManager.verticalPosition = .middle
            case "C":
                textPositionManager.verticalPosition = .bottom
            default:
                break
            }

            // Set horizontal alignment
            switch horizontalChar {
            case "1":
                textPositionManager.horizontalAlignment = .left
            case "2":
                textPositionManager.horizontalAlignment = .center
            case "3":
                textPositionManager.horizontalAlignment = .right
            default:
                break
            }

            print("Set text position to vertical: \(textPositionManager.verticalPosition), horizontal: \(textPositionManager.horizontalAlignment)")
            applyTextPositionAdjustment()
        }

        dismiss(animated: true)
    }
}

// テキスト位置調整に関するメソッドを追加
extension ViewController {
    // テキスト画像を作成
    private func createTextImageWithAlignment(text: String, alignment: NSTextAlignment,
                                              targetAspectRatio: CGFloat = 4.0) -> UIImage? {
        // --- ① テクスチャのサイズを決定（目標のアスペクト比と基準となる高さに基づく） ---
        let textureBaseHeight: CGFloat = 128 // 固定の基準像素高さ (調整可能)
        // アスペクト比を有効に保つ
        guard targetAspectRatio > 0 else {
            print("エラー：ターゲットのアスペクト比が無効です (\(targetAspectRatio))")
            return nil
        }
        let textureWidth = textureBaseHeight * targetAspectRatio
        let canvasSize = CGSize(width: textureWidth, height: textureBaseHeight)
        print("🎨 テクスチャを作成中、ターゲットアスペクト比：\(targetAspectRatio)、サイズ：\(canvasSize)")

        // --- ② フォントと属性の設定 ---
        // キャンバスの高さに合わせてフォントサイズを動的に調整
        let baseFontSize: CGFloat = 72 // 基準フォントサイズ
        // 基準フォントでの高さを取得しようとする
        let fontForHeightEstimate = UIFont.systemFont(ofSize: baseFontSize)
        let estimatedTextHeightForBaseFont = NSString(string: "Tg").size(withAttributes: [.font: fontForHeightEstimate]).height // "Tg"を使用して高さを推定
        let verticalPaddingFraction: CGFloat = 0.15 // 上下それぞれ 15% の余白を確保
        let availableHeight = canvasSize.height * (1.0 - verticalPaddingFraction * 2.0)

        var adjustedFontSize = baseFontSize // デフォルトのフォントサイズ
        if estimatedTextHeightForBaseFont > 0 && availableHeight > 0 {
             adjustedFontSize = max(10, baseFontSize * (availableHeight / estimatedTextHeightForBaseFont))
        } else {
             adjustedFontSize = max(10, canvasSize.height * 0.5) // 予備ロジック、高さの半分を占める
        }

        print("🎨 調整後のフォントサイズ：\(adjustedFontSize)")
        
        var fontColor = UIColor.black
        if let colorSquare = renderer.squares.first {
            let colorNo = colorSquare.matInfo?.homeMatConfig?.color
            fontColor = getDuskinColor(type: colorSquare.squareType, no: Int(colorNo ?? "0") ?? 0)
        }

        let attributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: adjustedFontSize, weight: .bold),
            .foregroundColor: fontColor // 不透明な色を使用
        ]

        // --- ③ 描画領域と配置 ---
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.alignment = alignment
        paragraphStyle.lineBreakMode = .byClipping // はみ出した部分を切り取る

        let drawingAttributes = attributes.merging([.paragraphStyle: paragraphStyle]) { (_, new) in new }

        // 描画領域はキャンバス全体から少しの水平マージンを差し引いたもの（垂直マージンはフォントサイズで調整済み）
         let horizontalPadding: CGFloat = max(10, canvasSize.width * 0.02) // 水平方向に少し余白を確保
         let drawingRect = CGRect(
             x: horizontalPadding,
             y: (canvasSize.height - adjustedFontSize * 1.2) / 2, // 描画の垂直中心点を見積もる
             width: canvasSize.width - horizontalPadding * 2,
             height: adjustedFontSize * 1.2 // 描画に少し垂直スペースを与える
         )

        // --- ④ 描画開始 ---
        UIGraphicsBeginImageContextWithOptions(canvasSize, false, 1.0) // scale 1.0
        defer { UIGraphicsEndImageContext() }
        guard let context = UIGraphicsGetCurrentContext() else { return nil }

        // （オプション）デバッグ用背景
        // UIColor.magenta.withAlphaComponent(0.1).setFill()
        // UIRectFill(CGRect(origin: .zero, size: canvasSize))

        // テキストの描画
        text.draw(with: drawingRect, options: [.usesLineFragmentOrigin], attributes: drawingAttributes, context: nil)

        return UIGraphicsGetImageFromCurrentImageContext()
    }
    
    private func calculateTargetDimensions(for labelSquare: Square, matSquare: Square) -> (scale: SIMD2<Float>, aspectRatio: Float, textureSize: CGSize)? {
        var paddingPx: Float = 20.0
        
        if labelSquare.matInfo != nil {
            let position = labelSquare.matInfo!.homeMatConfig!.positionList.first
            paddingPx = Float(position!.x)
            print("aqaz -----> paddingPx:\(paddingPx)")
        }
        
        
        let desiredHeightFraction: Float = 0.08
        let viewWidth = Float(metalView.bounds.width)
        let viewHeight = Float(metalView.bounds.height)

        guard viewWidth > 0, viewHeight > 0 else {
            print("⚠️ 目標サイズを計算できません：ビューサイズが無効です。")
            return nil
        }

        // 1. 目標の Scale X を計算する
        let totalPaddingNdcX = (paddingPx * 2.0 / viewWidth) * 2.0
        var targetScaleX = matSquare.scale.x - totalPaddingNdcX
        targetScaleX = max(0.01, targetScaleX)

        // 2. 目標の Scale Y を計算する
        let targetScaleY = desiredHeightFraction * 2.0

        let targetScale = SIMD2<Float>(targetScaleX, targetScaleY)

        // 3. 目標の視覚的なアスペクト比を計算する（アスペクト補正を無視して簡略化）
        guard targetScaleY > 0 else {
             print("⚠️ 目標のアスペクト比を計算できません：targetScaleY がゼロです。")
             return nil
        }
        let targetVisualAspectRatio = targetScaleX / targetScaleY
        guard targetVisualAspectRatio > 0 else {
            print("⚠️ 目標のアスペクト比を計算できません：計算結果が無効です。")
            return nil
        }


        // 4. テクスチャサイズの決定
        let textureBaseHeight: CGFloat = 128 // 基準高さ
        let textureWidth = textureBaseHeight * CGFloat(targetVisualAspectRatio)
        let targetTextureSize = CGSize(width: textureWidth, height: textureBaseHeight)

        print("📐 目標サイズを計算：Scale=\(targetScale), AspectRatio=\(targetVisualAspectRatio), TextureSize=\(targetTextureSize)")
        return (targetScale, targetVisualAspectRatio, targetTextureSize)
    }


    // 修正：アライメントの更新時にトリガー
    private func updateLabelAlignmentImmediately(label labelSquare: Square, targetH: HorizontalAlignment) {
        print("アライメント更新を実行：\(targetH)")
        guard let currentText = labelSquare.textContent else { print("エラー：テキスト内容なし"); return }
        guard let matSquare = renderer.squares.first(where: { $0.squareType == 0 && !$0.isHidden }) else { print("エラー：背景画像なし"); return }

        let targetAlignment = targetH.textAlignment

        // --- ① 目標サイズを計算 ---
        guard let targetDimensions = calculateTargetDimensions(for: labelSquare, matSquare: matSquare) else {
            print("エラー：目標サイズを計算できません、テクスチャを更新できません。")
            return
        }

        // --- ② マッチする新しいテクスチャを生成 ---
        if let newTextImage = createTextImageWithAlignment(text: currentText, alignment: targetAlignment, targetAspectRatio: CGFloat(targetDimensions.aspectRatio)),
           let newTexture = renderer.imageToTexture(image: newTextImage, device: renderer.device) {
            labelSquare.texture = newTexture
            labelSquare.originalAspectRatio = targetDimensions.aspectRatio
            labelSquare.size = SIMD2<Float>(Float(targetDimensions.textureSize.width), Float(targetDimensions.textureSize.height))
            labelSquare.scale = targetDimensions.scale
            labelSquare.currentHorizontalAlignment = targetH 

            // --- ④ 詳細なログを追加し、最終状態を確認 ---
            print("🔄 アラインメント更新 - マトリックス更新前の最終状態：")
            print("  テクスチャサイズ：\(labelSquare.size)")
            print("  元のアスペクト比：\(labelSquare.originalAspectRatio)")
            print("  スケール：\(labelSquare.scale)") // スケールが目標値であることを確認
            print("  位置：\(labelSquare.position)") // 位置が意図しない変更を受けていないことを確認

            // --- ⑤ 変換マトリックスを更新 ---
            labelSquare.updateTransformMatrix(preserveScale: true) // 更新適用
            print("リアルタイムテクスチャ（目標比率で）の更新に成功。")
            metalView.setNeedsDisplay()
        } else {
            print("エラー：リアルタイムテクスチャ更新（ターゲットスケール）は失敗しました。")
        }
        print("リアルタイムアライン更新が完了しました。")
    }

    
    private func updateLabelVerticalPositionImmediately(label labelSquare: Square, mat matSquare: Square, targetV: VerticalPosition) {
        print("垂直位置の更新を実行：\(targetV)")

        // --- ① 目標スケールを再計算する（mat が移動または拡大縮小した可能性があるため） ---
        var verticalPaddingPx: Float = 10.0
        if labelSquare.matInfo != nil {
            let position = labelSquare.matInfo!.homeMatConfig!.positionList.first
            print("aqaz -----> positionList:\(labelSquare.matInfo!.homeMatConfig!.positionList)")
            verticalPaddingPx = Float(position!.y)
            print("aqaz -----> verticalPaddingPx:\(verticalPaddingPx)")
        }

        let viewHeight = Float(metalView.bounds.height)
        var paddingNdcY:Float = 0.0
        if viewHeight > 0 {
            paddingNdcY = (verticalPaddingPx / viewHeight) * 2.0
            print("📐 目標スケールを計算中（位置更新による）: paddingNdcY=\(paddingNdcY)")
        }
        guard let targetDimensions = calculateTargetDimensions(for: labelSquare, matSquare: matSquare) else {
             print("エラー：目標サイズを計算できません。位置を更新できません。")
             return
        }
        if labelSquare.scale != targetDimensions.scale {
            labelSquare.scale = targetDimensions.scale
            print("即時調整 Scale (位置更新のため): \(labelSquare.scale)")
        }

        // --- ② 位置決めロジック (正確な境界と Label の新しい Scale を使用) ---
        guard let b = matSquare.calculateWorldNormalizedBounds(viewSize: metalView.bounds.size) else { /* ... エラー処理 ... */ return }
        let labelHalfH = labelSquare.scale.y * 0.5
        var targetY: Float = labelSquare.position.y
        print("🧮 即時位置計算：matBounds.minY=\(b.minY), matBounds.maxY=\(b.maxY), labelHalfH=\(labelHalfH)")

        switch targetV {
        case .top:    targetY = b.maxY - labelHalfH - paddingNdcY
        case .middle: targetY = (b.minY + b.maxY) * 0.5
        case .bottom: targetY = b.minY + labelHalfH + paddingNdcY
        }
        print("🧮 計算された targetY = \(targetY)")

        // --- ③ 位置を更新 (Y だけ更新) ---
        labelSquare.position.y = targetY
        // X 座標は変えない。初回追加時のみ変更 (初回追加は textInputDone で処理)

        // --- ④ 変換とビューを更新 ---
        labelSquare.updateTransformMatrix(preserveScale: true) // 重要
        metalView.setNeedsDisplay()
        createSelectionBox() // 選択枠を更新

         // --- ⑤ Square 内部で記録している状態を更新 ---
         labelSquare.currentVerticalPosition = targetV // 内部状態の更新を忘れずに

        print("リアルタイム垂直位置の更新が完了しました。新しい位置 Y: \(targetY)")
    }

    // テキスト画像を一時保存
    private func saveTextImageTemporarily(image: UIImage) -> String? {
        let tempDir = FileManager.default.temporaryDirectory
        let fileName = "text_\(UUID().uuidString).png"
        let fileURL = tempDir.appendingPathComponent(fileName)

        do {
            if let data = image.pngData() {
                try data.write(to: fileURL)
                return fileURL.path
            }
        } catch {
            print("Error saving text image: \(error)")
        }

        return nil
    }

    // 現在選択されているテキストオブジェクトからテキストを抽出する
    private func extractTextFromCurrentSquare() -> String? {
        guard let square = renderer.currentSquare, square.squareType == 3 else { return nil }

        // ファイルパスがある場合、ファイル名からテキストを抽出しようとする
        if let texturePath = square.texturePath {
            // ファイル名からテキストを抽出しようとする
            let fileName = URL(fileURLWithPath: texturePath).lastPathComponent
            if fileName.hasPrefix("text_") {
                // ファイルの内容からテキストを抽出しようとする
                if let image = UIImage(contentsOfFile: texturePath) {
                    // ここではデフォルトのテキストを使用する。画像からテキストを抽出するには OCR 技術が必要
                    // 実際のアプリケーションでは、テキスト内容を保存するか OCR 技術を使用する必要がある
                    return "テキスト例"
                }
            }
        }

        return nil
    }
    @objc private func adjustVerticalPositionTapped(_ sender: UIButton) {
        guard let position = VerticalPosition(rawValue: sender.tag) else { return }

        textPositionManager.verticalPosition = position
        print("垂直位置を調整します：\(position.description)")

        // ボタンの外観を更新する
        if let stackView = sender.superview as? UIStackView {
            for case let button as UIButton in stackView.arrangedSubviews {
                let isSelected = button.tag == position.rawValue
                button.backgroundColor = isSelected ? .systemBlue : .lightGray
                button.setTitleColor(isSelected ? .white : .darkGray, for: .normal)
            }
        }
    }

    @objc private func adjustHorizontalAlignmentTapped(_ sender: UIButton) {
        guard let alignment = HorizontalAlignment(rawValue: sender.tag) else { return }

        textPositionManager.horizontalAlignment = alignment
        print("水平揃えを調整します：\(alignment.description)")

        // ボタンの外観を更新する
        if let stackView = sender.superview as? UIStackView {
            for case let button as UIButton in stackView.arrangedSubviews {
                let isSelected = button.tag == alignment.rawValue
                button.backgroundColor = isSelected ? .systemBlue : .lightGray
                button.setTitleColor(isSelected ? .white : .darkGray, for: .normal)
            }
        }
    }

    @objc private func applyTextPositionAdjustment() {
        guard let labelSquare = renderer.currentSquare, labelSquare.squareType == 3 else {
            dismiss(animated: true)
            print("エラー：テキストオブジェクトが選択されていないか、型が一致しません。")
            return
        }

        guard let matSquare = renderer.squares.first(where: { $0.squareType == 0 && !$0.isHidden }) else {
            dismiss(animated: true)
            print("エラー：ベースマップオブジェクトが見つかりません。")
            return
        }

        // 目標状態を取得 ---
        let targetVertical = textPositionManager.verticalPosition
        let targetHorizontal = textPositionManager.horizontalAlignment
        let targetAlignment = targetHorizontal.textAlignment

        // --- 現在の状態を取得 ---
        let currentVertical = labelSquare.currentVerticalPosition ?? .middle // 未記録なら middle と仮定
        let currentHorizontal = labelSquare.currentHorizontalAlignment ?? .center // 未記録なら center と仮定
        let currentText = labelSquare.textContent ?? "テキストサンプル" // テキスト内容を取得

        // --- 更新が必要な部分を判定 ---
        let verticalChanged = targetVertical != currentVertical
        let horizontalChanged = targetHorizontal != currentHorizontal

        print("調整を適用：目標 V=\(targetVertical), H=\(targetHorizontal)。現在 V=\(currentVertical), H=\(currentHorizontal)。テキスト='\(currentText)'")
        print("更新が必要：垂直=\(verticalChanged)、水平揃え=\(horizontalChanged)")

        var needsTextureUpdate = horizontalChanged
        var needsPositionUpdate = verticalChanged

        // 初回適用の場合（現在の状態がない）は両方更新
        if labelSquare.currentVerticalPosition == nil || labelSquare.currentHorizontalAlignment == nil {
             print("初回適用、テクスチャと位置を更新します。")
             needsTextureUpdate = true
             needsPositionUpdate = true
        }
        
        guard let targetDimensions = calculateTargetDimensions(for: labelSquare, matSquare: matSquare) else {
                print("エラー (apply)")
                dismiss(animated: true)
                return
            }

        // --- テクスチャ更新 (水平揃え変更または初回適用) ---
        if needsTextureUpdate {
            print("テクスチャを更新中...")
            if let newTextImage = createTextImageWithAlignment(
                text: currentText,
                alignment: targetAlignment,
                targetAspectRatio: CGFloat(targetDimensions.aspectRatio)),
               let newTexture = renderer.imageToTexture(image: newTextImage, device: renderer.device) {
                labelSquare.texture = newTexture
                // 注意：createTextImageWithAlignment が画像サイズを変更した場合は scale を再調整する必要があるが、
                // 現在の実装のように固定サイズ (512x128) の場合は不要。
                print("テクスチャを更新しました。")
            } else {
                print("エラー: テクスチャの更新に失敗しました。")
                // 必要に応じて位置更新を継続するか判断
            }
        }

        // --- 縦位置を更新 (縦位置変更または初回適用) ---
        if needsPositionUpdate {
            print("縦位置を更新中...")

            updateLabelVerticalPositionImmediately(label: labelSquare, mat: matSquare, targetV: targetVertical)

            print("縦位置を更新しました。新しい Y: \(targetVertical)")

        } else if needsTextureUpdate {
            print("テクスチャのみ更新、位置とスケールは変更なし。")
        }

        // --- 新しい状態を記録 ---
        labelSquare.currentVerticalPosition = targetVertical
        labelSquare.currentHorizontalAlignment = targetHorizontal
        // labelSquare.textContent = currentText // テキストは別で更新される場合がある

        // --- 表示と選択枠を更新 ---
        metalView.setNeedsDisplay()
        if currentMode == .select || needsPositionUpdate { // 選択モード中または位置を動かした場合、選択枠を更新
            createSelectionBox() // 選択枠の位置を更新
        }

        dismiss(animated: true) // ポップアップを閉じる
        print("調整の適用が完了しました。")
    }
}

extension ViewController: UIPopoverPresentationControllerDelegate {
    func adaptivePresentationStyle(for controller: UIPresentationController) -> UIModalPresentationStyle {
        return .none
    }
}

extension ViewController: UIGestureRecognizerDelegate {
    // 複数のジェスチャーを同時認識するかどうか
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer,
                           shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }

    // ジェスチャー認識の可否判定
    func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
        switch gestureRecognizer {
        case is UIPanGestureRecognizer where currentMode == .deform:
            return true
        case is UIPanGestureRecognizer where currentMode == .transform:
            return true
        case is UIRotationGestureRecognizer, is UIPinchGestureRecognizer:
            return currentMode == .transform
        case is UITapGestureRecognizer where currentMode == .select:
            return true
        case is UITapGestureRecognizer where currentMode == .rectangle:
            return true
         case is UIPanGestureRecognizer where currentMode == .restore:
            return true
        default:
            return currentMode == .eraser
        }
    }
}

extension ViewController {
    @objc private func handleCornerPan(_ gesture: UIPanGestureRecognizer) {
        guard currentMode == .deform else { return }
        guard let square = renderer.currentSquare else { return }
        let location = gesture.location(in: metalView)

        let corners = square.computeCornerPoints(
            modelMatrix: square.transform.matrix,
            viewSize: metalView.bounds.size
        )

        switch gesture.state {
        case .began:
            let corners = square.computeCornerPoints(
                modelMatrix: square.transform.matrix,
                viewSize: metalView.bounds.size
            )

            var minDistance = CGFloat.infinity
            for (index, point) in corners.enumerated() {
                let distance = hypot(point.x - location.x, point.y - location.y)
                if distance < 30 && distance < minDistance {
                    minDistance = distance
                    selectedCorner = index
                }
            }
            lastPanLocation = location

        case .changed:
            guard let index = selectedCorner else { return }
            let translation = CGPoint(
                x: location.x - lastPanLocation.x,
                y: location.y - lastPanLocation.y
            )

            let viewSize = metalView.bounds.size
            let clipX = Float(translation.x) / Float(viewSize.width) * 2
            let clipY = -Float(translation.y) / Float(viewSize.height) * 2

            let inverseMatrix = square.transform.matrix.inverse
            if inverseMatrix.isSingular {
                print("Cannot deform: Transformation matrix is singular")
                return
            }

            let clipVec = SIMD4<Float>(clipX, clipY, 0, 0)
            let modelVec = inverseMatrix * clipVec
            square.cornerDisplacements[index] += SIMD2(modelVec.x, modelVec.y)

            lastPanLocation = location
            metalView.setNeedsDisplay()

        case .ended, .cancelled:
            selectedCorner = nil

        default: break
        }
    }
}

extension ViewController {
    private func createCornerControlViews() {
        cornerControlViews.forEach { $0.removeFromSuperview() }
        cornerControlViews.removeAll()

        for index in 0..<4 {
            let controlView = UIImageView(image: UIImage(named: "cerclePoint"))
            controlView.frame = CGRect(x: 0, y: 0, width: 40, height: 40)
            controlView.isUserInteractionEnabled = true
            controlView.tag = index

            let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handleControlViewPan(_:)))
            controlView.addGestureRecognizer(panGesture)

            metalView.addSubview(controlView)
            cornerControlViews.append(controlView)
        }

        updateCornerControlPositions()
    }

    private func updateCornerControlPositions() {
        guard let square = renderer.currentSquare else { return }
        let corners = square.computeCornerPoints(
            modelMatrix: square.transform.matrix,
            viewSize: metalView.bounds.size
        )

        let offset: CGFloat = 10

        for (index, corner) in corners.enumerated() {
            let controlView = cornerControlViews[index]

            let offsetX = corner.x < metalView.bounds.width / 2 ? -offset : offset
            let offsetY = corner.y < metalView.bounds.height / 2 ? -offset : offset

            controlView.center = CGPoint(
                x: corner.x + offsetX,
                y: corner.y + offsetY
            )
            controlView.isHidden = currentMode != .deform
        }
    }

    @objc private func handleControlViewPan(_ gesture: UIPanGestureRecognizer) {
        guard currentMode == .deform,
            let controlView = gesture.view,
            let square = renderer.currentSquare else { return }

        let location = gesture.location(in: metalView)

        switch gesture.state {
        case .began:
            selectedCorner = controlView.tag
            lastPanLocation = location

        case .changed:
            let translation = CGPoint(
                x: location.x - lastPanLocation.x,
                y: location.y - lastPanLocation.y
            )

            let viewSize = metalView.bounds.size
            let clipX = Float(translation.x) / Float(viewSize.width) * 2
            let clipY = -Float(translation.y) / Float(viewSize.height) * 2

            let inverseMatrix = square.transform.matrix.inverse
            if inverseMatrix.isSingular {
                print("Cannot deform: Transformation matrix is singular")
                return
            }

            let clipVec = SIMD4<Float>(clipX, clipY, 0, 0)
            let modelVec = inverseMatrix * clipVec
            square.cornerDisplacements[controlView.tag] += SIMD2(modelVec.x, modelVec.y)

            lastPanLocation = location
            metalView.setNeedsDisplay()
            updateCornerControlPositions()

        case .ended, .cancelled:
            selectedCorner = nil

        default:
            break
        }
    }

    private func autoSelectAddedElement() {
        // if menuBar.isEditMode && renderer.currentSquare != nil {
            currentMode = .select
            createSelectionBox()
            if let square = renderer.currentSquare {
                menuBar.updateDeleteButtonState(isSelected: true, squareType: square.squareType)
            }
        // }
    }
}

extension ViewController {
    private func addCenterControlPoint() {
        guard let square = renderer.currentSquare else { return }

        if let existingCenterPoint = metalView.subviews.first(where: {
            ($0 as? UIImageView)?.accessibilityIdentifier == "centerControlPoint"
        }) {
            existingCenterPoint.removeFromSuperview()
        }

        let centerPoint = UIImageView(image: UIImage(named: "cerclePoint"))
        centerPoint.frame.size = CGSize(width: 40, height: 40)
        centerPoint.isUserInteractionEnabled = true
        centerPoint.accessibilityIdentifier = "centerControlPoint"


        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handleCenterPointPan(_:)))
        panGesture.delegate = self
        centerPoint.addGestureRecognizer(panGesture)

        metalView.addSubview(centerPoint)

        let center = getSquareCenter(square)
        centerPoint.center = center

    }

    @objc private func handleCenterPointPan(_ gesture: UIPanGestureRecognizer) {
        guard currentMode == .transform, let square = renderer.currentSquare else { return }

        let location = gesture.location(in: metalView)

        switch gesture.state {
        case .began:
            lastCenterPanLocation = location
        case .changed:
            let translation = gesture.translation(in: metalView)
            let viewSize = metalView.bounds.size

            let normalizedTranslationX = Float(translation.x / viewSize.width * 2.0)
            let normalizedTranslationY = Float(-translation.y / viewSize.height * 2.0)


            let originalScale = square.scale
            square.position.x += normalizedTranslationX
            square.position.y += normalizedTranslationY
            square.updateTransformMatrix()

            square.scale = originalScale
            square.updateTransformMatrix(preserveScale: true)
            gesture.setTranslation(.zero, in: metalView)
            metalView.setNeedsDisplay()

            if let centerPoint = metalView.subviews.first(where: { ($0 as? UIImageView)?.accessibilityIdentifier == "centerControlPoint" }) as? UIImageView {
                centerPoint.center = getSquareCenter(square)
            }
            updateRotePointPosition()
            updateArrowPositions()
            updateSelectionBoxIfNeeded()

        case .ended, .cancelled:
            break
        default:
            break
        }
    }

    private func addArrowImages() {
        cornerControlViews.forEach { $0.removeFromSuperview() }
        cornerControlViews.removeAll()

        guard let square = renderer.currentSquare else { return }
        let corners = square.computeCornerPoints(
            modelMatrix: square.transform.matrix,
            viewSize: metalView.bounds.size
        )

        let arrowImages = [
            ("Arow2", corners[0]),
            ("Arow1", corners[1]),
            ("Arow1", corners[2]),
            ("Arow2", corners[3])
        ]

        let offset: CGFloat = 10

        for (index, (imageName, corner)) in arrowImages.enumerated() {
            let arrowView = UIImageView(image: UIImage(named: imageName))
            let offsetX = corner.x < metalView.bounds.width / 2 ? -offset : offset
            let offsetY = corner.y < metalView.bounds.height / 2 ? -offset : offset
            arrowView.frame = CGRect(x: corner.x + offsetX - 20, y: corner.y + offsetY - 20, width: 40, height: 40)
            arrowView.isUserInteractionEnabled = true
            arrowView.tag = index

            let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handleArrowPan(_:)))
            arrowView.addGestureRecognizer(panGesture)

            metalView.addSubview(arrowView)
            cornerControlViews.append(arrowView)
        }
    }
}

extension ViewController {
    @objc private func handleArrowPan(_ gesture: UIPanGestureRecognizer) {
        guard currentMode == .transform,
              let arrowView = gesture.view,
              let square = renderer.currentSquare else { return }

        let translation = gesture.translation(in: metalView)
        let viewSize = metalView.bounds.size
        let sensitivity: CGFloat = 0.5
        switch gesture.state {
        case .changed:
            let rotation = square.rotation
            let cosAngle = cos(rotation)
            let sinAngle = sin(rotation)

            let rotatedX = translation.x * CGFloat(cosAngle) + translation.y * CGFloat(sinAngle)
            let rotatedY = -translation.x * CGFloat(sinAngle) + translation.y * CGFloat(cosAngle)

//            let originalSize = getSquareSize(square)
//            let aspectRatio = originalSize.width / originalSize.height

            var scaleFactor: CGFloat = 1.0
            switch arrowView.tag {
            case 0: // 左上
                scaleFactor = 1.0 + (-rotatedX + rotatedY) / (viewSize.width * sensitivity)
            case 1: // 右上
                scaleFactor = 1.0 + (rotatedX + rotatedY) / (viewSize.width * sensitivity)
            case 2: // 左下
                scaleFactor = 1.0 + (-rotatedX - rotatedY) / (viewSize.width * sensitivity)
            case 3: // 右下
                scaleFactor = 1.0 + (rotatedX - rotatedY) / (viewSize.width * sensitivity)
            default: break
            }
 
            let newScale = max(0.1, min(10.0, scaleFactor))
            let currentScale = square.scale
            let scaleX = currentScale.x * Float(newScale)
//            let scaleY = scaleX / Float(aspectRatio)
            let scaleY = scaleX / square.originalAspectRatio
            square.scale = SIMD2<Float>(scaleX, scaleY)


            square.updateTransformMatrix(preserveScale: true)
            gesture.setTranslation(.zero, in: metalView)
            metalView.setNeedsDisplay()
            updateRotePointPosition()
            updateArrowPositions()
            updateCenterControlPointPosition()
            updateSelectionBoxIfNeeded()

        default:
            break
        }
    }

    private func updateArrowPositions() {
        guard let square = renderer.currentSquare else { return }
        let corners = square.computeCornerPoints(
            modelMatrix: square.transform.matrix,
            viewSize: metalView.bounds.size
        )

        let offset: CGFloat = 10

        for (index, corner) in corners.enumerated() {
            let arrowView = cornerControlViews[index]
            let offsetX = corner.x < metalView.bounds.width / 2 ? -offset : offset
            let offsetY = corner.y < metalView.bounds.height / 2 ? -offset : offset
            arrowView.center = CGPoint(x: corner.x + offsetX, y: corner.y + offsetY)

            let rotationAngle = -CGFloat(square.rotation)
            arrowView.transform = CGAffineTransform(rotationAngle: rotationAngle)
        }
    }

    private func addRotePoint() {
        guard let square = renderer.currentSquare else { return }

        rotePointImageView?.removeFromSuperview()

        let rotePointImage = UIImageView(image: UIImage(named: "RotePoint"))
        rotePointImage.frame.size = rotePointImageSize
        rotePointImage.isUserInteractionEnabled = true

        let center = getSquareCenter(square)
        let size = getSquareSize(square)

        // let maxDimension = max(size.width, size.height)
        // let radius = maxDimension / 2.0 + 10
        let radius = size.height / 2.0 + 10
        let uikitAngle = -CGFloat(square.rotation) - .pi / 2

        let newX = center.x + radius * cos(uikitAngle)
        let newY = center.y + radius * sin(uikitAngle)

        rotePointImage.center = CGPoint(x: newX, y: newY)
        rotePointImage.transform = CGAffineTransform(rotationAngle: -CGFloat(square.rotation))

        metalView.addSubview(rotePointImage)

        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handleRotePointPan(_:)))
        rotePointImage.addGestureRecognizer(panGesture)

        rotePointImageView = rotePointImage
        print("Initial square center: \(center), rotePoint center: \(rotePointImage.center), rotation: \(square.rotation)")
    }

    @objc private func handleRotePointPan(_ gesture: UIPanGestureRecognizer) {
        guard let square = renderer.currentSquare else { return }

        let location = gesture.location(in: metalView)
        let center = getSquareCenter(square)

        let originalAspectRatio = square.originalAspectRatio

        let normLocation = SIMD2<Float>(
            Float((location.x / metalView.bounds.width) * 2.0 - 1.0),
            Float(1.0 - (location.y / metalView.bounds.height) * 2.0)
        )
        let normCenter = SIMD2<Float>(
            Float((center.x / metalView.bounds.width) * 2.0 - 1.0),
            Float(1.0 - (center.y / metalView.bounds.height) * 2.0)
        )

        let dx = normLocation.x - normCenter.x
        let dy = normLocation.y - normCenter.y
        let newAngle = atan2(dy, dx)

        switch gesture.state {
        case .began:
            // 回転開始時に元のスケールを保存
            initialScale = square.scale
            lastCenterPanLocation = center
            metalAngle = CGFloat(newAngle)

        case .changed:
            let previousAngle = Float(metalAngle)
            var deltaAngle = newAngle - previousAngle

            if deltaAngle > Float.pi { deltaAngle -= 2 * Float.pi }
            if deltaAngle < -Float.pi { deltaAngle += 2 * Float.pi }

            square.rotation += deltaAngle
            metalAngle = CGFloat(newAngle)

            //square.updateTransformMatrix(preserveScale: true)
            square.updateCenterRotateMatrix() 
            
            let newCenter = getSquareCenter(square)

            let centerDiffX = Float((lastCenterPanLocation.x - newCenter.x) / metalView.bounds.width * 2.0)
            let centerDiffY = Float((newCenter.y - lastCenterPanLocation.y) / metalView.bounds.height * 2.0)

            square.position.x += centerDiffX
            square.position.y += centerDiffY

            let currentScale = square.scale
            //square.updateTransformMatrix()

            square.scale = currentScale
            //square.updateTransformMatrix(preserveScale: true)
            metalView.setNeedsDisplay()
            updateRotePointPosition()
            updateArrowPositions()
            updateCenterControlPointPosition()
            updateSelectionBoxIfNeeded()

        case .ended, .cancelled:
            let finalScale = square.scale

            //square.updateTransformMatrix(preserveScale: true)

            initialScale = nil
            break

        default:
            break
        }

        gesture.setTranslation(.zero, in: metalView)
    }

    private func updateRotePointPosition() {
        guard let square = renderer.currentSquare,
              let rotePointImage = rotePointImageView else { return }

        let center = getSquareCenter(square)
        let size = getSquareSize(square)

        print("aqaz V updateRotePointPosition center :\(center)")
        print("aqaz V updateRotePointPosition size :\(size)")
        // let maxDimension = max(size.width, size.height)
        // let radius = maxDimension / 2.0 + 10
        let radius = size.height / 2.0 + 10
        let uikitAngle = -CGFloat(square.rotation) - .pi / 2

        let newX = center.x + radius * cos(uikitAngle)
        let newY = center.y + radius * sin(uikitAngle)

        rotePointImage.center = CGPoint(x: newX, y: newY)
        rotePointImage.transform = CGAffineTransform(rotationAngle: -CGFloat(square.rotation))
    }


    private func normalizeAngle(_ angle: Float) -> Float {
        var normalized = angle
        while normalized > Float.pi { normalized -= 2 * Float.pi }
        while normalized < -Float.pi { normalized += 2 * Float.pi }
        return normalized
    }

    private func updateCenterControlPointPosition() {
        guard let square = renderer.currentSquare else { return }

        if let centerPoint = metalView.subviews.first(where: {
            ($0 as? UIImageView)?.accessibilityIdentifier == "centerControlPoint"
        }) as? UIImageView {
            centerPoint.center = getSquareCenter(square)
        }
    }

    private func metalAngleToUIKit(_ metalAngle: Float) -> Float {
        let uikitAngle = -metalAngle
        return normalizeAngle(uikitAngle)
    }

    private func getSquareCenter(_ square: Square) -> CGPoint {
        let center = square.computeTextureCenter(viewSize: metalView.bounds.size)
        return center
    }

    private func getSquareSize(_ square: Square) -> CGSize {
        let size = square.computeTextureSize(viewSize: metalView.bounds.size)
        return size
    }
}

extension ViewController {
    private func clearAllControlElements() {
        metalAngle = 0
        cornerControlViews.forEach { $0.removeFromSuperview() }
        cornerControlViews.removeAll()

        rotePointImageView?.removeFromSuperview()
        rotePointImageView = nil

        metalView.subviews.forEach { subview in
            if let imageView = subview as? UIImageView,
               imageView.accessibilityIdentifier == "centerControlPoint" {
                imageView.removeFromSuperview()
            }
        }
    }

    func menuBarDidSwitchMode(toEditMode: Bool) {
        clearAllControlElements()
        menuBar.clearButtonStyle()
        selectionBoxView?.removeFromSuperview()
        selectionBoxView = nil
        menuBar.updateColorButtonState(forMatType: "", squareType: 0, isSelected: false)
        menuBar.updateTransformButtonState(isSelected: false, squareType: 0)
        menuBar.updateForwordBackButtonState(isSelected: false, squareType: 0)
        menuBar.updateTextPositionButtonState(forMatType: "", isSelected: false)
        print("aqaz toEditMode:\(toEditMode)")

        // 編集モードから非編集モードへの切り替え時に確認アラートを表示
        if !toEditMode && renderer.currentSquare != nil {
            let alert = UIAlertController(
                title: "確認",
                message: "マット配置シミュレーションの\nマットを編集中のマットで\n上書きしますか？",
                preferredStyle: .alert
            )

            alert.addAction(UIAlertAction(title: "いいえ", style: .cancel) { [weak self] _ in
                self?.renderer.hideEditModeObjects()
                self?.backgroundImageView.isHidden = false
                return
            })

            alert.addAction(UIAlertAction(title: "はい", style: .default) { [weak self] _ in
                // 確認後に実際の切り替え処理を実行
                self?.performModeSwitching(toEditMode: false)
            })

            self.present(alert, animated: true)
            return
        }

        // 編集モードへの切り替えまたは確認後の処理
        performModeSwitching(toEditMode: toEditMode)
    }

    private func performModeSwitching(toEditMode: Bool) {
        backgroundImageView.isHidden = toEditMode

        for square in renderer.squares {
            print("aqaz square.isEditMode:\(square.isEditMode), squareType:\(square.squareType), isHidden:\(square.isHidden )")
        }

        if toEditMode {
            renderer.hideNonEditModeObjects()
            for square in renderer.squares where square.isEditMode {
                square.isHidden = false
            }

        } else {
            var editModeMat: (path: String, matType: String, position: SIMD2<Float>, scale: SIMD2<Float>, rotation: Float, cornerDisplacements: [SIMD2<Float>], color: UIColor?)? = nil

            var editModeOnePoints: [(path: String, position: SIMD2<Float>, scale: SIMD2<Float>, rotation: Float, cornerDisplacements: [SIMD2<Float>], color: UIColor?)] = []

            for onePointSquare in renderer.squares.filter({ $0.isEditMode && $0.squareType == 2 }) {
                let onePointColor = onePointSquare.color
                let uiColor: UIColor?

                if onePointColor != SIMD4<Float>(1, 1, 1, 1) {
                    uiColor = UIColor(red: CGFloat(onePointColor.x),
                                    green: CGFloat(onePointColor.y),
                                    blue: CGFloat(onePointColor.z),
                                    alpha: CGFloat(onePointColor.w))
                } else {
                    uiColor = nil
                }

                editModeOnePoints.append((
                    onePointSquare.texturePath ?? "",
                    onePointSquare.position,
                    onePointSquare.scale,
                    onePointSquare.rotation,
                    onePointSquare.cornerDisplacements,
                    uiColor
                ))
            }

            if let matSquare = renderer.squares.first(where: { $0.isEditMode && $0.squareType == 0 }) {
                let matColor = matSquare.color
                let uiColor: UIColor?

                if matColor != SIMD4<Float>(0, 0, 0, 0) {
                    uiColor = UIColor(red: CGFloat(matColor.x),
                                     green: CGFloat(matColor.y),
                                     blue: CGFloat(matColor.z),
                                     alpha: CGFloat(matColor.w))
                } else {
                    uiColor = nil
                }

                editModeMat = (
                    matSquare.texturePath ?? "",
                    matSquare.matType,
                    matSquare.position,
                    matSquare.scale,
                    matSquare.rotation,
                    matSquare.cornerDisplacements,
                    uiColor
                )
            }

            if let matInfo = editModeMat, !matInfo.path.isEmpty {
                let editModeObjects = renderer.squares.filter { $0.isEditMode }

                if editModeObjects.count > 1 {
                    if let mergedImage = createMergedImage(fromEditModeObjects: editModeObjects) {
                        if let mergedImagePath = saveMergedImage(mergedImage) {
                            editModeMat = (
                                mergedImagePath,
                                matInfo.matType,
                                matInfo.position,
                                matInfo.scale,
                                matInfo.rotation,
                                matInfo.cornerDisplacements,
                                nil
                            )
                        }
                    }
                } else if let matSquare = editModeObjects.first(where: { $0.squareType == 0 }),
                    let originalColor = matSquare.originalColor,
                    let matPath = matSquare.texturePath,
                    let originalImage = UIImage(contentsOfFile: matPath) {
                    let newColor = UIColor(
                        red: CGFloat(matSquare.color.x),
                        green: CGFloat(matSquare.color.y),
                        blue: CGFloat(matSquare.color.z),
                        alpha: CGFloat(matSquare.color.w)
                    )

                    let coloredImage = replaceColor(in: originalImage, originalColor: originalColor, withColor: newColor)
                    if let coloredImagePath = saveMergedImage(coloredImage) {
                        editModeMat = (
                            coloredImagePath,
                            matInfo.matType,
                            matInfo.position,
                            matInfo.scale,
                            matInfo.rotation,
                            matInfo.cornerDisplacements,
                            newColor
                        )
                        print("色置換を適用しました")
                    }
                }
            }

            renderer.hideEditModeObjects()

            print("aqaz editModeMat:\(editModeMat)")
            if let matInfo = editModeMat {
                let success = renderer.addSquare(imageName: matInfo.path, squareType: 0, matType: matInfo.matType, isEditMode: false)
                print("aqaz success:\(success)")
                if success, let newSquare = renderer.currentSquare {
                    print("aqaz medium =========")
                    renderer.updateSquareScaleToCustomSize(option: .medium)

                    if let color = matInfo.color {
                        // UIColor を SIMD4<Float>に変換
                        var red: CGFloat = 0, green: CGFloat = 0, blue: CGFloat = 0, alpha: CGFloat = 0
                        color.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
                        print(" R:\(red) G:\(green) B:\(blue) A:\(alpha)")
                        newSquare.color = SIMD4<Float>(
                            Float(red),
                            Float(green),
                            Float(blue),
                            Float(alpha)
                        )

                        newSquare.originalColor = color

                        if let image = UIImage(contentsOfFile: matInfo.path) {
                            let coloredImage = replaceColor(in: image, originalColor: color, withColor: color)

                            if let texture = renderer.imageToTexture(image: coloredImage, device: renderer.device) {
                                newSquare.texture = texture
                            }
                        }
                    }
                }
            }

            for square in renderer.squares where !square.isEditMode {
                square.isHidden = false
            }

            if renderer.currentSquare == nil {
                if let matSquare = renderer.squares.first(where: { !$0.isEditMode && $0.squareType == 0 }) {
                    renderer.currentSquare = matSquare
                    if let index = renderer.squares.firstIndex(where: { $0 === matSquare }) {
                        renderer.currentSquareIndex = index
                    }

                    menuBar.updateColorButtonState(forMatType: matSquare.matType, squareType: 0, isSelected: false)
                }
            }
        }

        clearAllControlElements()

        metalView.setNeedsDisplay()
        currentMode = .select
    }

    private func createMergedImage(fromEditModeObjects objects: [Square]) -> UIImage? {
        guard let matSquare = objects.first(where: { $0.squareType == 0 }) else { return nil }

        guard let matPath = matSquare.texturePath,
        var matImage = UIImage(contentsOfFile: matPath) else { return nil }

        if matSquare.color != SIMD4<Float>(1, 1, 1, 1) && matSquare.originalColor != nil {
            let originalColor = matSquare.originalColor!
            let newColor = UIColor(
                red: CGFloat(matSquare.color.x),
                green: CGFloat(matSquare.color.y),
                blue: CGFloat(matSquare.color.z),
                alpha: CGFloat(matSquare.color.w)
            )

            matImage = replaceColor(in: matImage, originalColor: originalColor, withColor: newColor)
        }

        UIGraphicsBeginImageContextWithOptions(matImage.size, false, UIScreen.main.scale)
        defer { UIGraphicsEndImageContext() }

        matImage.draw(in: CGRect(origin: .zero, size: matImage.size))

        let matTransform = matSquare.transform.matrix

        for object in objects where object.squareType != 0 {
            guard let objectPath = object.texturePath,
                  let objectImage = UIImage(contentsOfFile: objectPath) else { continue }

            let objectTransform = object.transform.matrix
            let relativeTransform = matTransform.inverse * objectTransform

            let scale = CGFloat(sqrt(pow(relativeTransform.columns.0.x, 2) + pow(relativeTransform.columns.0.y, 2)))
            let rotation = CGFloat(atan2(relativeTransform.columns.0.y, relativeTransform.columns.0.x))

            let relativeX = CGFloat(relativeTransform.columns.3.x) * matImage.size.width / 2 + matImage.size.width / 2
            let relativeY = CGFloat(-relativeTransform.columns.3.y) * matImage.size.height / 2 + matImage.size.height / 2

            let objectWidth = objectImage.size.width * scale
            let objectHeight = objectImage.size.height * scale
            let objectRect = CGRect(
                x: relativeX - objectWidth / 2,
                y: relativeY - objectHeight / 2,
                width: objectWidth,
                height: objectHeight
            )

            let context = UIGraphicsGetCurrentContext()!
            context.saveGState()

            context.translateBy(x: relativeX, y: relativeY)
            context.rotate(by: rotation)
            context.translateBy(x: -relativeX, y: -relativeY)

            objectImage.draw(in: objectRect)

            context.restoreGState()
        }

        return UIGraphicsGetImageFromCurrentImageContext()
    }

    private func saveMergedImage(_ image: UIImage) -> String? {
        let fileName = "merged_mat_\(Date().timeIntervalSince1970).png"

        let tempDirectory = FileManager.default.temporaryDirectory
        let fileURL = tempDirectory.appendingPathComponent(fileName)

        do {
            if let pngData = image.pngData() {
                try pngData.write(to: fileURL)
                return fileURL.path
            }
        } catch {
            print("error: \(error)")
        }

        return nil
    }

    private func replaceColor(in image: UIImage, originalColor: UIColor, withColor newColor: UIColor) -> UIImage {
        guard let cgImage = image.cgImage else { return image }

        let width = cgImage.width
        let height = cgImage.height
        let bitsPerComponent = 8
        let bytesPerRow = width * 4
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let bitmapInfo = CGBitmapInfo(rawValue: CGImageAlphaInfo.premultipliedLast.rawValue)

        guard let context = CGContext(data: nil,
                                    width: width,
                                    height: height,
                                    bitsPerComponent: bitsPerComponent,
                                    bytesPerRow: bytesPerRow,
                                    space: colorSpace,
                                    bitmapInfo: bitmapInfo.rawValue) else { return image }

        let rect = CGRect(x: 0, y: 0, width: width, height: height)
        context.draw(cgImage, in: rect)

        var origRed: CGFloat = 0, origGreen: CGFloat = 0, origBlue: CGFloat = 0, origAlpha: CGFloat = 0
        originalColor.getRed(&origRed, green: &origGreen, blue: &origBlue, alpha: &origAlpha)

        var newRed: CGFloat = 0, newGreen: CGFloat = 0, newBlue: CGFloat = 0, newAlpha: CGFloat = 0
        newColor.getRed(&newRed, green: &newGreen, blue: &newBlue, alpha: &newAlpha)

        guard let data = context.data else { return image }
        let pixelData = data.bindMemory(to: UInt8.self, capacity: width * height * 4)

        let tolerance: CGFloat = 0.1
        for y in 0..<height {
            for x in 0..<width {
                let pixelIndex = (width * y + x) * 4

                let r = CGFloat(pixelData[pixelIndex]) / 255.0
                let g = CGFloat(pixelData[pixelIndex + 1]) / 255.0
                let b = CGFloat(pixelData[pixelIndex + 2]) / 255.0

                let rDiff = abs(r - origRed)
                let gDiff = abs(g - origGreen)
                let bDiff = abs(b - origBlue)

                if rDiff <= tolerance && gDiff <= tolerance && bDiff <= tolerance {
                    pixelData[pixelIndex] = UInt8(newRed * 255)
                    pixelData[pixelIndex + 1] = UInt8(newGreen * 255)
                    pixelData[pixelIndex + 2] = UInt8(newBlue * 255)
                }
            }
        }

        if let newCGImage = context.makeImage() {
            return UIImage(cgImage: newCGImage, scale: image.scale, orientation: image.imageOrientation)
        }

        return image
    }

    private func createColorMask(from image: UIImage, targetColor: UIColor, tolerance: CGFloat) -> CGImage? {
        guard let inputCGImage = image.cgImage else { return nil }

        let width = inputCGImage.width
        let height = inputCGImage.height

        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let bitmapInfo = CGBitmapInfo(rawValue: CGImageAlphaInfo.premultipliedLast.rawValue)

        guard let context = CGContext(data: nil,
                                    width: width,
                                    height: height,
                                    bitsPerComponent: 8,
                                    bytesPerRow: 0,
                                    space: colorSpace,
                                    bitmapInfo: bitmapInfo.rawValue) else { return nil }

        context.draw(inputCGImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        var targetRed: CGFloat = 0, targetGreen: CGFloat = 0, targetBlue: CGFloat = 0, targetAlpha: CGFloat = 0
        targetColor.getRed(&targetRed, green: &targetGreen, blue: &targetBlue, alpha: &targetAlpha)

        guard let data = context.data else { return nil }

        let pixelData = data.bindMemory(to: UInt8.self, capacity: width * height * 4)
        for y in 0..<height {
            for x in 0..<width {
                let pixelIndex = (width * y + x) * 4

                let r = CGFloat(pixelData[pixelIndex]) / 255.0
                let g = CGFloat(pixelData[pixelIndex + 1]) / 255.0
                let b = CGFloat(pixelData[pixelIndex + 2]) / 255.0
                let a = CGFloat(pixelData[pixelIndex + 3]) / 255.0

                let rDiff = abs(r - targetRed)
                let gDiff = abs(g - targetGreen)
                let bDiff = abs(b - targetBlue)

                if rDiff <= tolerance && gDiff <= tolerance && bDiff <= tolerance {
                    pixelData[pixelIndex + 3] = UInt8(a * 255)
                } else {
                    pixelData[pixelIndex + 3] = 0
                }
            }
        }

        return context.makeImage()
    }

    private func getDominantColor(from image: UIImage) -> UIColor? {
        guard let cgImage = image.cgImage else { return nil }

        let width = cgImage.width
        let height = cgImage.height

        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let bitmapInfo = CGBitmapInfo(rawValue: CGImageAlphaInfo.premultipliedLast.rawValue)

        guard let context = CGContext(data: nil,
                                    width: width,
                                    height: height,
                                    bitsPerComponent: 8,
                                    bytesPerRow: 0,
                                    space: colorSpace,
                                    bitmapInfo: bitmapInfo.rawValue) else { return nil }

        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        guard let data = context.data else { return nil }
        let pixelData = data.bindMemory(to: UInt8.self, capacity: width * height * 4)

        var totalRed: Int = 0
        var totalGreen: Int = 0
        var totalBlue: Int = 0
        var totalAlpha: Int = 0
        var totalPixels: Int = 0

        let samplingRate = 10
        for y in stride(from: 0, to: height, by: samplingRate) {
            for x in stride(from: 0, to: width, by: samplingRate) {
                let pixelIndex = (width * y + x) * 4

                if pixelData[pixelIndex + 3] < 10 { continue }

                totalRed += Int(pixelData[pixelIndex])
                totalGreen += Int(pixelData[pixelIndex + 1])
                totalBlue += Int(pixelData[pixelIndex + 2])
                totalAlpha += Int(pixelData[pixelIndex + 3])
                totalPixels += 1
            }
        }

        guard totalPixels > 0 else { return nil }

        let avgRed = CGFloat(totalRed) / CGFloat(totalPixels) / 255.0
        let avgGreen = CGFloat(totalGreen) / CGFloat(totalPixels) / 255.0
        let avgBlue = CGFloat(totalBlue) / CGFloat(totalPixels) / 255.0
        let avgAlpha = CGFloat(totalAlpha) / CGFloat(totalPixels) / 255.0

        return UIColor(red: avgRed, green: avgGreen, blue: avgBlue, alpha: avgAlpha)
    }
}

extension ViewController: LogoSelectorViewControllerDelegate {
    func logoSelectorViewController(_ controller: LogoSelectorViewController, didSelectImageNamed imageName: String) {
        if FileManager.default.fileExists(atPath: imageName) {
            let success = renderer.addSquare(imageName: imageName, squareType: 1, isEditMode: true)
            if success {
                clearAllControlElements()
                renderer.updateSquareScaleToCustomSize(option: .small)
                autoSelectAddedElement()
                menuBar.updateColorButtonState(forMatType: "", squareType: 1, isSelected: false)
                menuBar.updateTransformButtonState(isSelected: true, squareType: 1)
                menuBar.updateForwordBackButtonState(isSelected: true, squareType: 1)
                menuBar.updateTextPositionButtonState(forMatType: "", isSelected: true)

            }
        }

        controller.dismiss(animated: true)
    }
}

extension ViewController: OnePointViewControllerDelegate {
    func onePointViewController(_ controller: OnePointViewController, didSelectImageAt path: String) {
        if FileManager.default.fileExists(atPath: path) {
            let success = renderer.addSquare(imageName: path, squareType: 2, isEditMode: true)
            if success {
                clearAllControlElements()
                renderer.updateSquareScaleToCustomSize(option: .small)
                autoSelectAddedElement()
                menuBar.updateColorButtonState(forMatType: "", squareType: 2, isSelected: true)
                menuBar.updateTransformButtonState(isSelected: true, squareType: 2)
                menuBar.updateForwordBackButtonState(isSelected: true, squareType: 2)
                menuBar.updateTextPositionButtonState(forMatType: "", isSelected: true)
            }
        }
    }
}

extension ViewController: PhotoCatalogViewControllerDelegate {
    func photoCatalogViewControllerDidSavePhoto(_ controller: PhotoCatalogViewController, image: UIImage) {
        backgroundImageView.image = image
        controller.dismiss(animated: true)
    }

    func photoCatalogViewControllerDidDeletePhoto(_ controller: PhotoCatalogViewController, at index: Int) {
        controller.dismiss(animated: true)
    }
}

extension ViewController: UITextViewDelegate {
    func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
        if text == "\n" {
            textInputDone(UIBarButtonItem())
            return false
        }
        // 2. 文字数制限の設定と最大制限値を取得
        var maxLength: Int = Int.max // デフォルトは無制限
        var limits: MatInfo.CharacterLimits? = nil
        // 現在の Square または背景画像から設定を取得しようとする
        if let currentMat = renderer.currentSquare, currentMat.matType == "家庭用マット", let info = currentMat.matInfo, let config = info.homeMatConfig {
            let charLimits = config.textConfig.maxCounts
             limits = charLimits
        } else if let mat = renderer.squares.first(where: { $0.squareType == 0 && !$0.isHidden }), mat.matType == "家庭用マット", let info = mat.matInfo, let config = info.homeMatConfig{
            let charLimits = config.textConfig.maxCounts 
            limits = charLimits
        }
        // UITextViewDelegate 内のリアルタイム入力制限ログ
        if let characterLimits = limits {
            let allLimits = [
                characterLimits.kanji,
                characterLimits.hiragana,
                characterLimits.katakana,
                characterLimits.lowercase,
                characterLimits.uppercase
            ]
            maxLength = allLimits.max() ?? Int.max
            print("入力制限：最大長 \(maxLength)")
        } else {
            print("入力制限：設定なし、制限しません。")
        }


        // 3. 新しいテキストとその長さを計算する
        let currentText = textView.text ?? ""
        // NSString のメソッドを使うと range の扱いが便利
        guard let stringRange = Range(range, in: currentText) else { return false }
        let updatedText = currentText.replacingCharacters(in: stringRange, with: text)
        let newLength = updatedText.count

        // 4. 長さが最大制限を超えていないか確認する
        if newLength > maxLength {
            print("リアルタイム入力制限：最大長さ \(maxLength) に達したため入力をブロックします。")
            // （任意）ここでユーザーに視覚的なフィードバックを与えることができる、例えば振動や警告表示
            // UIImpactFeedbackGenerator(style: .light).impactOccurred()
            return false // 最大長さを超えているため入力不可
        }

        // 5. 最大長さを超えていなければ入力を許可する
        return true
    }
}

extension ViewController: ColorPickerViewControllerDelegate {
    func colorPickerViewController(_ controller: ColorPickerViewController, didSelectColor color: UIColor) {
        if let _ = renderer.currentSquare {
            renderer.updateSquareColor(color: color)
            metalView.setNeedsDisplay()
        }

        controller.dismiss(animated: true)
    }

    func colorPickerViewController(_ controller: ColorPickerViewController, didReplaceColor originalColor: UIColor, withColor newColor: UIColor, sourceImage: UIImage?) {
        if let square = renderer.currentSquare {
            var r: CGFloat = 0, g: CGFloat = 0, b: CGFloat = 0, a: CGFloat = 0
            newColor.getRed(&r, green: &g, blue: &b, alpha: &a)

            square.originalColor = originalColor

            renderer.replaceSquareColor(originalColor: originalColor, newColor: newColor)

            if let path = square.texturePath, let image = UIImage(contentsOfFile: path) {
                let coloredImage = replaceColor(in: image, originalColor: originalColor, withColor: newColor)

                if let newPath = saveMergedImage(coloredImage) {
                    square.texturePath = newPath

                    if let texture = renderer.imageToTexture(image: coloredImage, device: renderer.device) {
                        square.texture = texture
                    }
                }
            }

            metalView.setNeedsDisplay()
        }
        controller.dismiss(animated: true)
    }
}

