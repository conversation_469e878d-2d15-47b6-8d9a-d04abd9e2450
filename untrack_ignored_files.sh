#!/bin/bash

# Files to untrack (keep them on disk, but remove from git tracking)
git rm --cached -r MatSimulator.xcodeproj/xcuserdata/
git rm --cached -r MatSimulator.xcodeproj/project.xcworkspace/xcuserdata/
git rm --cached MatSimulator.xcodeproj/project.xcworkspace/xcuserdata/solo.xcuserdatad/UserInterfaceState.xcuserstate
git rm --cached MatSimulator.xcodeproj/project.xcworkspace/xcuserdata/takayukisakamoto.xcuserdatad/UserInterfaceState.xcuserstate
git rm --cached MatSimulator.xcodeproj/project.xcworkspace/xcuserdata/zeromax.xcuserdatad/UserInterfaceState.xcuserstate
git rm --cached MatSimulator.xcodeproj/xcuserdata/solo.xcuserdatad/xcdebugger/Breakpoints_v2.xcbkptlist
git rm --cached MatSimulator.xcodeproj/xcuserdata/taka<PERSON><PERSON><PERSON><PERSON>.xcuserdatad/xcschemes/xcschememanagement.plist
git rm --cached MatSimulator.xcodeproj/xcuserdata/zeromax.xcuserdatad/xcdebugger/Breakpoints_v2.xcbkptlist
git rm --cached MatSimulator.xcodeproj/xcuserdata/zeromax.xcuserdatad/xcschemes/xcschememanagement.plist

# After running this script, commit the changes:
echo ""
echo "Files have been untracked. Now you should commit these changes with:"
echo "git commit -m \"Remove files that should be ignored\""
echo ""
echo "These files will remain on your disk but will no longer be tracked by Git."
