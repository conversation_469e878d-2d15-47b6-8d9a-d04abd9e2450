// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		023116132D663E1300AEF10D /* FileDownloader.swift in Sources */ = {isa = PBXBuildFile; fileRef = 023116122D663E1300AEF10D /* FileDownloader.swift */; };
		023116152D663E1B00AEF10D /* Constants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 023116142D663E1B00AEF10D /* Constants.swift */; };
		DF0FFB542D834152008E2935 /* ColorPickerViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0FFB532D834152008E2935 /* ColorPickerViewController.swift */; };
		DF0FFB562D86C666008E2935 /* DuskinColor.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0FFB552D86C666008E2935 /* DuskinColor.swift */; };
		DF20FC2B2D7D716300246381 /* LogoSelectorViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF20FC2A2D7D716300246381 /* LogoSelectorViewController.swift */; };
		DF20FC2D2D7D7D0100246381 /* LogoEditorViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF20FC2C2D7D7D0100246381 /* LogoEditorViewController.swift */; };
		DF20FC2F2D7DC0B100246381 /* LogoThumbnailCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF20FC2E2D7DC0B100246381 /* LogoThumbnailCell.swift */; };
		DF20FC312D7DD77100246381 /* OnePointViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF20FC302D7DD77100246381 /* OnePointViewController.swift */; };
		DF7B834C2D78AB1B001FB47D /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF7B834B2D78AB1B001FB47D /* Metal.framework */; };
		DF98BAE62D54FCC80089D711 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF98BAE52D54FCC80089D711 /* AppDelegate.swift */; };
		DF98BAE82D54FCC80089D711 /* SceneDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF98BAE72D54FCC80089D711 /* SceneDelegate.swift */; };
		DF98BAEA2D54FCC80089D711 /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF98BAE92D54FCC80089D711 /* ViewController.swift */; };
		DF98BAED2D54FCC80089D711 /* Base in Resources */ = {isa = PBXBuildFile; fileRef = DF98BAEC2D54FCC80089D711 /* Base */; };
		DF98BAEF2D54FCC90089D711 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = DF98BAEE2D54FCC90089D711 /* Assets.xcassets */; };
		DF98BAF22D54FCC90089D711 /* Base in Resources */ = {isa = PBXBuildFile; fileRef = DF98BAF12D54FCC90089D711 /* Base */; };
		DF98BAFD2D54FCC90089D711 /* MatSimulatorTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF98BAFC2D54FCC90089D711 /* MatSimulatorTests.swift */; };
		DF98BB072D54FCC90089D711 /* MatSimulatorUITests.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF98BB062D54FCC90089D711 /* MatSimulatorUITests.swift */; };
		DF98BB092D54FCC90089D711 /* MatSimulatorUITestsLaunchTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF98BB082D54FCC90089D711 /* MatSimulatorUITestsLaunchTests.swift */; };
		DFA6317B2D5642240052D6E5 /* Renderer.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFA6317A2D5642240052D6E5 /* Renderer.swift */; };
		DFA6317F2D5642EE0052D6E5 /* Shaders.metal in Sources */ = {isa = PBXBuildFile; fileRef = DFA6317E2D5642EE0052D6E5 /* Shaders.metal */; };
		DFA631812D5651240052D6E5 /* Square.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFA631802D5651240052D6E5 /* Square.swift */; };
		DFCCAC992D74548C00E851FA /* MTLTexture+UIImage.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFCCAC972D74548C00E851FA /* MTLTexture+UIImage.swift */; };
		DFCCAC9B2D74727900E851FA /* PhotoCatalogViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFCCAC9A2D74727900E851FA /* PhotoCatalogViewController.swift */; };
		DFCCAC9D2D747B1100E851FA /* PhotoCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFCCAC9C2D747B1100E851FA /* PhotoCell.swift */; };
		DFFFA7822D6977E100FC249A /* MatCatalogViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFFFA7812D6977E100FC249A /* MatCatalogViewController.swift */; };
		DFFFA7842D69C49900FC249A /* BackgroundImageViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFFFA7832D69C49900FC249A /* BackgroundImageViewController.swift */; };
		DFFFA7882D6B0DD600FC249A /* MenuBarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFFFA7872D6B0DD600FC249A /* MenuBarView.swift */; };
		F84AAB6B2DB28E680031DF33 /* MatRenderer.swift in Sources */ = {isa = PBXBuildFile; fileRef = F84AAB6A2DB28E680031DF33 /* MatRenderer.swift */; };
		F84AAB6D2DB28E720031DF33 /* HomeMatTextModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = F84AAB6C2DB28E720031DF33 /* HomeMatTextModel.swift */; };
		F84AAB6F2DB2963F0031DF33 /* HomeMatPositionInfo.swift in Sources */ = {isa = PBXBuildFile; fileRef = F84AAB6E2DB2963F0031DF33 /* HomeMatPositionInfo.swift */; };
		F84AAB712DB40C2B0031DF33 /* TextPositioning.swift in Sources */ = {isa = PBXBuildFile; fileRef = F84AAB702DB40C2B0031DF33 /* TextPositioning.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		DF98BAF92D54FCC90089D711 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = DF98BADA2D54FCC80089D711 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DF98BAE12D54FCC80089D711;
			remoteInfo = MatSimulator;
		};
		DF98BB032D54FCC90089D711 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = DF98BADA2D54FCC80089D711 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DF98BAE12D54FCC80089D711;
			remoteInfo = MatSimulator;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		023116122D663E1300AEF10D /* FileDownloader.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FileDownloader.swift; sourceTree = "<group>"; };
		023116142D663E1B00AEF10D /* Constants.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Constants.swift; sourceTree = "<group>"; };
		DF0FFB532D834152008E2935 /* ColorPickerViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ColorPickerViewController.swift; sourceTree = "<group>"; };
		DF0FFB552D86C666008E2935 /* DuskinColor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DuskinColor.swift; sourceTree = "<group>"; };
		DF20FC2A2D7D716300246381 /* LogoSelectorViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LogoSelectorViewController.swift; sourceTree = "<group>"; };
		DF20FC2C2D7D7D0100246381 /* LogoEditorViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LogoEditorViewController.swift; sourceTree = "<group>"; };
		DF20FC2E2D7DC0B100246381 /* LogoThumbnailCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LogoThumbnailCell.swift; sourceTree = "<group>"; };
		DF20FC302D7DD77100246381 /* OnePointViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OnePointViewController.swift; sourceTree = "<group>"; };
		DF7B834B2D78AB1B001FB47D /* Metal.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Metal.framework; path = System/Library/Frameworks/Metal.framework; sourceTree = SDKROOT; };
		DF98BAE22D54FCC80089D711 /* MatSimulator.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = MatSimulator.app; sourceTree = BUILT_PRODUCTS_DIR; };
		DF98BAE52D54FCC80089D711 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		DF98BAE72D54FCC80089D711 /* SceneDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SceneDelegate.swift; sourceTree = "<group>"; };
		DF98BAE92D54FCC80089D711 /* ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		DF98BAEC2D54FCC80089D711 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		DF98BAEE2D54FCC90089D711 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		DF98BAF12D54FCC90089D711 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		DF98BAF32D54FCC90089D711 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		DF98BAF82D54FCC90089D711 /* MatSimulatorTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MatSimulatorTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		DF98BAFC2D54FCC90089D711 /* MatSimulatorTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MatSimulatorTests.swift; sourceTree = "<group>"; };
		DF98BB022D54FCC90089D711 /* MatSimulatorUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MatSimulatorUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		DF98BB062D54FCC90089D711 /* MatSimulatorUITests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MatSimulatorUITests.swift; sourceTree = "<group>"; };
		DF98BB082D54FCC90089D711 /* MatSimulatorUITestsLaunchTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MatSimulatorUITestsLaunchTests.swift; sourceTree = "<group>"; };
		DFA6317A2D5642240052D6E5 /* Renderer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Renderer.swift; sourceTree = "<group>"; };
		DFA6317E2D5642EE0052D6E5 /* Shaders.metal */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.metal; path = Shaders.metal; sourceTree = "<group>"; };
		DFA631802D5651240052D6E5 /* Square.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Square.swift; sourceTree = "<group>"; };
		DFCCAC972D74548C00E851FA /* MTLTexture+UIImage.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "MTLTexture+UIImage.swift"; sourceTree = "<group>"; };
		DFCCAC9A2D74727900E851FA /* PhotoCatalogViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PhotoCatalogViewController.swift; sourceTree = "<group>"; };
		DFCCAC9C2D747B1100E851FA /* PhotoCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PhotoCell.swift; sourceTree = "<group>"; };
		DFFFA7812D6977E100FC249A /* MatCatalogViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MatCatalogViewController.swift; sourceTree = "<group>"; };
		DFFFA7832D69C49900FC249A /* BackgroundImageViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BackgroundImageViewController.swift; sourceTree = "<group>"; };
		DFFFA7872D6B0DD600FC249A /* MenuBarView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MenuBarView.swift; sourceTree = "<group>"; };
		F84AAB6A2DB28E680031DF33 /* MatRenderer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MatRenderer.swift; sourceTree = "<group>"; };
		F84AAB6C2DB28E720031DF33 /* HomeMatTextModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeMatTextModel.swift; sourceTree = "<group>"; };
		F84AAB6E2DB2963F0031DF33 /* HomeMatPositionInfo.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeMatPositionInfo.swift; sourceTree = "<group>"; };
		F84AAB702DB40C2B0031DF33 /* TextPositioning.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TextPositioning.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		DF98BADF2D54FCC80089D711 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DF7B834C2D78AB1B001FB47D /* Metal.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DF98BAF52D54FCC90089D711 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DF98BAFF2D54FCC90089D711 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		DF7B834A2D78AB1B001FB47D /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				DF7B834B2D78AB1B001FB47D /* Metal.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		DF98BAD92D54FCC80089D711 = {
			isa = PBXGroup;
			children = (
				DF98BAE42D54FCC80089D711 /* MatSimulator */,
				DF98BAFB2D54FCC90089D711 /* MatSimulatorTests */,
				DF98BB052D54FCC90089D711 /* MatSimulatorUITests */,
				DF98BAE32D54FCC80089D711 /* Products */,
				DF7B834A2D78AB1B001FB47D /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		DF98BAE32D54FCC80089D711 /* Products */ = {
			isa = PBXGroup;
			children = (
				DF98BAE22D54FCC80089D711 /* MatSimulator.app */,
				DF98BAF82D54FCC90089D711 /* MatSimulatorTests.xctest */,
				DF98BB022D54FCC90089D711 /* MatSimulatorUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		DF98BAE42D54FCC80089D711 /* MatSimulator */ = {
			isa = PBXGroup;
			children = (
				F84AAB702DB40C2B0031DF33 /* TextPositioning.swift */,
				F84AAB6E2DB2963F0031DF33 /* HomeMatPositionInfo.swift */,
				F84AAB6C2DB28E720031DF33 /* HomeMatTextModel.swift */,
				F84AAB6A2DB28E680031DF33 /* MatRenderer.swift */,
				DFCCAC982D74548C00E851FA /* Extensions */,
				DF98BAE52D54FCC80089D711 /* AppDelegate.swift */,
				DFFFA7832D69C49900FC249A /* BackgroundImageViewController.swift */,
				DF0FFB532D834152008E2935 /* ColorPickerViewController.swift */,
				023116142D663E1B00AEF10D /* Constants.swift */,
				DF0FFB552D86C666008E2935 /* DuskinColor.swift */,
				023116122D663E1300AEF10D /* FileDownloader.swift */,
				DF20FC2C2D7D7D0100246381 /* LogoEditorViewController.swift */,
				DF20FC2A2D7D716300246381 /* LogoSelectorViewController.swift */,
				DF20FC2E2D7DC0B100246381 /* LogoThumbnailCell.swift */,
				DFFFA7812D6977E100FC249A /* MatCatalogViewController.swift */,
				DFFFA7872D6B0DD600FC249A /* MenuBarView.swift */,
				DF20FC302D7DD77100246381 /* OnePointViewController.swift */,
				DFCCAC9C2D747B1100E851FA /* PhotoCell.swift */,
				DFCCAC9A2D74727900E851FA /* PhotoCatalogViewController.swift */,
				DFA6317A2D5642240052D6E5 /* Renderer.swift */,
				DF98BAE72D54FCC80089D711 /* SceneDelegate.swift */,
				DFA6317E2D5642EE0052D6E5 /* Shaders.metal */,
				DFA631802D5651240052D6E5 /* Square.swift */,
				DF98BAE92D54FCC80089D711 /* ViewController.swift */,
				DF98BAEB2D54FCC80089D711 /* Main.storyboard */,
				DF98BAEE2D54FCC90089D711 /* Assets.xcassets */,
				DF98BAF02D54FCC90089D711 /* LaunchScreen.storyboard */,
				DF98BAF32D54FCC90089D711 /* Info.plist */,
			);
			path = MatSimulator;
			sourceTree = "<group>";
		};
		DF98BAFB2D54FCC90089D711 /* MatSimulatorTests */ = {
			isa = PBXGroup;
			children = (
				DF98BAFC2D54FCC90089D711 /* MatSimulatorTests.swift */,
			);
			path = MatSimulatorTests;
			sourceTree = "<group>";
		};
		DF98BB052D54FCC90089D711 /* MatSimulatorUITests */ = {
			isa = PBXGroup;
			children = (
				DF98BB062D54FCC90089D711 /* MatSimulatorUITests.swift */,
				DF98BB082D54FCC90089D711 /* MatSimulatorUITestsLaunchTests.swift */,
			);
			path = MatSimulatorUITests;
			sourceTree = "<group>";
		};
		DFCCAC982D74548C00E851FA /* Extensions */ = {
			isa = PBXGroup;
			children = (
				DFCCAC972D74548C00E851FA /* MTLTexture+UIImage.swift */,
			);
			path = Extensions;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		DF98BAE12D54FCC80089D711 /* MatSimulator */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DF98BB0C2D54FCC90089D711 /* Build configuration list for PBXNativeTarget "MatSimulator" */;
			buildPhases = (
				DF98BADE2D54FCC80089D711 /* Sources */,
				DF98BADF2D54FCC80089D711 /* Frameworks */,
				DF98BAE02D54FCC80089D711 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = MatSimulator;
			productName = MatSimulator;
			productReference = DF98BAE22D54FCC80089D711 /* MatSimulator.app */;
			productType = "com.apple.product-type.application";
		};
		DF98BAF72D54FCC90089D711 /* MatSimulatorTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DF98BB0F2D54FCC90089D711 /* Build configuration list for PBXNativeTarget "MatSimulatorTests" */;
			buildPhases = (
				DF98BAF42D54FCC90089D711 /* Sources */,
				DF98BAF52D54FCC90089D711 /* Frameworks */,
				DF98BAF62D54FCC90089D711 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				DF98BAFA2D54FCC90089D711 /* PBXTargetDependency */,
			);
			name = MatSimulatorTests;
			productName = MatSimulatorTests;
			productReference = DF98BAF82D54FCC90089D711 /* MatSimulatorTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		DF98BB012D54FCC90089D711 /* MatSimulatorUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DF98BB122D54FCC90089D711 /* Build configuration list for PBXNativeTarget "MatSimulatorUITests" */;
			buildPhases = (
				DF98BAFE2D54FCC90089D711 /* Sources */,
				DF98BAFF2D54FCC90089D711 /* Frameworks */,
				DF98BB002D54FCC90089D711 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				DF98BB042D54FCC90089D711 /* PBXTargetDependency */,
			);
			name = MatSimulatorUITests;
			productName = MatSimulatorUITests;
			productReference = DF98BB022D54FCC90089D711 /* MatSimulatorUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		DF98BADA2D54FCC80089D711 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1540;
				LastUpgradeCheck = 1540;
				TargetAttributes = {
					DF98BAE12D54FCC80089D711 = {
						CreatedOnToolsVersion = 15.4;
					};
					DF98BAF72D54FCC90089D711 = {
						CreatedOnToolsVersion = 15.4;
						TestTargetID = DF98BAE12D54FCC80089D711;
					};
					DF98BB012D54FCC90089D711 = {
						CreatedOnToolsVersion = 15.4;
						TestTargetID = DF98BAE12D54FCC80089D711;
					};
				};
			};
			buildConfigurationList = DF98BADD2D54FCC80089D711 /* Build configuration list for PBXProject "MatSimulator" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = DF98BAD92D54FCC80089D711;
			productRefGroup = DF98BAE32D54FCC80089D711 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				DF98BAE12D54FCC80089D711 /* MatSimulator */,
				DF98BAF72D54FCC90089D711 /* MatSimulatorTests */,
				DF98BB012D54FCC90089D711 /* MatSimulatorUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		DF98BAE02D54FCC80089D711 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DF98BAEF2D54FCC90089D711 /* Assets.xcassets in Resources */,
				DF98BAF22D54FCC90089D711 /* Base in Resources */,
				DF98BAED2D54FCC80089D711 /* Base in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DF98BAF62D54FCC90089D711 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DF98BB002D54FCC90089D711 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		DF98BADE2D54FCC80089D711 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F84AAB6F2DB2963F0031DF33 /* HomeMatPositionInfo.swift in Sources */,
				DF20FC2D2D7D7D0100246381 /* LogoEditorViewController.swift in Sources */,
				DFA631812D5651240052D6E5 /* Square.swift in Sources */,
				F84AAB6B2DB28E680031DF33 /* MatRenderer.swift in Sources */,
				DFCCAC9B2D74727900E851FA /* PhotoCatalogViewController.swift in Sources */,
				DFFFA7882D6B0DD600FC249A /* MenuBarView.swift in Sources */,
				DF20FC2F2D7DC0B100246381 /* LogoThumbnailCell.swift in Sources */,
				DFCCAC9D2D747B1100E851FA /* PhotoCell.swift in Sources */,
				F84AAB712DB40C2B0031DF33 /* TextPositioning.swift in Sources */,
				DFA6317F2D5642EE0052D6E5 /* Shaders.metal in Sources */,
				F84AAB6D2DB28E720031DF33 /* HomeMatTextModel.swift in Sources */,
				DF20FC2B2D7D716300246381 /* LogoSelectorViewController.swift in Sources */,
				DF20FC312D7DD77100246381 /* OnePointViewController.swift in Sources */,
				DFCCAC992D74548C00E851FA /* MTLTexture+UIImage.swift in Sources */,
				DF98BAEA2D54FCC80089D711 /* ViewController.swift in Sources */,
				023116152D663E1B00AEF10D /* Constants.swift in Sources */,
				DF98BAE62D54FCC80089D711 /* AppDelegate.swift in Sources */,
				023116132D663E1300AEF10D /* FileDownloader.swift in Sources */,
				DFFFA7842D69C49900FC249A /* BackgroundImageViewController.swift in Sources */,
				DF0FFB542D834152008E2935 /* ColorPickerViewController.swift in Sources */,
				DFFFA7822D6977E100FC249A /* MatCatalogViewController.swift in Sources */,
				DFA6317B2D5642240052D6E5 /* Renderer.swift in Sources */,
				DF98BAE82D54FCC80089D711 /* SceneDelegate.swift in Sources */,
				DF0FFB562D86C666008E2935 /* DuskinColor.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DF98BAF42D54FCC90089D711 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DF98BAFD2D54FCC90089D711 /* MatSimulatorTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DF98BAFE2D54FCC90089D711 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DF98BB092D54FCC90089D711 /* MatSimulatorUITestsLaunchTests.swift in Sources */,
				DF98BB072D54FCC90089D711 /* MatSimulatorUITests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		DF98BAFA2D54FCC90089D711 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DF98BAE12D54FCC80089D711 /* MatSimulator */;
			targetProxy = DF98BAF92D54FCC90089D711 /* PBXContainerItemProxy */;
		};
		DF98BB042D54FCC90089D711 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DF98BAE12D54FCC80089D711 /* MatSimulator */;
			targetProxy = DF98BB032D54FCC90089D711 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		DF98BAEB2D54FCC80089D711 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				DF98BAEC2D54FCC80089D711 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		DF98BAF02D54FCC90089D711 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				DF98BAF12D54FCC90089D711 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		DF98BB0A2D54FCC90089D711 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		DF98BB0B2D54FCC90089D711 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		DF98BB0D2D54FCC90089D711 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = JQKRRB55PY;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MatSimulator/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "背景画像を撮影するためにカメラへのアクセスが必要です";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "背景画像を選択するためにアルバムへのアクセスが必要です";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.clamour.matsimulator;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		DF98BB0E2D54FCC90089D711 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = JQKRRB55PY;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MatSimulator/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "背景画像を撮影するためにカメラへのアクセスが必要です";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "背景画像を選択するためにアルバムへのアクセスが必要です";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.clamour.matsimulator;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		DF98BB102D54FCC90089D711 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = K7CRQZKJ9Z;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.clamour.metal.MatSimulatorTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MatSimulator.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MatSimulator";
			};
			name = Debug;
		};
		DF98BB112D54FCC90089D711 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = K7CRQZKJ9Z;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.clamour.metal.MatSimulatorTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MatSimulator.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MatSimulator";
			};
			name = Release;
		};
		DF98BB132D54FCC90089D711 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = K7CRQZKJ9Z;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.clamour.metal.MatSimulatorUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = MatSimulator;
			};
			name = Debug;
		};
		DF98BB142D54FCC90089D711 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = K7CRQZKJ9Z;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.clamour.metal.MatSimulatorUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = MatSimulator;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		DF98BADD2D54FCC80089D711 /* Build configuration list for PBXProject "MatSimulator" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DF98BB0A2D54FCC90089D711 /* Debug */,
				DF98BB0B2D54FCC90089D711 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DF98BB0C2D54FCC90089D711 /* Build configuration list for PBXNativeTarget "MatSimulator" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DF98BB0D2D54FCC90089D711 /* Debug */,
				DF98BB0E2D54FCC90089D711 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DF98BB0F2D54FCC90089D711 /* Build configuration list for PBXNativeTarget "MatSimulatorTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DF98BB102D54FCC90089D711 /* Debug */,
				DF98BB112D54FCC90089D711 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DF98BB122D54FCC90089D711 /* Build configuration list for PBXNativeTarget "MatSimulatorUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DF98BB132D54FCC90089D711 /* Debug */,
				DF98BB142D54FCC90089D711 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = DF98BADA2D54FCC80089D711 /* Project object */;
}
